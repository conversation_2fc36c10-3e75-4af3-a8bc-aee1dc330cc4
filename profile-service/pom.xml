<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flipkart.fintech</groupId>
        <artifactId>pinaka</artifactId>
        <version>3.4.15-SM</version>
    </parent>

    <artifactId>profile-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lombok.version>1.18.30</lombok.version>
        <hystrix.javanica.version>1.5.18</hystrix.javanica.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.14.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>javax.inject</artifactId>
            <version>2.5.0-b32</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.3-jre</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
            <version>2.1.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-annotation</artifactId>
            <version>4.1.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.9</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-hibernate</artifactId>
            <version>2.1.12</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>affordability-service-clients</artifactId>
            <version>${affordability.service.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.rome</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mockito-all</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mockito-core</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>pandora-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-logger</artifactId>
            <version>${fintech.logger.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
            <version>4.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.15.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!--        TODO remove the below, and bump version to latest-->
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-cryptex-bundle</artifactId>
            <version>1.1.3-SM-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>5.4.12.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1</version>
            <scope>compile</scope>
        </dependency>
        <!--        hystrix dependencies for metrics-->
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-javanica</artifactId>
            <version>${hystrix.javanica.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.24</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.13.0</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-client</artifactId>
            <version>${pandora.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-service-client</artifactId>
            <version>${pandora.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-client</artifactId>
            <version>${pandora.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>user-data-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech.pinaka.library</groupId>
            <artifactId>pinaka-library</artifactId>
            <version>3.4.15-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo.crisys</groupId>
            <artifactId>crisys-client</artifactId>
            <version>1.0.19-JAVA-17-SM</version>
            <exclusions>
                <exclusion>
                    <groupId>jakarta.persistence</groupId>
                    <artifactId>jakarta.persistence-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-json-jackson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>resilience</artifactId>
            <version>1.0.0-SM</version>
        </dependency>
        <dependency>
            <groupId>com.supermoney.google.clients</groupId>
            <artifactId>bigtable-client</artifactId>
            <version>1.0.4-JAVA8</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>citadel-client</artifactId>
            <version>2.0.10-SM</version>
            <scope>compile</scope>
        </dependency>
      
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-pubsub</artifactId>
            <scope>compile</scope>
        </dependency>
       
        <dependency>
            <groupId>com.sumo.dataplatform</groupId>
            <artifactId>event-publisher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>winterfell-client</artifactId>
            <version>${winterfell.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>winterfell-api</artifactId>
            <version>${winterfell.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>org.projectlombok</artifactId>
                    <groupId>lombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.upi</groupId>
            <artifactId>user-api-models</artifactId>
            <version>2.17-SM</version>
            <exclusions>
                <exclusion>
                    <artifactId>org.projectlombok</artifactId>
                    <groupId>lombok</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-json-jackson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.9.8</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.supermoney.commons.pubsub.client</groupId>
            <artifactId>pub-sub-client</artifactId>
            <version>${pub.sub.version}</version>
        </dependency>
        <dependency>
            <groupId>events-commons</groupId>
            <artifactId>event-commons</artifactId>
            <version>${events.common.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.0.0</version>
            <scope>test</scope>
        </dependency>

    </dependencies>


</project>