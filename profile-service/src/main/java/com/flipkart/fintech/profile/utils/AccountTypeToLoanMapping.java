package com.flipkart.fintech.profile.utils;

import java.util.HashMap;
import java.util.Map;

public class AccountTypeToLoanMapping {
    public static Map<String, String> loanMap = new HashMap<>();

    static {
        loanMap.put("00","OTHERS");
        loanMap.put("01", "AUTO LOAN");
        loanMap.put("02", "HOUSING LOAN");
        loanMap.put("03", "PROPERTY LOAN");
        loanMap.put("04", "LOAN AGAINST SHARES/SECURITIES");
        loanMap.put("05", "PERSONAL LOAN");
        loanMap.put("06", "CONSUMER LOAN");
        loanMap.put("07", "GOLD LOAN");
        loanMap.put("08", "EDUCATIONAL LOAN");
        loanMap.put("09", "LOAN TO PROFESSIONAL");
        loanMap.put("10", "CREDIT CARD");
        loanMap.put("11", "LEASING");
        loanMap.put("12", "OVERDRAFT");
        loanMap.put("13", "TWO-WHEELER LOAN");
        loanMap.put("14", "NON-FUNDED CREDIT FACILITY");
        loanMap.put("15", "LOAN AGAINST BANK DEPOSITS");
        loanMap.put("16", "FLEET CARD");
        loanMap.put("17", "Commercial Vehicle Loan");
        loanMap.put("18", "Telco – Wireless");
        loanMap.put("19", "Telco – Broadband");
        loanMap.put("20", "Telco – Landline");
        loanMap.put("23", "GECL Secured");
        loanMap.put("24", "GECL Unsecured");
        loanMap.put("31", "Secured Credit Card");
        loanMap.put("32", "Used Car Loan");
        loanMap.put("33", "Construction Equipment Loan");
        loanMap.put("34", "Tractor Loan");
        loanMap.put("35", "Corporate Credit Card");
        loanMap.put("36", "Kisan Credit Card");
        loanMap.put("37", "Loan on Credit Card");
        loanMap.put("38", "Prime Minister Jaan Dhan Yojana - Overdraft");
        loanMap.put("39", "Mudra Loans – Shishu / Kishor / Tarun");
        loanMap.put("40", "Microfinance – Business Loan");
        loanMap.put("41", "Microfinance – Personal Loan");
        loanMap.put("42", "Microfinance – Housing Loan");
        loanMap.put("43", "Microfinance – Others");
        loanMap.put("44", "Pradhan Mantri Awas Yojana - Credit Link Subsidy Scheme MAY CLSS");
        loanMap.put("45", "P2P Personal Loan");
        loanMap.put("46", "P2P Auto Loan");
        loanMap.put("47", "P2P Education Loan");
        loanMap.put("50", "Business Loan - Secured");
        loanMap.put("51", "BUSINESS LOAN – GENERAL");
        loanMap.put("52", "BUSINESS LOAN –PRIORITY SECTOR – SMALL BUSINESS");
        loanMap.put("53", "BUSINESS LOAN –PRIORITY SECTOR – AGRICULTURE");
        loanMap.put("54", "BUSINESS LOAN –PRIORITY SECTOR – OTHERS");
        loanMap.put("55", "BUSINESS NON-FUNDED CREDIT FACILITY – GENERAL");
        loanMap.put("56", "BUSINESS NON-FUNDED CREDIT FACILITY – PRIORITY SECTOR – SMALL BUSINESS");
        loanMap.put("57", "BUSINESS NON-FUNDED CREDIT FACILITY – PRIORITY SECTOR – AGRICULTURE");
        loanMap.put("58", "BUSINESS NON-FUNDED CREDIT FACILITY – PRIORITY SECTOR – OTHERS");
        loanMap.put("59", "BUSINESS LOANS AGAINST BANK DEPOSITS");
        loanMap.put("60", "Staff Loan");
        loanMap.put("61", "Business Loan - Unsecured");
        loanMap.put("69", "Short Term Personal Loan [Unsecured]");
        loanMap.put("70", "Priority Sector Gold Loan [Secured]");
        loanMap.put("71", "Temporary Overdraft [Unsecured]");
        loanMap.put("0","OTHERS");
        loanMap.put("1", "AUTO LOAN");
        loanMap.put("2", "HOUSING LOAN");
        loanMap.put("3", "PROPERTY LOAN");
        loanMap.put("4", "LOAN AGAINST SHARES/SECURITIES");
        loanMap.put("5", "PERSONAL LOAN");
        loanMap.put("6", "CONSUMER LOAN");
        loanMap.put("7", "GOLD LOAN");
        loanMap.put("8", "EDUCATIONAL LOAN");
        loanMap.put("9", "LOAN TO PROFESSIONAL");
    }
}
