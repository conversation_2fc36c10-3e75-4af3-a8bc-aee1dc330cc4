package com.flipkart.ams;

import static com.supermoney.ams.bridge.utils.PinakaConstants.SANDBOX_LENDERS_SET;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

public class ApplicationTypeUtils {
  /**
   * ApplicationType:
   * 1. AXIS = PERSONAL_LOAN
   * 2. IDFC = PERSONAL_LOAN_IDFC
   * 3. MONEYVIEW = PERSONAL_LOAN_MONEYVIEW
   * 4. FIBE = PERSONAL_LOAN_FIBE
   */

  private static List<String> PERSONAL_LOANS_APPLICATION_TYPES;
  private static List<String> LEAD_APPLICATION_TYPES;

  static {
    PERSONAL_LOANS_APPLICATION_TYPES = Lists.newArrayList("PERSONAL_LOAN", "PERSONAL_LOAN_IDFC", "PERSONAL_LOAN_HDFC", "PERSONAL_LOAN_RUPEEK", "PERSONAL_LOAN_MPOCKKET");
    LEAD_APPLICATION_TYPES = Lists.newArrayList(Arrays.toString(com.flipkart.ams.LEAD_APPLICATION_TYPES.values()));
    for (String lender : SANDBOX_LENDERS_SET) {
      String applicationType = "PERSONAL_LOAN_" + lender;
      PERSONAL_LOANS_APPLICATION_TYPES.add(applicationType);
    }
  }

  public static String getApplicationType(ProductType productType, String lender) {
    if(Lender.AXIS.name().equals(lender)) {
      return String.valueOf(productType);
    }
    return String.format("%s_%s", String.valueOf(productType), lender);
  }


  public static List<String> getApplicationTypes(ProductType productType) {
    if (productType == ProductType.PERSONAL_LOAN) {
      return PERSONAL_LOANS_APPLICATION_TYPES;
    }
    if (productType == ProductType.LEAD) {
      return LEAD_APPLICATION_TYPES;
    }
    throw new IllegalArgumentException("ApplicationTypes not defined for the productType");
  }

  public static String getLender(ApplicationDataResponse applicationDataResponse){
    return (String) applicationDataResponse.getApplicationData().get("financial_provider");
  }

  public static boolean isSameLender(String applicationType, Lender lender) {
    String lenderApplicationType = getApplicationType(ProductType.PERSONAL_LOAN, lender.name());
    return applicationType.equals(lenderApplicationType);
  }
}
