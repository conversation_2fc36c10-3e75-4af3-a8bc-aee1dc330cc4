package com.flipkart.ams;

import com.flipkart.fintech.citadel.api.models.ActiveApplicationResponse;
import com.flipkart.fintech.citadel.api.models.ActiveApplicationsResponse;
import com.flipkart.fintech.citadel.api.models.ApplicationData;
import com.flipkart.fintech.citadel.api.models.ApplicationStateResponse;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.AlmDiscardRequest;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.request.ApplicationResponse;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.UpdatePartnerStateRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.ImplementedBy;

import java.util.List;
import java.util.Optional;

@ImplementedBy(ApplicationServiceImpl.class)
public interface ApplicationService {

    ApplicationResponse resumeApplication(MerchantUser merchantUser, String applicationId, ResumeApplicationRequest resumeApplicationRequest) throws PinakaException;

    @Deprecated
    ApplicationDataResponse fetchApplicationData(String applicationId, MerchantUser merchantUser) throws PinakaException;

    ApplicationDataResponse fetchApplicationData(MerchantUser merchantUser, String applicationId) throws PinakaException;

    Optional<ApplicationDataResponse> findActiveApplication(MerchantUser merchantUser,
        ProductType productType) throws PinakaException;

    ApplicationDataResponse fetchActiveApplicationData(MerchantUser merchantUser,
        String applicationId) throws PinakaException;

    void updatePartnerState(
				UpdatePartnerStateRequest updatePartnerStateRequest) throws PinakaException;

		Optional<ApplicationDataResponse> findLatestActiveApplicationV2(MerchantUser merchantUser, ProductType productType) throws PinakaException;

    Optional<ApplicationDataResponse> getLatestActiveApplicationFromList(List<ApplicationData> applications, MerchantUser merchantUser, ProductType productType) throws PinakaException;

    ActiveApplicationsResponse findActiveApplicationsForProductTypeV2(MerchantUser merchantUser, ProductType productType) throws PinakaException;

    boolean almDiscard(AlmDiscardRequest applicationId) throws PinakaException;

    ActiveApplicationResponse getActiveApplications(MerchantUser merchantUser, String applicationType) throws PinakaException;

    ApplicationStateResponse getDiscardedApplications(MerchantUser merchantUser) throws PinakaException;

    Boolean discardApplicationAndLeads(MerchantUser user, String applicationId);

    Boolean discardApplication(MerchantUser user, String applicationId);
}
