{"batches": [{"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "e0883bfcaa21a4829f29a041fa05cadf15ac63f56aca4b09e68c49e70acc58c1"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-aapi-prod-585cb4d8f-w774m"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java -Xms6144m -Xmx6144m -XX:+UseG1GC -verbose:gc -Xloggc:/usr/local/flipkart/advanz-api/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Dcom.sun.management.jmxremote.port=3335 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Dlog4j.configurationFile=/usr/local/flipkart/advanz-api/resources/external/log4j.xml -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -Dlog4j2.formatMsgNoLookups=true com.flipkart.poseidon.Poseidon /usr/local/flipkart/advanz-api/resources/external/bootstrap.xml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java"}}, {"key": "process.pid", "value": {"intValue": 15}}, {"key": "process.runtime.description", "value": {"stringValue": "Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 25.202-b08"}}, {"key": "process.runtime.name", "value": {"stringValue": "Java(TM) SE Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_202-b08"}}, {"key": "service.instance.id", "value": {"stringValue": "c64194a1-722b-4c43-9f60-8bc14066071a"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pl-aapi"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f62a8f00f9d5414d", "parentSpanId": "0000000000000000", "traceState": "", "name": "POST /*", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979314000000, "endTimeUnixNano": 1748858980479390700, "attributes": [{"key": "user_agent.original", "value": {"stringValue": "Mozilla/5.0 (Linux; Android 14; RMX3997 Build/UP1A.231005.007) FKUA/Retail/2240100/Android/Mobile (realme/RMX3997/59b639ec7312358912a13e8f23eee069)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "network.peer.port", "value": {"intValue": 37184}}, {"key": "url.scheme", "value": {"stringValue": "https"}}, {"key": "thread.name", "value": {"stringValue": "qtp552526972-245"}}, {"key": "url.path", "value": {"stringValue": "/api/fpg/1/action/view"}}, {"key": "server.address", "value": {"stringValue": "127.0.0.1"}}, {"key": "client.address", "value": {"stringValue": "**************"}}, {"key": "network.peer.address", "value": {"stringValue": "127.0.0.1"}}, {"key": "http.route", "value": {"stringValue": "/*"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 245}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "e0883bfcaa21a4829f29a041fa05cadf15ac63f56aca4b09e68c49e70acc58c1"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-aapi-prod-585cb4d8f-w774m"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java -Xms6144m -Xmx6144m -XX:+UseG1GC -verbose:gc -Xloggc:/usr/local/flipkart/advanz-api/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Dcom.sun.management.jmxremote.port=3335 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Dlog4j.configurationFile=/usr/local/flipkart/advanz-api/resources/external/log4j.xml -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -Dlog4j2.formatMsgNoLookups=true com.flipkart.poseidon.Poseidon /usr/local/flipkart/advanz-api/resources/external/bootstrap.xml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java"}}, {"key": "process.pid", "value": {"intValue": 15}}, {"key": "process.runtime.description", "value": {"stringValue": "Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 25.202-b08"}}, {"key": "process.runtime.name", "value": {"stringValue": "Java(TM) SE Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_202-b08"}}, {"key": "service.instance.id", "value": {"stringValue": "c64194a1-722b-4c43-9f60-8bc14066071a"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pl-aapi"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "9fc7a7fc427c9ff7", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "romeServiceHttp.romeServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979314842000, "endTimeUnixNano": 1748858979350882800, "attributes": [{"key": "thread.id", "value": {"intValue": 920}}, {"key": "thread.name", "value": {"stringValue": "hystrix-romeServiceHttpRequest-195"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "12d89af34c68623d", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979351167000, "endTimeUnixNano": 1748858979368390700, "attributes": [{"key": "thread.id", "value": {"intValue": 706}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-97"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 2, "message": ""}, "events": [{"timeUnixNano": 1748858979368382500, "attributes": [{"key": "exception.type", "value": {"stringValue": "java.lang.RuntimeException"}}, {"key": "exception.message", "value": {"stringValue": "500 {\"error_code\":500,\"error_message\":\"No Merchant Account Mapping Exist\"}\n(URI = /loginservice/user/v1/merchant/ACC578AD722D85C436E9948F1B5B6B5565B?merchant=FLIPKART&phoneNumberFormat=E164, METHOD = GET)"}}, {"key": "exception.stacktrace", "value": {"stringValue": "java.lang.RuntimeException: 500 {\"error_code\":500,\"error_message\":\"No Merchant Account Mapping Exist\"}\n(URI = /loginservice/user/v1/merchant/ACC578AD722D85C436E9948F1B5B6B5565B?merchant=FLIPKART&phoneNumberFormat=E164, METHOD = GET)\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.handleException(SinglePoolHttpTaskHandler.java:331)\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.execute(SinglePoolHttpTaskHandler.java:157)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.run(TaskHandlerExecutor.java:250)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.run(TaskHandlerExecutor.java:48)\n\tat com.netflix.hystrix.HystrixCommand$2.call(HystrixCommand.java:301)\n\tat com.netflix.hystrix.HystrixCommand$2.call(HystrixCommand.java:297)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:46)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:45)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:15)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:51)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OperatorSubscribeOn$1.call(OperatorSubscribeOn.java:94)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:56)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:47)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction.call(HystrixContexSchedulerAction.java:69)\n\tat rx.internal.schedulers.ScheduledAction.run(ScheduledAction.java:55)\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\n\tat java.util.concurrent.FutureTask.run(FutureTask.java:266)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\nCaused by: com.flipkart.aapi.serviceclients.login.v5.LoginServiceFailureException: 500 {\"error_code\":500,\"error_message\":\"No Merchant Account Mapping Exist\"}\n\n\tat sun.reflect.GeneratedConstructorAccessor249.newInstance(Unknown Source)\n\tat sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.lang.reflect.Constructor.newInstance(Constructor.java:423)\n\tat com.flipkart.poseidon.serviceclients.ServiceResponseDecoder.decode(ServiceResponseDecoder.java:145)\n\tat com.flipkart.poseidon.serviceclients.ServiceResponseDecoder.decode(ServiceResponseDecoder.java:38)\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.execute(SinglePoolHttpTaskHandler.java:154)\n\t... 33 more\n"}}], "droppedAttributesCount": 0, "name": "exception"}]}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c2612363b6eb6326", "parentSpanId": "12d89af34c68623d", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.fallback", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979368477700, "endTimeUnixNano": 1748858979375562000, "attributes": [{"key": "thread.id", "value": {"intValue": 706}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-97"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 2, "message": ""}, "events": [{"timeUnixNano": 1748858979375550200, "attributes": [{"key": "exception.type", "value": {"stringValue": "java.lang.UnsupportedOperationException"}}, {"key": "exception.message", "value": {"stringValue": "No fallback available."}}, {"key": "exception.stacktrace", "value": {"stringValue": "java.lang.UnsupportedOperationException: No fallback available.\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.getFallBack(SinglePoolHttpTaskHandler.java:231)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.getFallback(TaskHandlerExecutor.java:293)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.getFallback(TaskHandlerExecutor.java:48)\n\tat com.netflix.hystrix.HystrixCommand$3.call(HystrixCommand.java:321)\n\tat com.netflix.hystrix.HystrixCommand$3.call(HystrixCommand.java:317)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:46)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:45)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:15)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OperatorOnErrorResumeNextViaFunction$4.onError(OperatorOnErrorResumeNextViaFunction.java:142)\n\tat rx.internal.operators.OnSubscribeDoOnEach$DoOnEachSubscriber.onError(OnSubscribeDoOnEach.java:87)\n\tat rx.internal.operators.OnSubscribeDoOnEach$DoOnEachSubscriber.onError(OnSubscribeDoOnEach.java:87)\n\tat com.netflix.hystrix.AbstractCommand$HystrixObservableTimeoutOperator$3.onError(AbstractCommand.java:1176)\n\tat rx.internal.operators.OperatorSubscribeOn$1$1.onError(OperatorSubscribeOn.java:59)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat rx.internal.operators.OnSubscribeDoOnEach$DoOnEachSubscriber.onError(OnSubscribeDoOnEach.java:87)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat com.netflix.hystrix.AbstractCommand$DeprecatedOnRunHookApplication$1.onError(AbstractCommand.java:1413)\n\tat com.netflix.hystrix.AbstractCommand$ExecutionHookApplication$1.onError(AbstractCommand.java:1344)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedSubscriber.onError(TracedSubscriber.java:81)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat rx.internal.operators.OnSubscribeThrow.call(OnSubscribeThrow.java:44)\n\tat rx.internal.operators.OnSubscribeThrow.call(OnSubscribeThrow.java:28)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:51)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:45)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:15)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:51)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OperatorSubscribeOn$1.call(OperatorSubscribeOn.java:94)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:56)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:47)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction.call(HystrixContexSchedulerAction.java:69)\n\tat rx.internal.schedulers.ScheduledAction.run(ScheduledAction.java:55)\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\n\tat java.util.concurrent.FutureTask.run(FutureTask.java:266)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n"}}], "droppedAttributesCount": 0, "name": "exception"}]}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "842191be928a724c", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "OAuthPoolHttpTaskHandler.userServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979379199700, "endTimeUnixNano": 1748858979399088600, "attributes": [{"key": "thread.id", "value": {"intValue": 1355}}, {"key": "thread.name", "value": {"stringValue": "hystrix-userServiceHttpRequest-100"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c9cfba0586250cf6", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979399460900, "endTimeUnixNano": 1748858979473071000, "attributes": [{"key": "thread.id", "value": {"intValue": 706}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-97"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "ae37358707795b32", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "pinakaServiceHttp.pinakaServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979473460500, "endTimeUnixNano": 1748858979547243500, "attributes": [{"key": "thread.id", "value": {"intValue": 935}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaServiceHttpRequest-10"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "70998fcb37199a7e", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979547444500, "endTimeUnixNano": 1748858979595269000, "attributes": [{"key": "thread.id", "value": {"intValue": 708}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-98"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "23a0ddf757611fa6", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "PINAKA_API_POOL.STATUS.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979596948700, "endTimeUnixNano": 1748858980107268000, "attributes": [{"key": "thread.id", "value": {"intValue": 1199}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-145"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f91650c067799f44", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "pageServiceClientViewHttp.pageServiceClientViewHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980111948300, "endTimeUnixNano": 1748858980141727000, "attributes": [{"key": "thread.id", "value": {"intValue": 1042}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pageServiceClientViewHttpRequest-99"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "2e335e9c2ecf554a", "parentSpanId": "9fc7a7fc427c9ff7", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979314863900, "endTimeUnixNano": 1748858979350786600, "attributes": [{"key": "server.address", "value": {"stringValue": "************"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 920}}, {"key": "thread.name", "value": {"stringValue": "hystrix-romeServiceHttpRequest-195"}}, {"key": "url.full", "value": {"stringValue": "http://************/2/session/validate"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "6d0f593c8b0a920d", "parentSpanId": "12d89af34c68623d", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979351186000, "endTimeUnixNano": 1748858979360012000, "attributes": [{"key": "error.type", "value": {"stringValue": "500"}}, {"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 706}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-97"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v1/merchant/ACC578AD722D85C436E9948F1B5B6B5565B?merchant=FLIPKART&phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 500}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 2, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "cc4a9dbee3379786", "parentSpanId": "842191be928a724c", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979379234800, "endTimeUnixNano": 1748858979398979000, "attributes": [{"key": "server.address", "value": {"stringValue": "***********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 1355}}, {"key": "thread.name", "value": {"stringValue": "hystrix-userServiceHttpRequest-100"}}, {"key": "url.full", "value": {"stringValue": "http://***********/userservice/v0.1/customer/ACC578AD722D85C436E9948F1B5B6B5565B"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f7a1e239503a0321", "parentSpanId": "c9cfba0586250cf6", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979399480600, "endTimeUnixNano": 1748858979472975000, "attributes": [{"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 706}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-97"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v2?phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "3432dde5d7e01710", "parentSpanId": "ae37358707795b32", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979473488600, "endTimeUnixNano": 1748858979547100000, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 935}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaServiceHttpRequest-10"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/migrate"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "900177c5d3d1531a", "parentSpanId": "70998fcb37199a7e", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979547464400, "endTimeUnixNano": 1748858979595175000, "attributes": [{"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 708}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-98"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v2?phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "9c16fc59cd2d4d5e", "parentSpanId": "f91650c067799f44", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980111969000, "endTimeUnixNano": 1748858980141479700, "attributes": [{"key": "server.address", "value": {"stringValue": "***********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 1042}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pageServiceClientViewHttpRequest-99"}}, {"key": "url.full", "value": {"stringValue": "http://***********/pages/v1/page/perso-dynam-311d7/view/client/mobile_api"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "071fa3846dad1c46", "parentSpanId": "23a0ddf757611fa6", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979597097500, "endTimeUnixNano": 1748858979623872300, "attributes": [{"key": "server.address", "value": {"stringValue": "**********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "thread.id", "value": {"intValue": 1199}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-145"}}, {"key": "url.full", "value": {"stringValue": "http://**********:80/v1/user-profile/ACCOUNTID/ACC578AD722D85C436E9948F1B5B6B5565B/segmentsWithSegmentGroups?channel=NEO&sc=FK"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "059f67083f7e60f9", "parentSpanId": "23a0ddf757611fa6", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979644957700, "endTimeUnixNano": 1748858980107003600, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 1199}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-145"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "937b4f63d1104802", "parentSpanId": "f62a8f00f9d5414d", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980166706700, "endTimeUnixNano": 1748858980476517400, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 873}}, {"key": "thread.name", "value": {"stringValue": "data-aggregator-thread-47"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.hystrix-1.4", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "c2d2f08dbb1e0b7996f5f778bb3d245368c56722f67779c0b3a358f3cf6aab05"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-pinaka-prod-6c976d8d5-kfqsm"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3584m -Xmx5734m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-pinaka-preprod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G com.flipkart.fintech.pinaka.service.application.PinakaApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "9650b515-e4d2-4368-97f4-63b5aee2aa8c"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pinaka"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "360716fb6d9d4e38", "parentSpanId": "3432dde5d7e01710", "traceState": "", "name": "POST /pinaka/6/pl/migrate", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979476000000, "endTimeUnixNano": 1748858979547138000, "attributes": [{"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 29652}}, {"key": "thread.name", "value": {"stringValue": "dw-29652"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 56580}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/migrate"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/migrate"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "3ebf385506aad0c8", "parentSpanId": "059f67083f7e60f9", "traceState": "", "name": "POST /pinaka/6/pl/apply-now", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979646000000, "endTimeUnixNano": 1748858980088424000, "attributes": [{"key": "network.peer.port", "value": {"intValue": 42750}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/apply-now"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/apply-now"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_202)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "16314ea223932d57", "parentSpanId": "937b4f63d1104802", "traceState": "", "name": "POST /pinaka/6/pl/fetch-bulk-data-v2", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858980168000000, "endTimeUnixNano": 1748858980476648400, "attributes": [{"key": "network.peer.port", "value": {"intValue": 42750}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_202)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "e522a83816f12764", "parentSpanId": "360716fb6d9d4e38", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979499592200, "endTimeUnixNano": 1748858979546983000, "attributes": [{"key": "thread.id", "value": {"intValue": 29652}}, {"key": "thread.name", "value": {"stringValue": "dw-29652 - POST /pinaka/6/pl/migrate"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4e1706d0150263a1", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979987728600, "endTimeUnixNano": 1748858980036245800, "attributes": [{"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f40aa8a063496025", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980036250600, "endTimeUnixNano": 1748858980088131300, "attributes": [{"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "8bf28ba6afb7c22f", "parentSpanId": "16314ea223932d57", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980202301400, "endTimeUnixNano": 1748858980226697500, "attributes": [{"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f164575311593a83", "parentSpanId": "16314ea223932d57", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980226737400, "endTimeUnixNano": 1748858980251497700, "attributes": [{"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "7cd3ac44b78646ad", "parentSpanId": "16314ea223932d57", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980427325200, "endTimeUnixNano": 1748858980476157700, "attributes": [{"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "cdc9d519385081cb", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979697600500, "endTimeUnixNano": 1748858979706081800, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "e8be20bd18b19e1b", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979706686000, "endTimeUnixNano": 1748858979715094000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "938a9d9d4ca16c24", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979867304400, "endTimeUnixNano": 1748858979977579800, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/create"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "5ebd132b306f9892", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979977766000, "endTimeUnixNano": 1748858979987220000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "260ae17516b3478d", "parentSpanId": "16314ea223932d57", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980193039400, "endTimeUnixNano": 1748858980201904600, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "ac24ad6a140dfb0e", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979715372800, "endTimeUnixNano": 1748858979744859600, "attributes": [{"key": "url.full", "value": {"stringValue": "http://************:80/dexter/1/user-profile/fetch"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "8547a695237b1f6a", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979745261600, "endTimeUnixNano": 1748858979773786600, "attributes": [{"key": "url.full", "value": {"stringValue": "http://************:80/dexter/1/user-profile/fetch"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "8609839cf40827b7", "parentSpanId": "3ebf385506aad0c8", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979774088400, "endTimeUnixNano": 1748858979867126300, "attributes": [{"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/2/profile/basic-profile?smUserId=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "thread.id", "value": {"intValue": 29472}}, {"key": "thread.name", "value": {"stringValue": "dw-29472 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 204}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "308eadf1be8e2967", "parentSpanId": "16314ea223932d57", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980251660500, "endTimeUnixNano": 1748858980256788500, "attributes": [{"key": "url.full", "value": {"stringValue": "http://usersvc-kaas-v1.sm-user-service-prod.fkcloud.in/userservice/v2/customer/SMA2FA7151022144A87905B6DCE31AF155D?piiTextType=PLAINTEXT"}}, {"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "usersvc-kaas-v1.sm-user-service-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "2d12cd88977ea98a", "parentSpanId": "16314ea223932d57", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980257023700, "endTimeUnixNano": 1748858980426785800, "attributes": [{"key": "url.full", "value": {"stringValue": "http://sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in/sm/upi/user-svc/v1/upi-user/search"}}, {"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "986d956640eb5f4c", "parentSpanId": "8609839cf40827b7", "traceState": "", "name": "GET /pinaka/2/profile/basic-profile", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979775000000, "endTimeUnixNano": 1748858979866981400, "attributes": [{"key": "network.peer.port", "value": {"intValue": 34324}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/2/profile/basic-profile"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/2/profile/basic-profile"}}, {"key": "url.query", "value": {"stringValue": "smUserId=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 2479}}, {"key": "thread.name", "value": {"stringValue": "dw-2479"}}, {"key": "http.response.status_code", "value": {"intValue": 204}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_322)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "77afa2a89e6b8fe3", "parentSpanId": "986d956640eb5f4c", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979798360800, "endTimeUnixNano": 1748858979821344000, "attributes": [{"key": "thread.id", "value": {"intValue": 2479}}, {"key": "thread.name", "value": {"stringValue": "dw-2479 - GET /pinaka/2/profile/basic-profile?smUserId=SMA2FA7151022144A87905B6DCE31AF155D"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f067d25f359c8f6d", "parentSpanId": "986d956640eb5f4c", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979821422800, "endTimeUnixNano": 1748858979866799900, "attributes": [{"key": "thread.id", "value": {"intValue": 2479}}, {"key": "thread.name", "value": {"stringValue": "dw-2479 - GET /pinaka/2/profile/basic-profile?smUserId=SMA2FA7151022144A87905B6DCE31AF155D"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "aa3fa212c63ae348", "parentSpanId": "77afa2a89e6b8fe3", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979798419700, "endTimeUnixNano": 1748858979821315600, "attributes": [{"key": "thread.id", "value": {"intValue": 2479}}, {"key": "thread.name", "value": {"stringValue": "dw-2479 - GET /pinaka/2/profile/basic-profile?smUserId=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.smUserId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.sm_user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c58f61112806325b", "parentSpanId": "8bf28ba6afb7c22f", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980202350600, "endTimeUnixNano": 1748858980226673700, "attributes": [{"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.smUserId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.sm_user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "0beec30e0a268804", "parentSpanId": "f164575311593a83", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980226789600, "endTimeUnixNano": 1748858980251484000, "attributes": [{"key": "thread.id", "value": {"intValue": 29651}}, {"key": "thread.name", "value": {"stringValue": "dw-29651 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.userId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "b139b6cf23467b0a32c94f3d2f4e0cbb124eaa2d440e00ffd4275e39524549e0"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-winterfell-87758d947-gk88b"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3072m -Xmx4915m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-winterfell-prod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties com.flipkart.fintech.winterfell.application.WinterFellApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "58fbf768-6ce0-4e0a-b667-86ab873174d5"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-winterfell"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "2346887999a49949", "parentSpanId": "cdc9d519385081cb", "traceState": "", "name": "GET /fintech-winterfell/1/application/activeApplicationsResponse", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979698000000, "endTimeUnixNano": 1748858979705556200, "attributes": [{"key": "thread.id", "value": {"intValue": 117281}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-117281"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 58784}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "63d2310d16e16545", "parentSpanId": "2346887999a49949", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979698822700, "endTimeUnixNano": 1748858979705034800, "attributes": [{"key": "thread.id", "value": {"intValue": 117281}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "thread.name", "value": {"stringValue": "dw-117281 - GET /fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "415d64c5634975f1", "parentSpanId": "e8be20bd18b19e1b", "traceState": "", "name": "GET /fintech-winterfell/1/application/activeApplicationsResponse", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979708000000, "endTimeUnixNano": 1748858979715080400, "attributes": [{"key": "thread.id", "value": {"intValue": 108134}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-108134"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 44288}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "b2379cf2359680f7", "parentSpanId": "938a9d9d4ca16c24", "traceState": "", "name": "POST /fintech-winterfell/1/application/create", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979868000000, "endTimeUnixNano": 1748858979976889300, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 44288}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/create"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/create"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "bdb7c79935a0ea5a", "parentSpanId": "415d64c5634975f1", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979708600000, "endTimeUnixNano": 1748858979714553300, "attributes": [{"key": "thread.id", "value": {"intValue": 108134}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}, {"key": "thread.name", "value": {"stringValue": "dw-108134 - GET /fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "813d83a653c44e2d", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979868480000, "endTimeUnixNano": 1748858979883913500, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/create"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "6225a31b311fb9ec", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "PUT", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979889178000, "endTimeUnixNano": 1748858979901909200, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "1406100a7788435d", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "PUT", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979902296300, "endTimeUnixNano": 1748858979913907700, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "537938d250665d83", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979885090300, "endTimeUnixNano": 1748858979886317300, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select distinct RES.* , P.KEY_ as ProcessDefini<PERSON><PERSON><PERSON>, P.ID_ as ProcessDefinitionId, P.NAME_ as ProcessDefinitionName, P.VERSION_ as ProcessDefinitionVersion, P.DEPLOYMENT_ID_ as DeploymentId from ACT_RU_EXECUTION RES inner join ACT_RE_PROCDEF P on RES.PROC_DEF_ID_ = P.ID_ WHERE RES.PARENT_ID_ is null and RES.BUSINESS_KEY_ = ? order by RES.ID_ asc"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "ccf5016b07ab70f7", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "SELECT sm_winterfell_prod.ACT_RE_PROCDEF", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979887858000, "endTimeUnixNano": 1748858979888776200, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RE_PROCDEF"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from ACT_RE_PROCDEF where ID_ = ?"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c904c80ec808b908", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "SELECT sm_winterfell_prod.ACT_RU_ENTITYLINK", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979914272000, "endTimeUnixNano": 1748858979915444700, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_ENTITYLINK"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from ACT_RU_ENTITYLINK where REF_SCOPE_ID_ = ? and REF_SCOPE_TYPE_ = ? and LINK_TYPE_ = ?"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c219dedbcf17272a", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_EXECUTION", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979915970000, "endTimeUnixNano": 1748858979921473000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_EXECUTION"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_EXECUTION (ID_, REV_, PROC_INST_ID_, BUSINESS_KEY_, PROC_DEF_ID_, ACT_ID_, IS_ACTIVE_, IS_CONCURRENT_, IS_SCOPE_,IS_EVENT_SCOPE_, IS_MI_ROOT_, PARENT_ID_, SUPER_EXEC_, ROOT_PROC_INST_ID_, SUSPENSION_STATE_, TENANT_ID_, NAME_, START_ACT_ID_, START_TIME_, START_USER_ID_, IS_COUNT_ENABLED_, EVT_SUBSCR_COUNT_, TASK_COUNT_, JOB_COUNT_, TIMER_JOB_COUNT_, SUSP_JOB_COUNT_, DEADLETTER_JOB_COUNT_, VAR_COUNT_, ID_LINK_COUNT_, CALLBACK_ID_, CALLBACK_TYPE_, REFERENCE_ID_, REFERENCE_TYPE_, PROPAGATED_STAGE_INST_ID_) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "83aabc30b3614621", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_ACTINST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979921773300, "endTimeUnixNano": 1748858979925809000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_ACTINST"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_ACTINST ( ID_, REV_, PROC_DEF_ID_, PROC_INST_ID_, EXECUTION_ID_, ACT_ID_, TASK_ID_, CALL_PROC_INST_ID_, ACT_NAME_, ACT_TYPE_, ASSIGNEE_, START_TIME_, END_TIME_, DURATION_, DELETE_REASON_, TENANT_ID_ ) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) , (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) , (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "7819b762f89a4857", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_TASK", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979925929500, "endTimeUnixNano": 1748858979932229000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_TASK"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_TASK (ID_, REV_, NAME_, PARENT_TASK_ID_, DESCRIPTION_, PRIORITY_, CREATE_TIME_, OWNER_, ASSIGNEE_, DELEGATION_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, TASK_DEF_ID_, SCOPE_ID_, SUB_SCOPE_ID_, SCOPE_TYPE_, SCOPE_DEFINITION_ID_, PROPAGATED_STAGE_INST_ID_, TASK_DEF_KEY_, DUE_DATE_, CATEGORY_, SUSPENSION_STATE_, TENANT_ID_, FORM_KEY_, CLAIM_TIME_, IS_COUNT_ENABLED_, VAR_COUNT_, ID_LINK_COUNT_, SUB_TASK_COUNT_) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "42874ceb1f911388", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_ENTITYLINK", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979932294100, "endTimeUnixNano": 1748858979935209000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_ENTITYLINK"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_ENTITYLINK (ID_, REV_, CREATE_TIME_, LINK_TYPE_, SCOPE_ID_, SCOPE_TYPE_, SCOPE_DEFINITION_ID_, REF_SCOPE_ID_, REF_SCOPE_TYPE_, REF_SCOPE_DEFINITION_ID_, HIERARCHY_TYPE_) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4ec5dbb1648d18fd", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_VARIABLE", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979936106800, "endTimeUnixNano": 1748858979963713000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_VARIABLE"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "INSERT INTO ACT_RU_VARIABLE (ID_, REV_, TYPE_, NAME_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, SCOPE_ID_, SUB_SCOPE_ID_, SCOPE_TYPE_, BYTEARRAY_ID_, DOUBLE_, LONG_ , TEXT_, TEXT2_) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "346cf06071492c65", "parentSpanId": "b2379cf2359680f7", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979974559200, "endTimeUnixNano": 1748858979975733800, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "950acadd2e2cb32c", "parentSpanId": "5ebd132b306f9892", "traceState": "", "name": "GET /fintech-winterfell/1/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979978000000, "endTimeUnixNano": 1748858979986744000, "attributes": [{"key": "thread.id", "value": {"intValue": 117278}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-117278"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 58784}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "3c50373a452d7bcb", "parentSpanId": "260ae17516b3478d", "traceState": "", "name": "GET /fintech-winterfell/1/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858980193000000, "endTimeUnixNano": 1748858980201092000, "attributes": [{"key": "thread.id", "value": {"intValue": 117278}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-117278"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 58784}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "d2409efd2ebf8dc4", "parentSpanId": "950acadd2e2cb32c", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979978349000, "endTimeUnixNano": 1748858979983486200, "attributes": [{"key": "thread.id", "value": {"intValue": 117278}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}, {"key": "thread.name", "value": {"stringValue": "dw-117278 - GET /fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4bc55dcaf9d66615", "parentSpanId": "3c50373a452d7bcb", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980193524500, "endTimeUnixNano": 1748858980197806800, "attributes": [{"key": "thread.id", "value": {"intValue": 117278}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}, {"key": "thread.name", "value": {"stringValue": "dw-117278 - GET /fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "a7f50aaa7c59442c", "parentSpanId": "950acadd2e2cb32c", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979984629200, "endTimeUnixNano": 1748858979985580300, "attributes": [{"key": "thread.id", "value": {"intValue": 117278}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-117278 - GET /fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "689c1dfe71a5da12", "parentSpanId": "3c50373a452d7bcb", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980198880000, "endTimeUnixNano": 1748858980199958300, "attributes": [{"key": "thread.id", "value": {"intValue": 117278}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-117278 - GET /fintech-winterfell/1/application/APP2506021539398438866221813658696442668"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "aaa696e8185c5cb37d40dd8f50f479e31b3b6e506f524c0df3d9ffd57c0d0d74"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-citadel-prod-659cc7b68c-tvpwh"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3338m -Xmx5341m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-citadel-prod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G com.flipkart.fintech.citadel.service.application.CitadelApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "9ad1531c-3a3d-4628-98a7-93f1120c91d1"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-citadel"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4891721c2d33b8f0", "parentSpanId": "63d2310d16e16545", "traceState": "", "name": "GET /fintech-citadel/2/application/getActiveApplications", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979700000000, "endTimeUnixNano": 1748858979705163300, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 56280}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 58472}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-56280"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "199443366d0795a2", "parentSpanId": "813d83a653c44e2d", "traceState": "", "name": "POST /fintech-citadel/2/application/create", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979871000000, "endTimeUnixNano": 1748858979884001000, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/create"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 56280}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/create"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 53658}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-56280"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4a50cce9aa2d8b96", "parentSpanId": "d2409efd2ebf8dc4", "traceState": "", "name": "GET /fintech-citadel/2/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979980000000, "endTimeUnixNano": 1748858979984084000, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 56200}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 51080}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-56200"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "49d158cf3939cb62", "parentSpanId": "4891721c2d33b8f0", "traceState": "", "name": "SELECT Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979701484000, "endTimeUnixNano": 1748858979703494700, "attributes": [{"key": "thread.id", "value": {"intValue": 56280}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "0e323fc6d1b06a95", "parentSpanId": "49d158cf3939cb62", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979701545500, "endTimeUnixNano": 1748858979703474700, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 56280}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Application as generatedAlias0 where generatedAlias0.userId=:param0 */ select applicatio0_.id as id1_0_, applicatio0_.created_at as created_2_0_, applicatio0_.data as data3_0_, applicatio0_.financial_provider as financia4_0_, applicatio0_.lead_id as lead_id5_0_, applicatio0_.lender_state as lender_s6_0_, applicatio0_.lender_sub_state as lender_s7_0_, applicatio0_.merchant as merchant8_0_, applicatio0_.product_type as product_9_0_, applicatio0_.state as state10_0_, applicatio0_.updated_at as updated11_0_, applicatio0_.user_id as user_id12_0_, applicatio0_.workflow_id as workflo13_0_ from application applicatio0_ where applicatio0_.user_id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "a6e6bd88692be29f", "parentSpanId": "4891721c2d33b8f0", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979703598300, "endTimeUnixNano": 1748858979704816600, "attributes": [{"key": "thread.id", "value": {"intValue": 56280}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "fbf36a6e18c1cde3", "parentSpanId": "199443366d0795a2", "traceState": "", "name": "Session.saveOrUpdate com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979872347600, "endTimeUnixNano": 1748858979873385200, "attributes": [{"key": "thread.id", "value": {"intValue": 56280}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "8fdc716edb4c0d8c", "parentSpanId": "199443366d0795a2", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979873498400, "endTimeUnixNano": 1748858979883744500, "attributes": [{"key": "thread.id", "value": {"intValue": 56280}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "9a5b6e41d36d0924", "parentSpanId": "4a50cce9aa2d8b96", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979981179100, "endTimeUnixNano": 1748858979982188500, "attributes": [{"key": "thread.id", "value": {"intValue": 56200}}, {"key": "thread.name", "value": {"stringValue": "dw-56200 - GET /fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "bd6a1c1b1e25553b", "parentSpanId": "4a50cce9aa2d8b96", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979982366200, "endTimeUnixNano": 1748858979983718400, "attributes": [{"key": "thread.id", "value": {"intValue": 56200}}, {"key": "thread.name", "value": {"stringValue": "dw-56200 - GET /fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4ac5ee4062bdd47c", "parentSpanId": "bdb7c79935a0ea5a", "traceState": "", "name": "GET /fintech-citadel/2/application/getActiveApplications", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979709000000, "endTimeUnixNano": 1748858979713836500, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 33005}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 33442}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-33005"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "d1a5c54a2b2ffe2d", "parentSpanId": "4ac5ee4062bdd47c", "traceState": "", "name": "SELECT Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979710696400, "endTimeUnixNano": 1748858979711797500, "attributes": [{"key": "thread.id", "value": {"intValue": 33005}}, {"key": "thread.name", "value": {"stringValue": "dw-33005 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "98066d302e1db71b", "parentSpanId": "4ac5ee4062bdd47c", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979711886300, "endTimeUnixNano": 1748858979713473800, "attributes": [{"key": "thread.id", "value": {"intValue": 33005}}, {"key": "thread.name", "value": {"stringValue": "dw-33005 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "56dd31ac46846fdc", "parentSpanId": "d1a5c54a2b2ffe2d", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979710760700, "endTimeUnixNano": 1748858979711776300, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 33005}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Application as generatedAlias0 where ( generatedAlias0.userId=:param0 ) and ( generatedAlias0.productType=:param1 ) */ select applicatio0_.id as id1_0_, applicatio0_.created_at as created_2_0_, applicatio0_.data as data3_0_, applicatio0_.financial_provider as financia4_0_, applicatio0_.lead_id as lead_id5_0_, applicatio0_.lender_state as lender_s6_0_, applicatio0_.lender_sub_state as lender_s7_0_, applicatio0_.merchant as merchant8_0_, applicatio0_.product_type as product_9_0_, applicatio0_.state as state10_0_, applicatio0_.updated_at as updated11_0_, applicatio0_.user_id as user_id12_0_, applicatio0_.workflow_id as workflo13_0_ from application applicatio0_ where applicatio0_.user_id=? and applicatio0_.product_type=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-33005 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA2FA7151022144A87905B6DCE31AF155D&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "03dce55111f64b8e", "parentSpanId": "fbf36a6e18c1cde3", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979872376600, "endTimeUnixNano": 1748858979873368600, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 56280}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* get current state com.flipkart.fintech.citadel.service.models.db.Application */ select applicatio_.id, applicatio_.created_at as created_2_0_, applicatio_.data as data3_0_, applicatio_.financial_provider as financia4_0_, applicatio_.lead_id as lead_id5_0_, applicatio_.lender_state as lender_s6_0_, applicatio_.lender_sub_state as lender_s7_0_, applicatio_.merchant as merchant8_0_, applicatio_.product_type as product_9_0_, applicatio_.state as state10_0_, applicatio_.updated_at as updated11_0_, applicatio_.user_id as user_id12_0_, applicatio_.workflow_id as workflo13_0_ from application applicatio_ where applicatio_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "5771d8de65899330", "parentSpanId": "8fdc716edb4c0d8c", "traceState": "", "name": "INSERT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979873607000, "endTimeUnixNano": 1748858979874482200, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 56280}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* insert com.flipkart.fintech.citadel.service.models.db.Application */ insert into application (created_at, data, financial_provider, lead_id, lender_state, lender_sub_state, merchant, product_type, state, updated_at, user_id, workflow_id, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-56280 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "5d71476dc41cc993", "parentSpanId": "9a5b6e41d36d0924", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979981223400, "endTimeUnixNano": 1748858979982129700, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 56200}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-56200 - GET /fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "bff419b762c41137", "parentSpanId": "6225a31b311fb9ec", "traceState": "", "name": "PUT /fintech-citadel/2/application/{applicationId}/update-process-id", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979891000000, "endTimeUnixNano": 1748858979901877000, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}/update-process-id"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 55904}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c03a8bf57ee58efb", "parentSpanId": "1406100a7788435d", "traceState": "", "name": "PUT /fintech-citadel/2/application/{applicationId}/state-change", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858979904000000, "endTimeUnixNano": 1748858979913737000, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23440}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}/state-change"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 51780}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-23440"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "70f37eb81b6d715a", "parentSpanId": "bff419b762c41137", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979891986700, "endTimeUnixNano": 1748858979892821000, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "6bffd2a3b66614bf", "parentSpanId": "bff419b762c41137", "traceState": "", "name": "Session.saveOrUpdate com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979892827100, "endTimeUnixNano": 1748858979892829400, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "5dad88922c9ab919", "parentSpanId": "bff419b762c41137", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979892931300, "endTimeUnixNano": 1748858979901578500, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "9c6f95046e28481e", "parentSpanId": "c03a8bf57ee58efb", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979904944400, "endTimeUnixNano": 1748858979905694700, "attributes": [{"key": "thread.id", "value": {"intValue": 23440}}, {"key": "thread.name", "value": {"stringValue": "dw-23440 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "5d3c5711fb15ddbf", "parentSpanId": "c03a8bf57ee58efb", "traceState": "", "name": "Session.saveOrUpdate com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979905696800, "endTimeUnixNano": 1748858979905698800, "attributes": [{"key": "thread.id", "value": {"intValue": 23440}}, {"key": "thread.name", "value": {"stringValue": "dw-23440 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "f8eb16fc043e1c40", "parentSpanId": "c03a8bf57ee58efb", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858979905837600, "endTimeUnixNano": 1748858979913448700, "attributes": [{"key": "thread.id", "value": {"intValue": 23440}}, {"key": "thread.name", "value": {"stringValue": "dw-23440 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "c5c55cf9cc730cf0", "parentSpanId": "70f37eb81b6d715a", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979892037400, "endTimeUnixNano": 1748858979892779300, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "d91ef4e980a4e75b", "parentSpanId": "5dad88922c9ab919", "traceState": "", "name": "UPDATE sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979893032700, "endTimeUnixNano": 1748858979893730600, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* update com.flipkart.fintech.citadel.service.models.db.Application */ update application set created_at=?, data=?, financial_provider=?, lead_id=?, lender_state=?, lender_sub_state=?, merchant=?, product_type=?, state=?, updated_at=?, user_id=?, workflow_id=? where id=?"}}, {"key": "db.operation", "value": {"stringValue": "UPDATE"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "0c1a1776eb0f748a", "parentSpanId": "9c6f95046e28481e", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979904969700, "endTimeUnixNano": 1748858979905655800, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23440}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-23440 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "43d7dc595accfeac", "parentSpanId": "f8eb16fc043e1c40", "traceState": "", "name": "UPDATE sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858979905914000, "endTimeUnixNano": 1748858979906594800, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23440}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* update com.flipkart.fintech.citadel.service.models.db.Application */ update application set created_at=?, data=?, financial_provider=?, lead_id=?, lender_state=?, lender_sub_state=?, merchant=?, product_type=?, state=?, updated_at=?, user_id=?, workflow_id=? where id=?"}}, {"key": "db.operation", "value": {"stringValue": "UPDATE"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-23440 - PUT /fintech-citadel/2/application/APP2506021539398438866221813658696442668/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "341658137af20fde", "parentSpanId": "4525167c610ecf3d", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858980195711500, "endTimeUnixNano": 1748858980196487400, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - GET /fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "6a65ff50818aea2d", "parentSpanId": "4bc55dcaf9d66615", "traceState": "", "name": "GET /fintech-citadel/2/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858980195000000, "endTimeUnixNano": 1748858980197696000, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 55904}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4525167c610ecf3d", "parentSpanId": "6a65ff50818aea2d", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980195672300, "endTimeUnixNano": 1748858980196541700, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - GET /fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "27fde97aa0cac29832aed957aa5ac01b", "spanId": "4e7c330e99ab1b6e", "parentSpanId": "6a65ff50818aea2d", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858980196687000, "endTimeUnixNano": 1748858980197330200, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - GET /fintech-citadel/2/application/APP2506021539398438866221813658696442668"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}]}