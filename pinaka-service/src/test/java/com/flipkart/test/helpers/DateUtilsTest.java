package com.flipkart.test.helpers;

import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import org.junit.Test;


import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


public class DateUtilsTest {

    @Test
    public void testExperianDateFormat() throws ParseException {
        Date date = DateUtils.getDateFromString("19991019", DateUtils.experianDateFormat);
        Calendar calendar = Calendar.getInstance();
        calendar.set(1999, 9, 19, 0, 0, 0);
        assertEquals(date.toString(), calendar.getTime().toString());
        String dateInUi = DateUtils.convertDateFormat("19991019", DateUtils.experianDateFormat, DateUtils.getSimpleDateFormat9);
        assertEquals(dateInUi, "19/10/1999");
    }

    @Test(expected = ParseException.class)
    public void testExperianDateParseException() throws ParseException {
        Date date = DateUtils.getDateFromString("19/10/1999", DateUtils.experianDateFormat);
    }
}
