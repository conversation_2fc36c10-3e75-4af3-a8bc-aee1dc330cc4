package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.utils.FormUtils.PhoneNumberValidator;
import com.google.i18n.phonenumbers.NumberParseException;
import org.junit.Test;


import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ETBPhoneNumberTest {
    @Test
    public void testValidPhoneNumber() throws NumberParseException {
        assertFalse(PhoneNumberValidator.validPhoneNumber("+911223"));
        assertFalse(PhoneNumberValidator.validPhoneNumber("0"));
        assertTrue(PhoneNumberValidator.validPhoneNumber("+911234567890"));
        assertTrue(PhoneNumberValidator.validPhoneNumber("1234567890"));
        assertFalse(PhoneNumberValidator.validPhoneNumber("123567890"));
        assertFalse(PhoneNumberValidator.validPhoneNumber("+91-1234"));
        assertTrue(PhoneNumberValidator.validPhoneNumber("+91-1234567890"));
        assertFalse(PhoneNumberValidator.validPhoneNumber("+91123"));
        assertFalse(PhoneNumberValidator.validPhoneNumber(null));
    }
}
