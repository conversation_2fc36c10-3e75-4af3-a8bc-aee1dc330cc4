package com.flipkart.fintech.pinaka.service.ruleengine.lendingorchestrator.pagestate;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.offer.orchestrator.model.PreApprovedOfferDetails;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class PageStateRuleEngineTest {

  private final PageStateRuleEngine pageStateRuleEngine = new PageStateRuleEngine(null);

  @ParameterizedTest
  @MethodSource("providePageStateRuleEngineInput")
  public void expectsToCreateLead(PageStateRuleEngineInput input,
      PageStateRuleEngineStates expected) {
    assertEquals(expected, pageStateRuleEngine.getStateByRuleEngine(input));
  }

  public static Stream<Arguments> providePageStateRuleEngineInput() {
    return Stream.of(
        Arguments.of(
            new PageStateRuleEngineInput(null, null, null, null),
            PageStateRuleEngineStates.CREATE_LEAD
        ),
        Arguments.of(
            new PageStateRuleEngineInput(null, getApplicationDataResponse("BASIC_DETAILS"), null,
                null),
            PageStateRuleEngineStates.RESUME_LEAD
        ),
        Arguments.of(
            new PageStateRuleEngineInput(getApplicationDataResponse("SUBMIT_OFFER_START"), null,
                getLendingPageRequest(), null),
            PageStateRuleEngineStates.RESUME_LENDER_APPLICATION
        ),
        Arguments.of(
            new PageStateRuleEngineInput(getApplicationDataResponse("SUBMIT_OFFER_START"), null,
                null, null),
            PageStateRuleEngineStates.RESUME_LENDER_APPLICATION
        ),
        Arguments.of(
            new PageStateRuleEngineInput(getApplicationDataResponse("SUCCESS"), null,
                null, getPreApprovedOfferDetails()),
            PageStateRuleEngineStates.REPEAT_LOAN_LANDING_PAGE
        ),
        Arguments.of(
            new PageStateRuleEngineInput(getApplicationDataResponse("SUCCESS"), null,
                null, null),
            PageStateRuleEngineStates.RESUME_LENDER_APPLICATION
        ),Arguments.of(
            new PageStateRuleEngineInput(getApplicationDataResponse("APPLICATION_COMPLETED"), null,
                null, getPreApprovedOfferDetails()),
            PageStateRuleEngineStates.REPEAT_LOAN_LANDING_PAGE
        ),
        Arguments.of(
            new PageStateRuleEngineInput(getApplicationDataResponse("APPLICATION_COMPLETED"), null,
                null, null),
            PageStateRuleEngineStates.RESUME_LENDER_APPLICATION
        )
    );
  }

  private static PreApprovedOfferDetails getPreApprovedOfferDetails() {
    return PreApprovedOfferDetails.builder().build();
  }

  private static LendingPageRequest getLendingPageRequest() {
    return LendingPageRequest.builder().applicationId("applicationId").build();
  }

  private static ApplicationDataResponse getApplicationDataResponse(String applicationState) {
    return ApplicationDataResponse.builder()
        .externalUserId("externalUserId")
        .applicationId("applicationId")
        .applicationType("applicationType")
        .tenant(Tenant.CALM)
        .applicationState(applicationState)
        .build();
  }

}