package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DateFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.CustomLog;
import lombok.SneakyThrows;
import org.apache.http.NameValuePair;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.testng.asserts.Assertion;
import java.text.ParseException;

import java.io.*;
import java.time.ZonedDateTime;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage1FormTransformer.DATE_OF_BIRTH_FORM_FIELD_INDEX;

@CustomLog
public class ReviewPage1FormTransformerTest {

    private ReviewPage1FormTransformer reviewPage1FormTransformer;
    private ReviewPage1FormTransformer reviewPage1FormTransformerWrongDOB;
    private BureauDataManager bureauDataManager;
    Map<String, ReviewPage1FormTransformer.FormFields> dataPageScreen = new HashMap<String, ReviewPage1FormTransformer.FormFields>() {{
        put("panNumber", ReviewPage1FormTransformer.FormFields.PAN_NUMBER);
        put("dob", ReviewPage1FormTransformer.FormFields.DOB);
        put("email", ReviewPage1FormTransformer.FormFields.EMAIL);
        put("houseNumber", ReviewPage1FormTransformer.FormFields.HOUSE_NUMBER);
        put("area", ReviewPage1FormTransformer.FormFields.AREA);
        put("pincodeDetails", ReviewPage1FormTransformer.FormFields.PINCODE);
        put("gender", ReviewPage1FormTransformer.FormFields.GENDER);
        put("employmentType", ReviewPage1FormTransformer.FormFields.EMPLOYMENT_TYPE);
    }};


    @Before
    public void setUp() throws IOException {
        InputStream applicationResponseRaw = getClass().getClassLoader().getResourceAsStream("response/applicationDataResponse_page2.json");
        ApplicationDataResponse applicationDataResponse = new ObjectMapper().readValue(applicationResponseRaw, ApplicationDataResponse.class);
        ProfileDetailedResponse profileDetailedResponse = new ObjectMapper().readValue(getClass().getClassLoader()
                .getResourceAsStream("response/profileDetailedResponse_archit.json"), ProfileDetailedResponse.class);
        ProfileDetailedResponse profileDetailedResponseWrongDOB = new ObjectMapper().readValue(getClass().getClassLoader()
                .getResourceAsStream("response/profileDetailedResponse_wrongDOB.json"), ProfileDetailedResponse.class);
        ReviewUserDataSourceResponse reviewUserDataSourceResponse = getMockReviewUserDataSource(applicationDataResponse, profileDetailedResponse);
        ReviewUserDataSourceResponse reviewUserDataSourceResponseWrongDOB = getMockReviewUserDataSource(applicationDataResponse, profileDetailedResponseWrongDOB);
        reviewPage1FormTransformer = new ReviewPage1FormTransformer(reviewUserDataSourceResponse, dataPageScreen, null);
        reviewPage1FormTransformerWrongDOB = new ReviewPage1FormTransformer(reviewUserDataSourceResponseWrongDOB,dataPageScreen, null);
    }

    private ReviewUserDataSourceResponse getMockReviewUserDataSource(ApplicationDataResponse applicationDataResponse, ProfileDetailedResponse profileDetailedResponse) {
        ReviewUserDataSourceResponse reviewUserDataSourceResponse = new ReviewUserDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        reviewUserDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setAddressLine1("141, #2, GreenLine Apartments");
        addressDetailResponse.setAddressLine2("Harlur Road");
        addressDetailResponse.setPincode("104591");
        InitialUserDataResponse initialUserDataResponse = InitialUserDataResponse.builder()
                                                            .panNumber("**********")
                                                            .gender("1")
                                                            .addressDetailResponse(addressDetailResponse)
                                                            .dateOfBirth("19850718")
                                                            .build();
        reviewUserDataSourceResponse.setInitialUserDataResponse(initialUserDataResponse);
        reviewUserDataSourceResponse.setProfile(profileDetailedResponse);
        return reviewUserDataSourceResponse;
    }


    @SneakyThrows
    @Test
    public void testReviewPage1BuildWigdetDataPrefillFromProfile(){
        GenericFormWidgetData widgetData = reviewPage1FormTransformer.buildWidgetData(new ApplicationDataResponse());

        for (RenderableComponent<FormFieldValue> renderableComponent : widgetData.getRenderableComponents()) {
            // Assert we are prefilling from profile if pan is present in both profile and experian
            log.info(renderableComponent.getValue().getName());
            if ("panNumber".equals(renderableComponent.getValue().getName())){
                Assertions.assertEquals(renderableComponent.getValue().getValue(), "**********");
            }
            //Assert we are prefilling employment Type
            if("employmentType".equals(renderableComponent.getValue().getName())){
                Assertions.assertEquals(Objects.requireNonNull(((MultiDropdownFormFieldValue) renderableComponent.getValue()).getValue()), "Salaried");
            }
            // Assert prefill this from profile over experian
            if("houseNumber".equals(renderableComponent.getValue().getName())){
                Assertions.assertEquals(renderableComponent.getValue().getValue(), "1503");
            }

            if("dob".equals(renderableComponent.getValue().getName())){
                Assertions.assertEquals(renderableComponent.getValue().getValue(),"19/10/1999");
            }
        }

        // Assert we are setting min age to 18
        String maxDob = (Objects.requireNonNull((DateFormFieldValue) widgetData.getRenderableComponents().get(DATE_OF_BIRTH_FORM_FIELD_INDEX).getValue())).getMaxValue();
        assert maxDob != null;
        Assertions.assertEquals(ZonedDateTime.parse(maxDob).toLocalDate().toString(), DateTime.now().minusYears(18).toLocalDate().toString());
    }

    @SneakyThrows
    @Test
    public void testReviewPage1DOBException(){
        GenericFormWidgetData widgetData = reviewPage1FormTransformerWrongDOB.buildWidgetData(new ApplicationDataResponse());

        for(RenderableComponent<FormFieldValue> renderableComponent : widgetData.getRenderableComponents()){
            if("dob".equals(renderableComponent.getValue().getName())){
                Assertions.assertNull(renderableComponent.getValue().getValue());
            }
        }
    }
}