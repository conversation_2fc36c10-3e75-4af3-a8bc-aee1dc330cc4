package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.fintech.profile.eventPulisher.BQEventPublisher;
import com.flipkart.rome.datatypes.common.events.*;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class UIEventIngestionImplTest {

    private UIEventIngestionHandlerImpl uiEventIngestionHandler;
    private ObjectMapper objectMapper;
    @Mock
    private BQEventPublisher bqEventPublisher;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        objectMapper = new ObjectMapper();
        uiEventIngestionHandler = new UIEventIngestionHandlerImpl(objectMapper, bqEventPublisher);
    }
    
    @Test
    public void testPayloadFor_API_CALL_EVENT() throws IOException {
        ApiCallEvent event = new ApiCallEvent();
        event.setEvent_id("abc");
        event.setEvent_type("API_CALL_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        event.setApi_name("api_ABC");
        event.setNetwork_info_downlink("info");
        event.setNetwork_info_effectiveType("xyz");
        event.setNetwork_info_rtt(1000);
        event.setLatency(100);
        event.setStatus("SUCCESS");
        event.setReq_id("rid-123");
        event.setError_reason("error reason - xyz");

        String expectedDynamicFields = "{\"api_name\":\"api_ABC\",\"network_info_downlink\":\"info\",\"network_info_rtt\":1000,\"latency\":100,\"status\":\"SUCCESS\",\"req_id\":\"rid-123\",\"error_reason\":\"error reason - xyz\",\"network_info_effectiveType\":\"xyz\"}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_ALL_CONSENTS_NOT_CHECKED_EVENT() throws IOException {
        AllConsentsNotCheckedEvent event = new AllConsentsNotCheckedEvent();
        event.setEvent_id("abc");
        event.setEvent_type("ALL_CONSENTS_NOT_CHECKED_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        String expectedDynamicFields = "{}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_CONSENT_CLICKED_EVENT() throws IOException {
        ConsentClickedEvent event = new ConsentClickedEvent();
        event.setEvent_id("abc");
        event.setEvent_timestamp(100000);
        event.setEvent_type("CONSENT_CLICKED_EVENT");
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");
        event.setConsent_for("login");
        event.setConsent_id("uid");
        event.setConsent_type("xyz");
        event.setChecked(true);

        String expectedDynamicFields = "{\"checked\":true,\"consent_for\":\"login\",\"consent_id\":\"uid\",\"consent_type\":\"xyz\"}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_CONSENT_IMPRESSION_EVENT() throws IOException {
        ConsentImpressionEvent event = new ConsentImpressionEvent();
        event.setEvent_id("abc");
        event.setEvent_type("CONSENT_IMPRESSION_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        event.setConsent_for("login");
        event.setConsent_id("uid");
        event.setConsent_type("xyz");

        String expectedDynamicFields = "{\"consent_id\":\"uid\",\"consent_for\":\"login\",\"consent_type\":\"xyz\"}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_CTA_CLICKED_EVENT() throws IOException {
        CTAClickEvent event = new CTAClickEvent();
        event.setEvent_id("abc");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");
        event.setEvent_type("CTA_CLICKED_EVENT");

        String expectedDynamicFields = "{}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_FIELD_IMPRESSION_EVENT() throws IOException {
        FieldImpressionEvent event = new FieldImpressionEvent();
        event.setEvent_id("abc");
        event.setEvent_type("FIELD_CLICK_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        event.setField_name("field_name");
        event.setPrefill(true);
        event.setPrefilled_value("random");
        event.setSend_edits(true);
        event.setSend_impression(true);
        event.setSend_inline_error(true);
        event.setEdited_how_many_times(10);

        String expectedDynamicFields = "{\"field_name\":\"field_name\",\"prefilled_value\":\"random\",\"prefill\":true,\"send_edits\":true,\"send_impression\":true,\"send_inline_error\":true,\"edited_how_many_times\":10}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_FIELD_CLICK_EVENT() throws IOException {
        FieldClickEvent event = new FieldClickEvent();
        event.setEvent_id("abc");
        event.setEvent_type("FIELD_CLICK_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        event.setField_name("field_name");
        event.setPrefill(true);
        event.setPrefilled_value("random");
        event.setSend_edits(true);
        event.setSend_impression(true);
        event.setSend_inline_error(true);
        event.setEdited_how_many_times(10);

        event.setEdited(true);

        String expectedDynamicFields = "{\"edited\":true,\"field_name\":\"field_name\",\"prefill\":true,\"prefilled_value\":\"random\",\"send_edits\":true,\"send_impression\":true,\"send_inline_error\":true,\"edited_how_many_times\":10}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_PAGE_LOAD_EVENT() throws IOException {
        PageLoadEvent event = new PageLoadEvent();
        event.setEvent_id("abc");
        event.setEvent_type("PAGE_LOAD_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");
        event.setPage_load_time(1000);

        String expectedDynamicFields = "{\"page_load_time\":1000}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_CTA_IMPRESSION_EVENT() throws IOException {
        CtaImpressionEvent event = new CtaImpressionEvent();
        event.setEvent_id("abc");
        event.setEvent_type("CTA_IMPRESSION_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        event.setCk("dummy-ck");
        event.setMeta("dummy-meta");
        event.setNavigational_page_type("dummy-example");

        String expectedDynamicFields = "{\"navigational_page_type\":\"dummy-example\",\"meta\":\"dummy-meta\",\"ck\":\"dummy-ck\"}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }

    @Test
    public void testPayloadFor_TOAST_EVENT() throws IOException {
        ToastEvent event = new ToastEvent();
        event.setEvent_id("abc");
        event.setEvent_type("TOAST_EVENT");
        event.setEvent_timestamp(100000);
        event.setOs("ios");
        event.setOs("1.0");
        event.setBrowser("chrome");
        event.setBrowser_version("1.0");
        event.setDevice_type("mobile");
        event.setScreen_resolution("1x2");
        event.setUser_agent("usm_abc");
        event.setConnection_type("HTTP/1.1");
        event.setLocale("english");
        event.setTimezone("asia");
        event.setAccount_id("xyz");
        event.setApplication_id("xyz1");
        event.setLogin_status("login:Myntra");
        event.setPage_id("null");
        event.setPage_name("null");
        event.setPage_type("null");
        event.setPrevious_page_id("1");
        event.setPrevious_page_name("1");
        event.setPrevious_page_type("1");
        event.setUtm_campaign("UK");
        event.setUtm_medium("UK");
        event.setUtm_source("UK");

        event.setMessage("dummy-ck");
        event.setToast_id("dummy-id");

        String expectedDynamicFields = "{\"message\":\"dummy-ck\",\"toast_id\":\"dummy-id\"}";
        assertEquals(expectedDynamicFields.length(), uiEventIngestionHandler.getDynamicJsonFields(event).length());
    }
}
