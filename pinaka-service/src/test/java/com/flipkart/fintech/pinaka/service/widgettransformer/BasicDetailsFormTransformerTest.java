package com.flipkart.fintech.pinaka.service.widgettransformer;

import static com.flipkart.fintech.pinaka.service.widgettransformer.BasicDetailsFormTransformer.DATE_OF_BIRTH_FORM_FIELD_INDEX;
import static org.junit.jupiter.api.Assertions.*;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.response.BasicDetailsPageDataSourceResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Nullable;
import org.junit.jupiter.api.Test;

class BasicDetailsFormTransformerTest {

  private BasicDetailsFormTransformer transformer;

  @Test
  public void shouldSetDateInFormIfDateIsCorrect() throws PinakaClientException {
    BasicDetailsPageDataSourceResponse response = BasicDetailsPageDataSourceResponse.builder()
        .profile(ProfileDetailedResponse.builder()
            .dob("WuSHJ6xfr9hTrKaJ3aHmfQ==") // 07/01/1993
            .build())
        .build();
    Map<String, Object> applicationData = new HashMap<>();
    ApplicationDataResponse applicationDataResponse = getApplicationDataResponse(
        applicationData);
    transformer = new BasicDetailsFormTransformer(response, new HashMap<>(), null);

    GenericFormWidgetData genericFormWidgetData = transformer.buildWidgetData(
        applicationDataResponse);

    assertEquals("07/01/1993", getDateOfBirthValue(genericFormWidgetData));
  }

  @Test
  public void shouldNotSetDateInFormIfDateIsIncorrect() throws PinakaClientException {
    BasicDetailsPageDataSourceResponse response = BasicDetailsPageDataSourceResponse.builder()
        .profile(ProfileDetailedResponse.builder()
            .dob("bHSdwA7CL09tA91AxF64tg==") // 1997-01-13
            .build())
        .build();
    Map<String, Object> applicationData = new HashMap<>();
    ApplicationDataResponse applicationDataResponse = getApplicationDataResponse(
        applicationData);
    transformer = new BasicDetailsFormTransformer(response, new HashMap<>(), null);

    GenericFormWidgetData genericFormWidgetData = transformer.buildWidgetData(
        applicationDataResponse);

    assertNull(getDateOfBirthValue(genericFormWidgetData));
  }

  private static ApplicationDataResponse getApplicationDataResponse(
      Map<String, Object> applicationData) {
    applicationData.put("financial_provider", "AXIS");
    ApplicationDataResponse applicationDataResponse = ApplicationDataResponse.builder()
        .merchantId("merchantId")
        .externalUserId("externalUserId")
        .applicationType("PERSONAL_LOAN")
        .applicationId("applicationId")
        .tenant(Tenant.CALM)
        .applicationData(applicationData)
        .build();
    return applicationDataResponse;
  }

  private static @Nullable Object getDateOfBirthValue(GenericFormWidgetData genericFormWidgetData) {
    return Objects.requireNonNull(
        Objects.requireNonNull(genericFormWidgetData.getRenderableComponents())
            .get(DATE_OF_BIRTH_FORM_FIELD_INDEX).getValue()).getValue();
  }

}