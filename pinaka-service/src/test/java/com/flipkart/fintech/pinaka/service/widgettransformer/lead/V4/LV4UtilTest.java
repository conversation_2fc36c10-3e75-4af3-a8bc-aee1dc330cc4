package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V4;

import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LV4UtilTest {

    @Mock
    private DynamicBucket dynamicBucket;

    @Test
    public void testIsLv4Enabled_FeatureFlagDisabled() {
        when(dynamicBucket.getBoolean("ENABLE_LEAD_V4_FLOW")).thenReturn(false);

        boolean result = LV4Util.isLv4Enabled(dynamicBucket, "user123");

        assertFalse(result);
    }

    @Test
    public void testIsLv4Enabled_WhitelistedUser() {
        when(dynamicBucket.getBoolean("ENABLE_LEAD_V4_FLOW")).thenReturn(true);
        when(dynamicBucket.getStringArray("LEAD_V4_WHITELISTED_USERS")).thenReturn(Arrays.asList("user123", "user456"));

        boolean result = LV4Util.isLv4Enabled(dynamicBucket, "user123");

        assertTrue(result);
    }

    @Test
    public void testIsLv4Enabled_NotWhitelistedUser() {
        when(dynamicBucket.getBoolean("ENABLE_LEAD_V4_FLOW")).thenReturn(true);
        when(dynamicBucket.getStringArray("LEAD_V4_WHITELISTED_USERS")).thenReturn(Arrays.asList("user456", "user789"));

        boolean result = LV4Util.isLv4Enabled(dynamicBucket, "user123");

        assertFalse(result);
    }

    @Test
    public void testIsLv4Enabled_PercentageBasedRouting() {
        when(dynamicBucket.getBoolean("ENABLE_LEAD_V4_FLOW")).thenReturn(true);
        when(dynamicBucket.getStringArray("LEAD_V4_WHITELISTED_USERS")).thenReturn(Arrays.asList());
        when(dynamicBucket.getInt("LEAD_V4_TRAFFIC_PERCENTAGE")).thenReturn(100);

        // With 100% traffic, should always be enabled
        boolean result = LV4Util.isLv4Enabled(dynamicBucket, "user123");
        assertTrue("User should be enabled with 100% traffic", result);

        // Test with 0% traffic
        when(dynamicBucket.getInt("LEAD_V4_TRAFFIC_PERCENTAGE")).thenReturn(0);
        boolean result2 = LV4Util.isLv4Enabled(dynamicBucket, "user123");
        assertFalse("User should not be enabled with 0% traffic", result2);
    }

    @Test
    public void testIsLv4Application_WithApplicationDataResponse() {
        ApplicationDataResponse response = new ApplicationDataResponse();
        response.setApplicationState("LEAD_V4_PAGE_1");
        response.setApplicationData(new HashMap<>());

        boolean result = LV4Util.isLv4Application(response);

        assertTrue(result);
    }

    @Test
    public void testIsLv4Application_WithoutV4InState() {
        ApplicationDataResponse response = new ApplicationDataResponse();
        response.setApplicationState("LEAD_V3_PAGE_1");
        response.setApplicationData(new HashMap<>());
        
        boolean result = LV4Util.isLv4Application(response);
        
        assertFalse(result);
    }

    @Test
    public void testIsLv4Application_WithV4InApplicationData() {
        ApplicationDataResponse response = new ApplicationDataResponse();
        response.setApplicationState("SOME_STATE");
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_user_name", "John Doe");
        response.setApplicationData(applicationData);
        
        boolean result = LV4Util.isLv4Application(response);
        
        assertTrue(result);
    }

    @Test
    public void testIsLv4Application_NullResponse() {
        boolean result = LV4Util.isLv4Application((ApplicationDataResponse) null);
        
        assertFalse(result);
    }

    @Test
    public void testIsLv4Application_WithApplicationDataMap() {
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        
        boolean result = LV4Util.isLv4Application(applicationData);
        
        assertTrue(result);
    }

    @Test
    public void testIsLv4Application_EmptyApplicationData() {
        Map<String, Object> applicationData = new HashMap<>();
        
        boolean result = LV4Util.isLv4Application(applicationData);
        
        assertFalse(result);
    }
}
