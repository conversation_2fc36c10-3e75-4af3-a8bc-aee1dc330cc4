package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.calm.AutoSuggestFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.PinakaConstants;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.SneakyThrows;
import org.apache.http.NameValuePair;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;


import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ReviewPage2FormTransformerTest {
    private ReviewPage2FormTransformer reviewPage2FormTransformer;
    private ApplicationDataResponse applicationDataResponse;
    private DynamicBucket dynamicBucket;
    Map<String, ReviewPage2FormTransformer.FormField> formFieldMap = new HashMap<String, ReviewPage2FormTransformer.FormField>() {{
        put("organization", ReviewPage2FormTransformer.FormField.COMPANY_NAME);
        put("income", ReviewPage2FormTransformer.FormField.MONTHLY_INCOME);
        put("incomeSource", ReviewPage2FormTransformer.FormField.INCOME_SOURCE);
        put("industryName", ReviewPage2FormTransformer.FormField.INDUSTRY_TYPE);
        put("annualTurnOver", ReviewPage2FormTransformer.FormField.ANNUAL_TURNOVER);
        put("bonusIncome", ReviewPage2FormTransformer.FormField.BONUS_OR_OTHER_INCOME);
    }};

    @Before
    public void setUp() throws IOException {
        dynamicBucket = Mockito.mock(DynamicBucket.class);
        Mockito.when(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_MFI)).thenReturn("002");
        Mockito.when(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_CKCY)).thenReturn("003");
        Mockito.when(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_NONPEP)).thenReturn("004");
        InputStream applicationResponseRaw = getClass().getClassLoader().getResourceAsStream("response/applicationDataResponse_ReviewPage2.json");
        applicationDataResponse = new ObjectMapper().readValue(applicationResponseRaw, ApplicationDataResponse.class);
        InputStream profileResponse = getClass().getClassLoader().getResourceAsStream("response/profileDetailedResponse_wrongDOB.json");
        ProfileDetailedResponse profileDetailedResponse = new ObjectMapper().readValue(profileResponse, ProfileDetailedResponse.class);
        ReviewUserDataSourceResponse reviewUserDataSourceResponse = getMockReviewUserDataSource(applicationDataResponse,profileDetailedResponse);
        reviewPage2FormTransformer = new ReviewPage2FormTransformer(reviewUserDataSourceResponse, formFieldMap, dynamicBucket);
    }

    private ReviewUserDataSourceResponse getMockReviewUserDataSource(ApplicationDataResponse applicationDataResponse, ProfileDetailedResponse profileDetailedResponse) {
        ReviewUserDataSourceResponse reviewUserDataSourceResponse = new ReviewUserDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        reviewUserDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        reviewUserDataSourceResponse.setProfile(profileDetailedResponse);
        return reviewUserDataSourceResponse;
    }

    @SneakyThrows
    @Test
    public void testReviewPage1BuildWigdetDataPrefillFromProfile(){
        GenericFormWidgetData widgetData = reviewPage2FormTransformer.buildWidgetData(applicationDataResponse);

        for (RenderableComponent<FormFieldValue> renderableComponent : widgetData.getRenderableComponents()) {
            if ("organization".equals(Objects.requireNonNull(renderableComponent.getValue()).getName())){
                Assertions.assertEquals(((AutoSuggestFormFieldValue)renderableComponent.getValue()).getValue().getTitle(), "Amik Metals Pvt Ltd");
            }
            if ("income".equals(renderableComponent.getValue().getName())){
                Assertions.assertEquals(renderableComponent.getValue().getValue(), "14000");
            }
            if ("bonusIncome".equals(renderableComponent.getValue().getName())){
                Assertions.assertEquals(renderableComponent.getValue().getValue(), "10000");
                Assertions.assertEquals(Objects.requireNonNull(renderableComponent.getValue().getTracking()).get("prefilledValue"), "10000");
            }

        }
    }

}