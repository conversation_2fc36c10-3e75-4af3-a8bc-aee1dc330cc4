package com.flipkart.fintech.pinaka.service.web.impl;

import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.TestHelper;
import com.flipkart.fintech.pinaka.service.core.SecurityService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import io.dropwizard.jersey.errors.ErrorMessage;
import io.dropwizard.testing.junit.ResourceTestRule;
import org.junit.After;
import org.junit.Before;
import org.junit.ClassRule;
import org.junit.Test;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SecurityResourceV6ImplTest {

    private final static SecurityService securityService = mock(SecurityService.class);
    private TestHelper testHelper;
    private final static String errorMessage = "test error";

    @ClassRule
    public final static ResourceTestRule resource =
            ResourceTestRule.builder().addResource(new SecurityResourceImpl(securityService)).build();

    @Before
    public void setUp() throws Exception {
        testHelper = new TestHelper();
    }

    @After
    public void tearDown() throws Exception {
        reset(securityService);
    }

    @Test
    public void testGetKeyShouldThrowServerError() throws Exception {
        when(securityService.getKey()).thenThrow(new PinakaException(errorMessage));
        Response response = resource.client().target("/security/key").request()
                .accept(MediaType.APPLICATION_JSON)
                .get();
        assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), response.getStatus());
        assertEquals(errorMessage, response.readEntity(ErrorMessage.class).getMessage());
        verify(securityService).getKey();
    }

    @Test
    public void testGetKey() throws Exception {
        SecurityKeyResponse expectedResponse = testHelper.getSecurityKeyResponse();
        when(securityService.getKey()).thenReturn(expectedResponse);
        Response response = resource.client().target("/security/key").request()
                .accept(MediaType.APPLICATION_JSON)
                .get();
        verify(securityService).getKey();
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        SecurityKeyResponse actualResponse = response.readEntity(SecurityKeyResponse.class);
        assertEquals(expectedResponse.getKeyRef(), actualResponse.getKeyRef());
        assertEquals(expectedResponse.getPublicKey(), actualResponse.getPublicKey());
    }
}