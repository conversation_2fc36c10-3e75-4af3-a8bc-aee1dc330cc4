package com.flipkart.fintech.pinaka.service.core.v7;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

class FinalNameTest {

  public static final String VALID_FIRST_NAME = "GE14gzEGK3H64NHI7f8UiQ==";
  public static final String VALID_LAST_NAME = "sJHP+KG+vB5gCboxllmCwg==";
  public static final String EMPTY_FIRST_NAME = "KGEOIYrJDB3Sjuqu7OYLKw==";
  public static final String VERIFIED_LAST_NAME = "sQYd9aFo+k+/MR1L4Y3zew==";
  public static final String VERIFIED_FIRST_NAME = "iIhaZEdcYl0vAU2sPWsgfw==";

  @Test
  void validateOnNullData() {
    Map<String, Object> basicDetails = new HashMap<>();
    basicDetails.put("firstName", VALID_FIRST_NAME);
    basicDetails.put("lastName", VALID_LAST_NAME);
    FinalName finalName = FinalName.from(null, basicDetails);
    assertNotNull(finalName.getFirstName());
    assertNotNull(finalName.getLastName());
  }

  @Test
  void validateNullNameInBoth() {
    Map<String, Object> verifyPanName = new HashMap<>();
    Map<String, Object> basicDetails = new HashMap<>();
    FinalName finalName = FinalName.from(verifyPanName, basicDetails);
    assertNotNull(finalName.getFirstName());
    assertNotNull(finalName.getLastName());
  }

  @Test
  void validateNullNameInVerifyPan() {
    Map<String, Object> verifyPanName = new HashMap<>();
    Map<String, Object> basicDetails = new HashMap<>();
    basicDetails.put("firstName", VALID_FIRST_NAME);
    basicDetails.put("lastName", VALID_LAST_NAME);
    FinalName finalName = FinalName.from(verifyPanName, basicDetails);
    assertNotNull(finalName.getFirstName());
    assertNotNull(finalName.getLastName());
  }


  @Test
  void validateValidNameInVerifyPan() {
    Map<String, Object> verifyPanName = new HashMap<>();
    Map<String, Object> basicDetails = new HashMap<>();
    verifyPanName.put("firstName", VERIFIED_FIRST_NAME);
    verifyPanName.put("lastName", VERIFIED_LAST_NAME);
    FinalName finalName = FinalName.from(verifyPanName, basicDetails);
    assertNotNull(finalName.getFirstName());
    assertNotNull(finalName.getLastName());
  }

  @Test
  void validateThatVerifyPanDataIsMovedToBasicDetails() {
    Map<String, Object> verifyPanName = new HashMap<>();
    Map<String, Object> basicDetails = new HashMap<>();
    verifyPanName.put("firstName", VERIFIED_FIRST_NAME);
    verifyPanName.put("lastName", VERIFIED_LAST_NAME);
    FinalName finalName = FinalName.from(verifyPanName, basicDetails);
    assertNotNull(finalName.getFirstName());
    assertNotNull(finalName.getLastName());
    assertEquals(VERIFIED_FIRST_NAME, finalName.getEncryptedFirstName());
    assertEquals(VERIFIED_LAST_NAME, finalName.getEncryptedLastName());
  }

  @Test
  void validateInValidNameInVerifyPan() {
    Map<String, Object> verifyPanName = new HashMap<>();
    Map<String, Object> basicDetails = new HashMap<>();
    verifyPanName.put("firstName", EMPTY_FIRST_NAME); // empty string
    verifyPanName.put("lastName", VERIFIED_LAST_NAME);
    basicDetails.put("firstName", VALID_FIRST_NAME);
    basicDetails.put("lastName", VALID_LAST_NAME);
    FinalName finalName = FinalName.from(verifyPanName, basicDetails);
    assertNotNull(finalName.getFirstName());
    assertEquals("R S Devi", finalName.getFirstName());
    assertNotNull(finalName.getLastName());
  }

}