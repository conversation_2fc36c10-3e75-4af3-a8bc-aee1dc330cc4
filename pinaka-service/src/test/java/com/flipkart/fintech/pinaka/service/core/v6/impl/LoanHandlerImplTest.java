package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.fintech.lead.service.LeadService;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.arsenal.ArsenalService;
import com.flipkart.fintech.pinaka.service.core.page.PageActionResponseHandler;
import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.pinaka.service.core.v7.CreateApplicationRequestFactory;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.AmsBridge;
import org.junit.Ignore;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Ignore //todo: fix this
class LoanHandlerImplTest {

    public static final String MERCHANT_ID = "FK";
    @Mock
    private ApplicationService applicationService;

    @Mock
    private CreateApplicationRequestFactory createApplicationRequestFactory;

    @Mock
    private PageActionResponseHandler pageActionResponseHandler;

    @Mock
    private ArsenalService arsenalService;

    @Mock
    private AmsBridge amsBridge;

    @Mock
    private LandingPageRequest landingPageRequest;

    @Mock
    private ApplicationDataResponse applicationDataResponse;

    @Mock
    private LeadService leadService;

    private LoanHandlerImpl loanHandler;

    @BeforeEach
    void setUp() {
        loanHandler = new LoanHandlerImpl( pageActionResponseHandler);
    }

    @AfterEach
    void tearDown() {
        loanHandler = null;
    }

}