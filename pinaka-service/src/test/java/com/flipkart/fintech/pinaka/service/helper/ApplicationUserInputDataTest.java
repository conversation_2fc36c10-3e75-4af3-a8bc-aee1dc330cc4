package com.flipkart.fintech.pinaka.service.helper;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        ApplicationUserInputData.class,
        ApplicationInputDataMapper.class
})
public class ApplicationUserInputDataTest {

    private Map<String, Object> applicationData;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        applicationData = new HashMap<>();

        Map<String, Object> nameDetails = new HashMap<>();
        nameDetails.put("firstName", "John");
        nameDetails.put("lastName", "Doe");
        nameDetails.put("phoneNumber", "1234567890");

        Map<String, Object> basicDetails = new HashMap<>();
        basicDetails.put("dob", "1990-01-01");
        basicDetails.put("gender", "Male");
        basicDetails.put("area", "Downtown");
        basicDetails.put("houseNumber", "42");
        basicDetails.put("panNumber", "**********");

        Map<String, Object> pincodeDetails = new HashMap<>();
        pincodeDetails.put("pincode", "560001");
        pincodeDetails.put("city", "Bangalore");
        pincodeDetails.put("state", "Karnataka");
        basicDetails.put("pincodeDetails", pincodeDetails);

        Map<String, Object> incomeDetails = new HashMap<>();
        incomeDetails.put("email", "<EMAIL>");
        incomeDetails.put("income", "500000");
        incomeDetails.put("loanPurpose", "Education");
        incomeDetails.put("employmentType", "Salaried");
        incomeDetails.put("organization", "ExampleCorp");
        incomeDetails.put("incomeSource", "Job");
        incomeDetails.put("industryName", "IT");
        incomeDetails.put("annualTurnOver", "1000000");
        incomeDetails.put("consentListData", Arrays.asList("A", "B"));
        incomeDetails.put("consentData", Collections.singletonMap("agree", true));

        mockStatic(ApplicationInputDataMapper.class);
        Mockito.when(ApplicationInputDataMapper.getNameDetails(applicationData)).thenReturn(nameDetails);
        Mockito.when(ApplicationInputDataMapper.getBasicDetails(applicationData)).thenReturn(basicDetails);
        Mockito.when(ApplicationInputDataMapper.getIncomeDetails(applicationData)).thenReturn(incomeDetails);
    }

    @Test
    public void testGetApplicationResponseData_validInput_shouldPopulateFields() throws PinakaException, PinakaException {
        ApplicationUserInputData data = ApplicationUserInputData.getApplicationResponseData(applicationData);

        assertEquals("John", data.getFirstName());
        assertEquals("Doe", data.getLastName());
        assertEquals("1234567890", data.getPhoneNumber());
        assertEquals("1990-01-01", data.getDob());
        assertEquals("560001", data.getPincode().toString());
        assertEquals("Bangalore", data.getCity());
        assertEquals("Karnataka", data.getState());
        assertEquals("**********", data.getPan());
        assertEquals("Male", data.getGender());
        assertEquals("Downtown", data.getArea());
        assertEquals("42", data.getHouseNumber());
        assertEquals("<EMAIL>", data.getEmail());
        assertEquals("500000", data.getIncome());
        assertEquals("Education", data.getLoanPurpose());
        assertEquals("Salaried", data.getEmploymentType().name());
        assertEquals("ExampleCorp", data.getOrganization());
        assertEquals("Job", data.getIncomeSource());
        assertEquals("IT", data.getIndustryName());
        assertEquals("1000000", data.getAnnualTurnOver());
        assertNotNull(data.getConsentListData());
        assertNotNull(data.getConsentData());
    }

    @Test
    public void testGetApplicationResponseData_fallbackToFlatPincode_shouldWork() throws PinakaException {
        Map<String, Object> flatBasicDetails = new HashMap<>();
        flatBasicDetails.put("pincode", "560002");
        flatBasicDetails.put("city", "Mumbai");
        flatBasicDetails.put("state", "Maharashtra");

        Mockito.when(ApplicationInputDataMapper.getBasicDetails(applicationData)).thenReturn(flatBasicDetails);

        ApplicationUserInputData data = ApplicationUserInputData.getApplicationResponseData(applicationData);

        assertEquals("560002", data.getPincode().toString());
        assertEquals("Mumbai", data.getCity());
        assertEquals("Maharashtra", data.getState());
    }

    @Test
    public void testGetApplicationResponseData_panFallback_shouldUsePanIfPanNumberMissing() throws PinakaException {
        Map<String, Object> noPanNumberDetails = new HashMap<>();
        noPanNumberDetails.put("pan", "XYZ1234");

        Mockito.when(ApplicationInputDataMapper.getBasicDetails(applicationData)).thenReturn(noPanNumberDetails);

        ApplicationUserInputData data = ApplicationUserInputData.getApplicationResponseData(applicationData);
        assertEquals("XYZ1234", data.getPan());
    }

    @Test
    public void testGetApplicationResponseData_bonusIncome_shouldBeSetCorrectly() throws PinakaException {
        Map<String, Object> incomeDetailsWithBonus = new HashMap<>();
        incomeDetailsWithBonus.put("bonusIncome", "120000");

        // Include minimal valid data to avoid nulls/exceptions
        Mockito.when(ApplicationInputDataMapper.getNameDetails(applicationData)).thenReturn(Collections.emptyMap());
        Mockito.when(ApplicationInputDataMapper.getBasicDetails(applicationData)).thenReturn(Collections.emptyMap());
        Mockito.when(ApplicationInputDataMapper.getIncomeDetails(applicationData)).thenReturn(incomeDetailsWithBonus);

        ApplicationUserInputData data = ApplicationUserInputData.getApplicationResponseData(applicationData);

        assertEquals("120000", data.getBonusIncome());
    }

}
