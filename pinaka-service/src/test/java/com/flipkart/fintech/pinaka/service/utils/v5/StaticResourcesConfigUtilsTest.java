package com.flipkart.fintech.pinaka.service.utils.v5;

import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

public class StaticResourcesConfigUtilsTest {
    private ConfigUtils configUtils;

    private String featureName = "default.test";

    @Mock
    private DynamicBucket dynamicBucket;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        configUtils = new ConfigUtils(dynamicBucket);
    }

    @Test
    public void testFeatureEnabledForAll() {
        when(dynamicBucket.getStringArray(featureName + PinakaConstants.ConfigFeature.FEATURE_NAME_PREFIX)).
                thenReturn(new ArrayList<>());

        boolean enabled = configUtils.isFeatureEnabled("APP202302020123213", featureName);

        Assert.assertTrue(enabled);
    }

    @Test
    public void testFeatureEnabledForCUG() {
        List<String> cugUserList = Arrays.asList("CugUser1", "CugUser2");

        when(dynamicBucket.getStringArray(featureName + PinakaConstants.ConfigFeature.FEATURE_NAME_PREFIX)).
                thenReturn(cugUserList);

        boolean enabled = configUtils.isFeatureEnabled("CugUser1", featureName);

        Assert.assertTrue(enabled);
    }

    @Test
    public void testFeatureDisabledForNonCUG() {
        List<String> cugUserList = Arrays.asList("CugUser1", "CugUser2");

        when(dynamicBucket.getStringArray(featureName + PinakaConstants.ConfigFeature.FEATURE_NAME_PREFIX)).
                thenReturn(cugUserList);

        boolean enabled = configUtils.isFeatureEnabled("CugUser3", featureName);

        Assert.assertFalse(enabled);
    }
}
