package com.flipkart.fintech.pinaka.service.pagehandler;

import com.flipkart.fintech.pinaka.service.core.v6.impl.FetchPageDataHandlerImpl;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

@Ignore("TODO")
public class PiiDataDecryptionTest {

    private ApplicationDataResponse applicationDataResponse;

    @Before
    public void setUp() throws Exception {
        InputStream inputStream = FetchPageDataHandlerImpl.class.getClassLoader().getResourceAsStream("PIIEncryptionData.json");
        applicationDataResponse = ObjectMapperUtil.get().readValue(inputStream, ApplicationDataResponse.class);
    }

    @Test
    public void decryptData() throws IOException {

        System.out.println("Before decryption:");
        System.out.println(applicationDataResponse.getApplicationData());
        Map<String, Object> data = new FormDataDecryption().decryptFormData(applicationDataResponse.getApplicationData());
        System.out.println("After decryption:");
        System.out.println(data);

    }

}

