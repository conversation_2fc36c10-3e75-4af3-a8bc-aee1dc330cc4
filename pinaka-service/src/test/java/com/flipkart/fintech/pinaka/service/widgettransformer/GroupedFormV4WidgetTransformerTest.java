package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage2FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage1FormTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import static org.testng.AssertJUnit.assertEquals;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        GroupedFormV4WidgetTransformer.class,
        LV3NamePageFormTransformer.class,
        LV3ReviewPage1FormTransformer.class,
        LV3ReviewPage2FormTransformer.class,
        InitialUserReviewDataSource.class,
        LeadPageDataSource.class
})
public class GroupedFormV4WidgetTransformerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private GroupedFormWidgetData groupedFormWidgetData;
    @Mock
    private InitialUserReviewDataSource initialUserReviewDataSource;
    @Mock
    private LeadPageDataSource leadPageDataSource;
    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    private LeadPageDataSourceResponse leadPageDataSourceResponse;
    @Mock
    LV3NamePageFormTransformer lv3NamePageFormTransformer;
    @Mock
    LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer;
    @Mock
    LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer;

    @Before
    public void setup() throws Exception {
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(applicationDataResponse)).thenReturn(reviewUserDataSourceResponse);
        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(applicationDataResponse)).thenReturn(leadPageDataSourceResponse);
    }

    @Test
    public void testLV3NamePageTransformerTest() throws Exception {
        when(lv3NamePageFormTransformer.buildWidgetGroupData(applicationDataResponse)).thenReturn(groupedFormWidgetData);

        GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer = getGroupedFormV4WidgetTransformer();
        GenericFormWidgetData groupedWidgetDataResponse = groupedFormV4WidgetTransformer.buildWidgetData("LEAD_V3_PAGE_1_FORM", applicationDataResponse);

        verify(lv3NamePageFormTransformer, times(1)).buildWidgetGroupData(applicationDataResponse);
        assertEquals(groupedFormWidgetData, groupedWidgetDataResponse);
    }

    @Test
    public void testLV3ReviewPage1Transformer() throws Exception {
        when(lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse))
                .thenReturn(groupedFormWidgetData);

        GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer = getGroupedFormV4WidgetTransformer();
        GenericFormWidgetData groupedWidgetDataResponse = groupedFormV4WidgetTransformer.buildWidgetData("LEAD_V3_PAGE_2_FORM", applicationDataResponse);

        verify(lv3ReviewPage1FormTransformer, times(1)).buildWidgetGroupData(applicationDataResponse);
        assertEquals(groupedFormWidgetData, groupedWidgetDataResponse);
    }

    @Test
    public void testLV3ReviewPage2Transformer() throws Exception {
        GenericFormWidgetData genericFormWidgetData = mock(GenericFormWidgetData.class);

        when(lv3ReviewPage2FormTransformer.buildWidgetData(applicationDataResponse)).thenReturn(genericFormWidgetData);

        GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer = getGroupedFormV4WidgetTransformer();
        GenericFormWidgetData genericFormWidgetDataResponse = groupedFormV4WidgetTransformer.buildWidgetData("LEAD_V3_PAGE_3_FORM", applicationDataResponse);

        verify(lv3ReviewPage2FormTransformer, times(1)).buildWidgetData(applicationDataResponse);
        assertEquals(genericFormWidgetData, genericFormWidgetDataResponse);
    }


    @Test(expected = PinakaException.class)
    public void testInvalidFormType() throws Exception {
        GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer = getGroupedFormV4WidgetTransformer();
        groupedFormV4WidgetTransformer.buildWidgetData("INVALID", applicationDataResponse);
    }

    @NotNull
    private GroupedFormV4WidgetTransformer getGroupedFormV4WidgetTransformer() {
        return new GroupedFormV4WidgetTransformer(
                lv3NamePageFormTransformer,
                lv3ReviewPage1FormTransformer,
                lv3ReviewPage2FormTransformer
        );
    }
}
