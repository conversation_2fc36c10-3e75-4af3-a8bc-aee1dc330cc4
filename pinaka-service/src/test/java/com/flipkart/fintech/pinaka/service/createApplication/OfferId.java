package com.flipkart.fintech.pinaka.service.createApplication;

import static org.testng.AssertJUnit.assertNotNull;
import static org.testng.AssertJUnit.assertNull;


import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.utils.CreateApplicationUtils;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

public class OfferId {

    @Mock
    private BorrowerEntity borrowerEntityWithoutOfferId;

    @Mock
    private BorrowerEntity borrowerEntityWithOfferId;

    @Mock
    private BorrowerEntity nullmetadata;

    @Before
    public void setUp() throws Exception
    {
        borrowerEntityWithoutOfferId = new BorrowerEntity();
        borrowerEntityWithOfferId = new BorrowerEntity();
        nullmetadata = new BorrowerEntity();
        String metdata = "{\"display_id\": \"ff93796114614713\", \"cohort\": \"NTB\"}";
        String metadataWithOfferId = "{\"display_id\": \"ff93796114614710\", \"cohort\": \"NTB\",\"offer_id\": \"1234\"}";
        borrowerEntityWithoutOfferId.setMetadata(metdata);
        borrowerEntityWithOfferId.setMetadata(metadataWithOfferId);
        nullmetadata.setMetadata(null);
    }
    @Test
    public void offerIdPresent()
    {
        String offerId = CreateApplicationUtils.getOfferId(Optional.of(borrowerEntityWithOfferId));
        assertNotNull(offerId);
    }

    @Test
    public void offerIdNotPresent()
    {
       String offerId =  CreateApplicationUtils.getOfferId(Optional.of(borrowerEntityWithoutOfferId));
        assertNull(offerId);
    }

    @Test
    public void nullNullmetadata()
    {
        String offerId = CreateApplicationUtils.getOfferId(Optional.of(nullmetadata));
        assertNull(offerId);
    }

}
