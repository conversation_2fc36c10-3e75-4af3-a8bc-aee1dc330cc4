package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.codahale.metrics.Meter;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.model.IncomeSource;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.codahale.metrics.MetricRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.testng.AssertJUnit.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        FormWidgetDataFetcher.class,
        PinakaMetricRegistry.class,
        MetricRegistry.class,
})
public class FormWidgetDataFetcherTest {

    @Mock
    Decrypter decrypter;
    @Mock
    LeadPageDataSourceResponse leadPageDataSourceResponse;
    @Mock
    ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    LocationRequestHandler locationRequestHandler;
    FormWidgetDataFetcher formWidgetDataFetcher = new FormWidgetDataFetcher();
    @Mock
    InitialUserDataResponse initialUserDataResponse;
    @Mock
    MetricRegistry metricRegistry;
    @Before
    public void init() {
        decrypter = mock(Decrypter.class);
        leadPageDataSourceResponse = mock(LeadPageDataSourceResponse.class);
        reviewUserDataSourceResponse = mock(ReviewUserDataSourceResponse.class);
        locationRequestHandler = mock(LocationRequestHandler.class);
        initialUserDataResponse = mock(InitialUserDataResponse.class);
        metricRegistry = mock(MetricRegistry.class);
    }

    @Test
    public void getDataForFieldsJustNameDetails() {
        Set<String> fields = new HashSet<>();
        fields.add("fullName");
        FormWidgetDataFetcher formWidgetDataFetcherSpy = spy(new FormWidgetDataFetcher());
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setFirstName("Prasoon");
        profileDetailedResponse.setLastName("Birla");
        profileDetailedResponse.setPhoneNo("9191919191");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Map<String, Object> dataForFields = formWidgetDataFetcherSpy.getDataForFields(fields, leadPageDataSourceResponse, reviewUserDataSourceResponse, decrypter, locationRequestHandler);
        assertEquals("Prasoon Birla", dataForFields.get("fullName"));
        assertEquals("9191919191", dataForFields.get("phoneNumber"));
        assertEquals(2, dataForFields.size());
    }

    @Test
    public void getDataForFields() {
        Set<String> fields = new HashSet<>();
        FormWidgetDataFetcher formWidgetDataFetcherSpy = spy(new FormWidgetDataFetcher());
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setFirstName("Prasoon");
        profileDetailedResponse.setLastName("Birla");
        profileDetailedResponse.setPhoneNo("9191919191");
        profileDetailedResponse.setDob("19/02/1998");
        profileDetailedResponse.setAddressLine1("Line1");
        profileDetailedResponse.setAddressLine2("Line2");
        profileDetailedResponse.setGender("Male");
        profileDetailedResponse.setEmail("email");
        when(decrypter.decryptString(anyString())).thenReturn("Dummy");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Map<String, Object> dataForFields = formWidgetDataFetcherSpy.getDataForFields(fields, leadPageDataSourceResponse, reviewUserDataSourceResponse, decrypter, locationRequestHandler);
        assertEquals(17, dataForFields.size());
    }

    @Test
    public void testGetFullName() {
        ProfileDetailedResponse profileDetailedResponse = spy(new ProfileDetailedResponse());
        leadPageDataSourceResponse.setIsNameEncrypted(false);
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getFirstName()).thenReturn("Prasoon");
        when(profileDetailedResponse.getLastName()).thenReturn("Birla");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("Prasoon Birla", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetFullNameFirstNameNotFound() {
        ProfileDetailedResponse profileDetailedResponse = spy(new ProfileDetailedResponse());
        leadPageDataSourceResponse.setIsNameEncrypted(false);
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getFirstName()).thenReturn("");
        when(profileDetailedResponse.getLastName()).thenReturn("Birla");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("Birla", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetFullNameLastNameNotFound() {
        ProfileDetailedResponse profileDetailedResponse = spy(new ProfileDetailedResponse());
        leadPageDataSourceResponse.setIsNameEncrypted(false);
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getFirstName()).thenReturn("Prasoon");
        when(profileDetailedResponse.getLastName()).thenReturn("");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("Prasoon", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetFullNameEncryptionEnabled() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        when(leadPageDataSourceResponse.getIsNameEncrypted()).thenReturn(true);
        profileDetailedResponse.setFirstName("Prasoon");
        profileDetailedResponse.setLastName("Birla");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("Prasoon")).thenReturn("Decrypted Prasoon");
        when(decrypter.decryptString("Birla")).thenReturn("Decrypted Birla");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("Decrypted Prasoon Decrypted Birla", fullName);
        verify(decrypter, times(2)).decryptString(anyString());
    }

    @Test
    public void testNoNameFoundInProfile() {
        when(leadPageDataSourceResponse.getIsNameEncrypted()).thenReturn(false);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setFirstName("");
        profileDetailedResponse.setLastName("");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("")).thenReturn("");
        when(decrypter.decryptString("")).thenReturn("");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetPanNumberProfileNullAndUserResponseNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String panNumber = formWidgetDataFetcher.getPanNumber(reviewUserDataSourceResponse, decrypter);
        assertEquals("", panNumber);
    }

    @Test
    public void testGetPanNumberOnlyProfileNull() {
        String expectedPan = "abcd";
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getPanNumber()).thenReturn(expectedPan);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String panNumber = formWidgetDataFetcher.getPanNumber(reviewUserDataSourceResponse, decrypter);
        assertEquals(expectedPan, panNumber);
    }

    @Test
    public void testGetPanNumberProfileHasPan() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setPan("ABCD");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString(profileDetailedResponse.getPan())).thenReturn(profileDetailedResponse.getPan());
        mockMeter();
        String panNumber = formWidgetDataFetcher.getPanNumber(reviewUserDataSourceResponse, decrypter);
        assertEquals(profileDetailedResponse.getPan(), panNumber);
        verify(decrypter, times(1)).decryptString(profileDetailedResponse.getPan());
    }

    @Test
    public void testPhoneNumberWhenProfileExists() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setPhoneNo("9191919191");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String phoneNumber = formWidgetDataFetcher.getPhoneNumber(leadPageDataSourceResponse);
        assertEquals("9191919191", phoneNumber);
    }

    @Test
    public void testPhoneNumberWhenProfileDoesNotExists() {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(null);
        String phoneNumber = formWidgetDataFetcher.getPhoneNumber(leadPageDataSourceResponse);
        assertEquals("", phoneNumber);
    }

    @Test
    public void testBonusIncomeWhenProfileDoesntExist() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        Integer phoneNumber = formWidgetDataFetcher.getBonusIncome(reviewUserDataSourceResponse);
        assertEquals(0, (int) phoneNumber);
    }

    @Test
    public void testBonusIncomeWhenProfileDoesExistButBonusIncomeIsNull() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setBonusIncome(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Integer phoneNumber = formWidgetDataFetcher.getBonusIncome(reviewUserDataSourceResponse);
        assertEquals(0, (int) phoneNumber);
    }

    @Test
    public void testBonusIncomeWhenProfileDoesExistButBonusIncomeIsValid() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setBonusIncome(1200);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Integer phoneNumber = formWidgetDataFetcher.getBonusIncome(reviewUserDataSourceResponse);
        assertEquals(1200, (int) phoneNumber);
    }

    @Test
    public void testGetGenderStringWhenProfileNotFound() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsNull() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsF() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender("F");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Female", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsM() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender("M");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsO() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender("O");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Others", gender);
    }

    @Test
    public void testGetGenderString2WhenUserDataResponseNotFound() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderString2WhenUserDataResponseNotFoundButGenderIsNull() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderString2WhenProfileFoundButGenderIsF() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender("2");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Female", gender);
    }

    @Test
    public void testGetGenderString2WhenProfileFoundButGenderIsM() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender("1");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderString2WhenProfileFoundButGenderIsO() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender("3");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Others", gender);
    }

    @Test
    public void testGetCompanyNameWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String companyName = formWidgetDataFetcher.getCompanyName(reviewUserDataSourceResponse);
        assertEquals("", companyName);
    }

    @Test
    public void testGetCompanyNameWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setCompanyName("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String companyName = formWidgetDataFetcher.getCompanyName(reviewUserDataSourceResponse);
        assertEquals("ABCD", companyName);
    }

    @Test
    public void testGetOrganizationIdWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String organizationId = formWidgetDataFetcher.getOrganizationId(reviewUserDataSourceResponse);
        assertEquals("", organizationId);
    }

    @Test
    public void testGetOrganizationIdWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setOrganizationId("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String organizationId = formWidgetDataFetcher.getOrganizationId(reviewUserDataSourceResponse);
        assertEquals("ABCD", organizationId);
    }

    @Test
    public void testGetIndustryNameWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String industryName = formWidgetDataFetcher.getOrganizationId(reviewUserDataSourceResponse);
        assertEquals("", industryName);
    }

    @Test
    public void testGetIndustryNameWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setIndustryType("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String industryName = formWidgetDataFetcher.getIndustryName(reviewUserDataSourceResponse);
        assertEquals("ABCD", industryName);
    }

    @Test
    public void testGetIndustryIdWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String industryId = formWidgetDataFetcher.getIndustryId(reviewUserDataSourceResponse);
        assertEquals("", industryId);
    }

    @Test
    public void testGetIndustryIdWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setIndustryId("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String industryId = formWidgetDataFetcher.getIndustryId(reviewUserDataSourceResponse);
        assertEquals("ABCD", industryId);
    }

    @Test
    public void testGetMonthlyIncomeWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String monthlyIncome = formWidgetDataFetcher.getMonthlyIncome(reviewUserDataSourceResponse);
        assertEquals("", monthlyIncome);
    }

    @Test
    public void testGetMonthlyIncomeWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setMonthlyIncome(123);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String monthlyIncome = formWidgetDataFetcher.getMonthlyIncome(reviewUserDataSourceResponse);
        assertEquals("123", monthlyIncome);
    }

    @Test
    public void testGetIncomeSourceWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String incomeSource = formWidgetDataFetcher.getIncomeSource(reviewUserDataSourceResponse);
        assertEquals("", incomeSource);
    }

    @Test
    public void testGetIncomeSourceWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setIncomeSource(IncomeSource.ONLINE);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String incomeSource = formWidgetDataFetcher.getIncomeSource(reviewUserDataSourceResponse);
        assertEquals(IncomeSource.ONLINE.name(), incomeSource);
    }

    @Test
    public void testGetEmploymentTypeWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String employmentType = formWidgetDataFetcher.getEmploymentType(reviewUserDataSourceResponse);
        assertEquals("", employmentType);
    }

    @Test
    public void testGetEmploymentTypeWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setEmploymentType(EmploymentType.Salaried);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String employmentType = formWidgetDataFetcher.getEmploymentType(reviewUserDataSourceResponse);
        assertEquals(EmploymentType.Salaried.name(), employmentType);
    }

    @Test
    public void testGetEmailProfileNullAndUserResponseNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String email = formWidgetDataFetcher.getEmail(reviewUserDataSourceResponse, decrypter);
        assertEquals("", email);
    }

    @Test
    public void testGetEmailOnlyProfileNull() {
        String expectedEmail = "abcd";
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getEmail()).thenReturn(expectedEmail);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String email = formWidgetDataFetcher.getEmail(reviewUserDataSourceResponse, decrypter);
        assertEquals(expectedEmail, email);
    }

    @Test
    public void testGetEmailProfileHasEmail() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setEmail("ABCD");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString(profileDetailedResponse.getEmail())).thenReturn(profileDetailedResponse.getEmail());
        mockMeter();
        String email = formWidgetDataFetcher.getEmail(reviewUserDataSourceResponse, decrypter);
        assertEquals(profileDetailedResponse.getEmail(), email);
        verify(decrypter, times(1)).decryptString(profileDetailedResponse.getEmail());
    }

    @Test
    public void testGetPinCodeWhenProfileIsNullAndUserDataResponseIsNull(){
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        PincodeDetailsResponse pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse,locationRequestHandler);
        assertNull(pincodeDetailsResponse.getPincode());
    }

    @Test
    public void testGetPinCodeWhenProfileIsNull() throws PinakaException {
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setPincode("560102");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(locationRequestHandler.checkPincodeExistence("560102")).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        PincodeDetailsResponse pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse,locationRequestHandler);
        assertNull(pincodeDetailsResponse);
    }

    @Test
    public void testGetPinCodeWhenLocationThrowsError() throws PinakaException {
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setPincode("560102");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(locationRequestHandler.checkPincodeExistence("560102")).thenThrow(new PinakaException(""));
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        PincodeDetailsResponse pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse,locationRequestHandler);
        assertEquals("560102", pincodeDetailsResponse.getPincode());
    }

    @Test
    public void testGetPinCodeWhenProfileIsNotNull() throws PinakaException {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setUserEnteredPincode(560102);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        PincodeDetailsResponse pincodeDetailsResponse2 = new PincodeDetailsResponse();
        pincodeDetailsResponse2.setPincode("560102");
        when(locationRequestHandler.checkPincodeExistence("560102")).thenReturn(pincodeDetailsResponse2);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        PincodeDetailsResponse pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse,locationRequestHandler);
        assertEquals("560102", pincodeDetailsResponse.getPincode());
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNullAndUserDataResponseIsNull(){
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNullAndAddressDetailIsNotPresent(){
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNull(){
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setAddressLine1("Line1");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNotNullAndAddressLine1IsNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine1(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNotNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine1("Line1");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("Line1")).thenReturn("Line1");
        mockMeter();
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineOne);
        verify(decrypter, times(1)).decryptString("Line1");
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNullAndUserDataResponseIsNull(){
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNullAndAddressDetailIsNotPresent(){
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNull(){
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setAddressLine2("Line1");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNotNullAndAddressLine1IsNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine2(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNotNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine2("Line1");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("Line1")).thenReturn("Line1");
        mockMeter();
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineTwo);
        verify(decrypter, times(1)).decryptString("Line1");
    }

    @Test
    public void testGetAvailableAddressesWhenInitialUserResponseIsNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        List<CAISHolderAddressDetails> availableAddresses = formWidgetDataFetcher.getAvailableAddresses(reviewUserDataSourceResponse);
        assertEquals(0, availableAddresses.size());
    }

    @Test
    public void testGetAvailableAddressesWhenExperianAddressesAreNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getExperianAddressDetails()).thenReturn(null);
        List<CAISHolderAddressDetails> availableAddresses = formWidgetDataFetcher.getAvailableAddresses(reviewUserDataSourceResponse);
        assertEquals(0, availableAddresses.size());
    }

    @Test
    public void testGetAvailableAddresses() {
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        addressDetails.add(new CAISHolderAddressDetails());
        addressDetails.add(new CAISHolderAddressDetails());
        addressDetails.add(new CAISHolderAddressDetails());
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getExperianAddressDetails()).thenReturn(addressDetails);
        List<CAISHolderAddressDetails> availableAddresses = formWidgetDataFetcher.getAvailableAddresses(reviewUserDataSourceResponse);
        assertEquals(3, availableAddresses.size());
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseIsNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsNull() {
        when(initialUserDataResponse.getDateOfBirth()).thenReturn(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsEmpty() {
        when(initialUserDataResponse.getDateOfBirth()).thenReturn("");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsInValid() {
        when(initialUserDataResponse.getDateOfBirth()).thenReturn("INVALID");
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setSmUserId("abdc");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsValid() {
        String date = "19/05/1990";
        when(initialUserDataResponse.getDateOfBirth()).thenReturn(date);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setSmUserId("abdc");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals(date, dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNotNullButDOBIsNull() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNotNullButDOBIsInvalid() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setDob("INVALID");
        when(decrypter.decryptString("INVALID")).thenReturn("INVALID");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNotNullButDOBValid() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        String dob = "19/02/1998";
        profileDetailedResponse.setDob(dob);
        when(decrypter.decryptString(dob)).thenReturn(dob);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals(dob, dateOfBirth);
    }

    private void mockMeter() {
        mockStatic(PinakaMetricRegistry.class);
        when(PinakaMetricRegistry.getMetricRegistry()).thenReturn(metricRegistry);
        mockStatic(MetricRegistry.class);
        String nameFromMetricRegistry = "name";
        Meter meter = mock(Meter.class);
        when(metricRegistry.meter(nameFromMetricRegistry)).thenReturn(meter);
        doNothing().when(meter).mark();
        when(MetricRegistry.name(any(Class.class), anyString())).thenReturn(nameFromMetricRegistry);
    }

}
