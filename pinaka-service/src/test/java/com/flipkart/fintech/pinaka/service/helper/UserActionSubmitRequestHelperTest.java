package com.flipkart.fintech.pinaka.service.helper;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataEncryption;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import org.junit.runner.RunWith;
import org.junit.Test;
import org.junit.Before;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class UserActionSubmitRequestHelperTest {

    private FormDataEncryption formDataEncryption;
    private FormDataDecryption formDataDecryption;
    private FormSubmitRequest formSubmitRequest;

    @Before
    public void setup() {
        formDataEncryption = mock(FormDataEncryption.class);
        formDataDecryption = mock(FormDataDecryption.class);
        formSubmitRequest = mock(FormSubmitRequest.class);
    }

    @Test
    public void testAdjustAnnualIncome_ValidIncome_SelfEmployed() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "5000");
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getTaskKey()).thenReturn("leadV3Page1");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // When
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);

        // Then
        assertEquals(60000, formData.get("annualTurnOver"));
    }

    @Test(expected = PinakaException.class)
    public void testAdjustAnnualIncome_InvalidIncome_ThrowsException() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "invalid");
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getTaskKey()).thenReturn("leadV3Page1");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // Then
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);
    }

    @Test(expected = PinakaException.class)
    public void testAdjustAnnualIncome_IncomeNotString_ThrowsException() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", 5000); // Not a string
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getTaskKey()).thenReturn("leadV3Page1");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // Then
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);
    }

    @Test
    public void testAdjustAnnualIncome_NotSelfEmployed_NoChange() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "5000");
        formData.put("employmentType", "Salaried");
        when(formSubmitRequest.getTaskKey()).thenReturn("leadV3Page1");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // When
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);

        // Then
        assertFalse(formData.containsKey("annualTurnOver"));
    }

    @Test
    public void testAdjustAnnualIncome_TaskKeyNotMatching_NoChange() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "5000");
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getTaskKey()).thenReturn("otherTaskKey");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // When
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);

        // Then
        assertFalse(formData.containsKey("annualTurnOver"));
    }

    @Test
    public void testAdjustFullName_ValidName_SplitsCorrectly() throws PinakaException {
        // Given
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("fullName", "John Doe");
        request.setFormData(formData);

        when(formDataDecryption.getDecryptedPlainTextString("John Doe")).thenReturn("John Doe");
        when(formDataEncryption.encryptString("John")).thenReturn("encryptedFirstName");
        when(formDataEncryption.encryptString("Doe")).thenReturn("encryptedLastName");

        // When
        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        // Then
        assertEquals("encryptedFirstName", request.getFormData().get("firstName"));
        assertEquals("encryptedLastName", request.getFormData().get("lastName"));
        assertFalse(request.getFormData().containsKey("fullName"));
    }

    @Test
    public void testAdjustFullName_SingleWordName_SameFirstAndLast() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("fullName", "Madonna");
        request.setFormData(formData);

        when(formDataDecryption.getDecryptedPlainTextString("Madonna")).thenReturn("Madonna");
        when(formDataEncryption.encryptString("Madonna")).thenReturn("encryptedMadonna");

        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        assertEquals("encryptedMadonna", request.getFormData().get("firstName"));
        assertEquals("encryptedMadonna", request.getFormData().get("lastName"));
        assertFalse(request.getFormData().containsKey("fullName"));
    }

    @Test(expected = PinakaException.class)
    public void testAdjustFullName_InvalidEmptyName_ThrowsException() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("fullName", "");
        request.setFormData(formData);
        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);
    }

    @Test
    public void testAdjustOrganizationName_CopiesAndRemovesBusinessName() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("businessName", "Acme Corp");
        request.setFormData(formData);

        // Only test organization logic
        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        assertEquals("Acme Corp", request.getFormData().get("organization"));
        assertFalse(request.getFormData().containsKey("businessName"));
    }

    @Test
    public void testAdjustOrganizationName_NoBusinessName_NoChange() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        request.setFormData(formData);

        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        assertFalse(request.getFormData().containsKey("organization"));
    }
}
