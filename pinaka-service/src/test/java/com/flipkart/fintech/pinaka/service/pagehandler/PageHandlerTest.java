package com.flipkart.fintech.pinaka.service.pagehandler;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.api.response.deserializer.AccordianWidgetDeserializer;
import com.flipkart.fintech.pinaka.api.response.idfc.WidgetEntity;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.MockHelper;
import com.flipkart.fintech.pinaka.service.core.v6.DocumentService;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.core.v6.impl.FetchPageDataHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.*;
import com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan.RepeatLoanOfferPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan.response.RepeatLoanOfferPageData;
import com.flipkart.fintech.pinaka.service.response.ApplicationStatusPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.KFSDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.*;
import com.flipkart.fintech.pinaka.service.widgettransformer.landingPages.OfferDetailsLpTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage2FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformerFactory;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.*;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.gamifiedOnboarding.StepperWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.mapiWidgetData.MarkupWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import com.supermoney.ams.bridge.AmsBridge;
import com.supermoney.ams.bridge.dao.FileBasedConfigurationDao;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import static org.testng.AssertJUnit.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        PageHandler.class,
        FormWidgetTransformer.class,
        KycDetailsTransformer.class,
        UrlUtil.class,
        ApplicationState.class,
        ApplicationStatusWidgetTransformer.class,
        KFSWidgetTransformer.class,
        KFSDetailsPageDataSource.class,
        GroupedFormV4WidgetTransformer.class,
        LV3NamePageFormTransformer.class,
        LV3ReviewPage1FormTransformer.class,
        LV3ReviewPage2FormTransformer.class,
        InitialUserReviewDataSource.class,
        LeadPageDataSource.class,
        FileBasedConfigurationDao.class,
        OfferScreenTransformer.class,
        RejectScreenTransformer.class,
        ObjectMapperUtil.class
})
public class PageHandlerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private AmsBridge amsBridge;
    @Mock
    private DocumentService documentService;
    @Mock
    RepeatLoanOfferPageDataSource repeatLoanOfferPageDataSource;
    @Mock
    RepeatLoanOfferPageTransformer repeatLoanOfferPageTransformer;
    @Mock
    SubmitButtonWidgetTransformerFactory submitButtonWidgetTransformerFactory;
    @Mock
    SandboxOfferScreenTransformer sandboxOfferScreenTransformer;
    @Mock
    DynamicBucket dynamicBucket;
    @Mock
    Decrypter decrypter;
    @Mock
    ErrorScreenTransformer errorScreenTransformer;
    @Mock
    PlClient plClient;
    @Mock
    SuccessScreenTransformer successScreenTransformer;
    @Mock
    CustomScreenTransformer customScreenTransformer;
    @Mock
    LocationRequestHandler locationRequestHandler;
    @Mock
    PageDataSourceFactory pageDataSourceFactory;
    @Mock
    ConfigUtils configUtils;
    @Mock
    GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer;
    @Mock CardWidgetTransformer cardWidgetTransformer;

    FormWidgetDataPrefillUtils formWidgetDataPrefillUtils = new FormWidgetDataPrefillUtils();
    FormWidgetDataJsonParser formWidgetDataJsonParser = new FormWidgetDataJsonParser();
    FormWidgetDataFetcher formWidgetDataFetcher = new FormWidgetDataFetcher();

    String mockContext = "";

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        InputStream inputStream = FetchPageDataHandlerImpl.class.getClassLoader().getResourceAsStream("OfferScreenData.json");
        applicationDataResponse = ObjectMapperUtil.get().readValue(inputStream, ApplicationDataResponse.class);
        FileBasedConfigurationDao configurationDao = new FileBasedConfigurationDao();

        when(configUtils.getEncryptionData()).thenReturn(Optional.empty());
        PowerMockito.mockStatic(UrlUtil.class);
        PowerMockito.when(UrlUtil.class, "createEncryptedToken", any()).thenReturn("abcdadlaskdjfljlkajsdf;lakjf");
        MockHelper.setFinalStatic(BasicDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(OfferScreenDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(OtpVerificationPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(KycDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(LiveSelfiePageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(KFSDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(KFSOtpPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(RepaymentModesDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(AadharFormPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        amsBridge = mock(AmsBridge.class);
        documentService = mock(DocumentService.class);
        MockHelper.setFinalStatic(BankDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);
        MockHelper.setFinalStatic(ApplicationStatusPageDataSource.class.getDeclaredField("configUtils"), configUtils);
    }

    @Test
    public void createPageHandlerResponse_Success() throws Exception {
        // Arrange
        String merchantId = "testMerchant";
        List<SlotInfo> slotInfoList = new ArrayList<>();
        SlotInfo slotInfo1 = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        SlotInfo slotInfo2 = new SlotInfo(2, WidgetTypeV4.FORM_V4);
        slotInfoList.add(slotInfo1);
        slotInfoList.add(slotInfo2);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("id", "appId", "token", slotInfoList, null);
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);

        WidgetEntity widgetEntity1 = new WidgetEntity(1, WidgetTypeV4.TEXT_V2, mock(WidgetData.class));
        WidgetEntity widgetEntity2 = new WidgetEntity(2, WidgetTypeV4.FORM_V4, mock(WidgetData.class));

        PageHandler spyPageHandler = PowerMockito.spy(getPageHandler());
        PowerMockito.doReturn(widgetEntity1)
                .when(spyPageHandler, "getWidgetEntity", eq(applicationDataResponse), eq(slotInfo1), eq(merchantId));
        PowerMockito.doReturn(widgetEntity2)
                .when(spyPageHandler, "getWidgetEntity", eq(applicationDataResponse), eq(slotInfo2), eq(merchantId));

        // Act
        List<WidgetEntity> result = spyPageHandler.createPageHandlerResponse(merchantId, pageServiceRequest, applicationDataResponse);

        // Assert
        assertEquals(2, result.size());
        assertEquals(widgetEntity1, result.get(0));
        assertEquals(widgetEntity2, result.get(1));
        PowerMockito.verifyPrivate(spyPageHandler, times(2))
                .invoke("getWidgetEntity", any(ApplicationDataResponse.class), any(SlotInfo.class), anyString());
    }

    @Test
    public void createPageHandlerResponse_WithNullWidget() throws Exception {
        // Arrange
        String merchantId = "testMerchant";
        List<SlotInfo> slotInfoList = new ArrayList<>();
        SlotInfo slotInfo1 = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        SlotInfo slotInfo2 = new SlotInfo(2, WidgetTypeV4.FORM_V4);
        slotInfoList.add(slotInfo1);
        slotInfoList.add(slotInfo2);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("id", "appId", "token", slotInfoList, null);
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);

        WidgetEntity widgetEntity1 = new WidgetEntity(1, WidgetTypeV4.TEXT_V2, mock(WidgetData.class));

        PageHandler spyPageHandler = PowerMockito.spy(getPageHandler());
        PowerMockito.doReturn(widgetEntity1)
                .when(spyPageHandler, "getWidgetEntity", eq(applicationDataResponse), eq(slotInfo1), eq(merchantId));
        PowerMockito.doReturn(null)
                .when(spyPageHandler, "getWidgetEntity", eq(applicationDataResponse), eq(slotInfo2), eq(merchantId));

        // Act
        List<WidgetEntity> result = spyPageHandler.createPageHandlerResponse(merchantId, pageServiceRequest, applicationDataResponse);

        // Assert
        assertEquals(1, result.size());
        assertEquals(widgetEntity1, result.get(0));
        PowerMockito.verifyPrivate(spyPageHandler, times(2))
                .invoke("getWidgetEntity", any(ApplicationDataResponse.class), any(SlotInfo.class), anyString());
    }

    @Test
    public void createPageHandlerResponse_EmptySlotList() throws Exception {
        // Arrange
        String merchantId = "testMerchant";
        List<SlotInfo> slotInfoList = new ArrayList<>();
        PageServiceRequest pageServiceRequest = new PageServiceRequest("id", "appId", "token", slotInfoList, null);
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);

        PageHandler spyPageHandler = PowerMockito.spy(getPageHandler());

        // Act
        List<WidgetEntity> result = spyPageHandler.createPageHandlerResponse(merchantId, pageServiceRequest, applicationDataResponse);

        // Assert
        assertTrue(result.isEmpty());
        PowerMockito.verifyPrivate(spyPageHandler, never())
                .invoke("getWidgetEntity", any(ApplicationDataResponse.class), any(SlotInfo.class), anyString());
    }

    @Test(expected = PinakaException.class)
    public void createPageHandlerResponse_ThrowsException() throws Exception {
        // Arrange
        String merchantId = "testMerchant";
        List<SlotInfo> slotInfoList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        slotInfoList.add(slotInfo);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("id", "appId", "token", slotInfoList, null);
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);

        PageHandler spyPageHandler = PowerMockito.spy(getPageHandler());
        PowerMockito.doThrow(new PinakaException("Test exception"))
                .when(spyPageHandler, "getWidgetEntity", eq(applicationDataResponse), eq(slotInfo), eq(merchantId));

        // Act
        spyPageHandler.createPageHandlerResponse(merchantId, pageServiceRequest, applicationDataResponse);
    }

    @Test
    public void basicDetailsTest() throws Exception {
        // Mocks
        when(dynamicBucket.getBoolean(anyString())).thenReturn(false);
        FormWidgetTransformer mockedFormWidgetTransformer = mock(FormWidgetTransformer.class);
        whenNew(FormWidgetTransformer.class)
                .withArguments(any(SandboxOfferScreenTransformer.class), any(Map.class), any(Map.class), any(Map.class), any(Map.class), any(DynamicBucket.class), any(Decrypter.class))
                .thenReturn(mockedFormWidgetTransformer);
        when(mockedFormWidgetTransformer.buildWidgetData("IDFC_BASIC_DETAILS_FORM", applicationDataResponse)).thenReturn(new GenericFormWidgetData());

        // action
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        slotInfo.setContentId("LOGO");
        widgetTypeList.add(slotInfo);
        slotInfo = new SlotInfo(2, WidgetTypeV4.FORM_V4, "IDFC_BASIC_DETAILS_FORM");
        widgetTypeList.add(slotInfo);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, null);
        List<WidgetEntity> pageResponse = getPageHandler()
                .createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        System.out.println(fetchBulkDataResponseV2);
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);

        // assert
        assertEquals(RichMessageWidgetData.class, pageResponse.get(0).getWidgetData().getClass());
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }

    @Test
    public void offerWaitScreen() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.LOADING_WIDGET);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        pageServiceRequest.setSlotInfoList(widgetTypeList);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(LoadingWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void announcementCardTest() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        AnnouncementCardWidgetData announcementCardWidgetData = new AnnouncementCardWidgetData();
        OfferDetailsLpTransformer offerDetailsLpTransformer = mock(OfferDetailsLpTransformer.class);
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        RichMessageWidgetData richMessageWidgetData = new RichMessageWidgetData();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);

        when(pageDataSourceFactory.getAnnouncementCardWidgetTransformer(any(ApplicationDataResponse.class), any(SlotInfo.class)))
                .thenReturn(offerDetailsLpTransformer);
        when(offerDetailsLpTransformer.buildAnnouncementCardWidgetData(applicationDataResponse)).thenReturn(announcementCardWidgetData);
        when(applicationStatusWidgetTransformer.buildtextV2WidgetData(applicationStatusPageDataSourceResponse)).thenReturn(richMessageWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_CARD);
        slotInfo.setContentId("LOGO");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("applicationStatus");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementCardWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementCardWidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void approvedOfferCard() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ApprovedOfferWidgetData approvedOfferWidgetData = new ApprovedOfferWidgetData();
        OfferDetailsLpTransformer offerDetailsLpTransformer = mock(OfferDetailsLpTransformer.class);
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        RichMessageWidgetData richMessageWidgetData = new RichMessageWidgetData();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);

        when(pageDataSourceFactory.getApprovedOfferWidgetTransformer(any(ApplicationDataResponse.class), any(SlotInfo.class)))
                .thenReturn(offerDetailsLpTransformer);
        when(offerDetailsLpTransformer.buildApprovedOfferWidgetData(applicationDataResponse)).thenReturn(approvedOfferWidgetData);
        when(applicationStatusWidgetTransformer.buildtextV2WidgetData(applicationStatusPageDataSourceResponse)).thenReturn(richMessageWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.APPROVED_OFFER);
        slotInfo.setContentId("LOGO");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("prasoonb", "prasoonb", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("applicationStatus");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(ApprovedOfferWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(approvedOfferWidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void richMultiImageBanner() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        RichMultiImageBannerWidgetData richMultiImageBannerWidgetData = new RichMultiImageBannerWidgetData();
        RepeatLoanOfferPageData repeatLoanOfferPageData = mock(RepeatLoanOfferPageData.class);
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        RichMessageWidgetData richMessageWidgetData = new RichMessageWidgetData();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);

        when(repeatLoanOfferPageDataSource.getData(applicationDataResponse)).thenReturn(repeatLoanOfferPageData);
        when(repeatLoanOfferPageTransformer.buildRepeatLoanOfferMultiImageBannerWidgetData(repeatLoanOfferPageData)).thenReturn(richMultiImageBannerWidgetData);
        when(applicationStatusWidgetTransformer.buildtextV2WidgetData(applicationStatusPageDataSourceResponse)).thenReturn(richMessageWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.RICH_MULTI_IMAGE_BANNER);
        slotInfo.setContentId("LOGO");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("prasoonb", "prasoonb", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("applicationStatus");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMultiImageBannerWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(richMultiImageBannerWidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void markupWidgetTransformer() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.MARKUP);
        slotInfo.setContentId("LOGO");
        slotInfo.setStaticContentId("scapic-axis-tnc");
        widgetTypeList.add(slotInfo);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("prasoonb", "prasoonb", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("applicationStatus");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(MarkupWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void otpVerificationTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "OTP_VERIFICATION_SCREEN");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationDataResponse applicationDataResponse;
        InputStream inputStream = FetchPageDataHandlerImpl.class.getClassLoader().getResourceAsStream("OfferScreenData.json");
        applicationDataResponse = ObjectMapperUtil.get().readValue(inputStream, ApplicationDataResponse.class);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void repaymentModesTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "REPAYMENT_MODES");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void aadharFormTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "AADHAR_FORM");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void liveSelfieTest()
            throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.LIVE_SELFIE_WIDGET);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(LiveSelfieWidgetData.class,
                fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }


    @Test
    public void kycDetailsTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        widgetTypeList.add(slotInfo);
        slotInfo = new SlotInfo(2, WidgetTypeV4.KEY_VALUE);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("CKYC_DETAILS");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(KeyValueWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }

    @Test
    public void ekycDetailsTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        widgetTypeList.add(slotInfo);
        slotInfo = new SlotInfo(2, WidgetTypeV4.KEY_VALUE);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("EKYC_DETAILS");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(KeyValueWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }


    @Test(expected = PinakaException.class)
    public void expectedExceptionTest() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        // WidgetTypeV4.INFO not supported
        SlotInfo slotInfo = new SlotInfo(6, WidgetTypeV4.INFO);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("kfsScreen");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(SubmitButtonWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }

    @Test
    public void expectedNullWidgetsTest() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(6, WidgetTypeV4.GROUPED_FORM_V4);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(0, fetchBulkDataResponseV21.getWidgetEntityList().size());
    }

    @Test(expected = PinakaException.class)
    public void noWidgetsTest() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        // WidgetTypeV4.INFO not supported
        SlotInfo slotInfo = new SlotInfo(6, WidgetTypeV4.INFO);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("kfsScreen");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(SubmitButtonWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }

    @Test
    public void kfsScreenSubmitButtonTest() throws Exception {
        KFSDetailsPageDataSource kfsDetailsPageDataSource = mock(KFSDetailsPageDataSource.class);
        KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse = new KFSDetailsPageDataSourceResponse();
        KFSWidgetTransformer kfsWidgetTransformer = mock(KFSWidgetTransformer.class);
        RichMessageWidgetData richMessageWidgetData = new RichMessageWidgetData();

        whenNew(KFSDetailsPageDataSource.class).withNoArguments().thenReturn(kfsDetailsPageDataSource);

        MockHelper.setFinalStatic(KFSDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);

        when(kfsDetailsPageDataSource.getData(applicationDataResponse)).thenReturn(kfsDetailsPageDataSourceResponse);

        whenNew(KFSWidgetTransformer.class).withNoArguments().thenReturn(kfsWidgetTransformer);
        when(configUtils.getEncryptionData()).thenReturn(Optional.empty());

        when(kfsWidgetTransformer.buildtextWidgetData(kfsDetailsPageDataSourceResponse))
                .thenReturn(richMessageWidgetData);
        SubmitButtonWidgetData submitButtonWidgetData = new SubmitButtonWidgetData();

        SubmitButtonWidgetTransformer submitButtonWidgetTransformer = mock(SubmitButtonWidgetTransformer.class);

        when(submitButtonWidgetTransformerFactory.getSubmitButtonWidgetTransformer(null, applicationDataResponse))
                .thenReturn(submitButtonWidgetTransformer);

        when(submitButtonWidgetTransformer.buildSubmitButtonWidgetData(any(ApplicationDataResponse.class)))
                .thenReturn(submitButtonWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        widgetTypeList.add(slotInfo);
        slotInfo = new SlotInfo(6, WidgetTypeV4.SUBMIT_BUTTON_WIDGET);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("KFS_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(SubmitButtonWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }

    @Test
    public void kfsScreenTestHtmlShowcase() throws Exception {
        KFSDetailsPageDataSource kfsDetailsPageDataSource = mock(KFSDetailsPageDataSource.class);
        KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse = new KFSDetailsPageDataSourceResponse();
        KFSWidgetTransformer kfsWidgetTransformer = mock(KFSWidgetTransformer.class);
        RichMessageWidgetData richMessageWidgetData = new RichMessageWidgetData();

        whenNew(KFSDetailsPageDataSource.class).withNoArguments().thenReturn(kfsDetailsPageDataSource);

        MockHelper.setFinalStatic(KFSDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);

        when(kfsDetailsPageDataSource.getData(applicationDataResponse)).thenReturn(kfsDetailsPageDataSourceResponse);

        whenNew(KFSWidgetTransformer.class).withNoArguments().thenReturn(kfsWidgetTransformer);
        when(configUtils.getEncryptionData()).thenReturn(Optional.empty());

        when(kfsWidgetTransformer.buildtextWidgetData(kfsDetailsPageDataSourceResponse))
                .thenReturn(richMessageWidgetData);
        StaticHtmlShowcaseWidgetData staticHtmlShowcaseWidgetData = new StaticHtmlShowcaseWidgetData();

        when(kfsWidgetTransformer.buildHtmlWidgetData(kfsDetailsPageDataSourceResponse))
                .thenReturn(staticHtmlShowcaseWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        widgetTypeList.add(slotInfo);
        slotInfo = new SlotInfo(5, WidgetTypeV4.STATIC_HTML_SHOWCASE);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("KFS_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(StaticHtmlShowcaseWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(1).getWidgetData().getClass());
    }

    @Test
    public void kfsScreenTableWidgetTest() throws Exception {
        KFSDetailsPageDataSource kfsDetailsPageDataSource = mock(KFSDetailsPageDataSource.class);
        KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse = new KFSDetailsPageDataSourceResponse();
        KFSWidgetTransformer kfsWidgetTransformer = mock(KFSWidgetTransformer.class);
        TableWidgetData tableWidgetData = new TableWidgetData();

        whenNew(KFSDetailsPageDataSource.class).withNoArguments().thenReturn(kfsDetailsPageDataSource);

        MockHelper.setFinalStatic(KFSDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);

        when(kfsDetailsPageDataSource.getData(applicationDataResponse)).thenReturn(kfsDetailsPageDataSourceResponse);

        whenNew(KFSWidgetTransformer.class).withNoArguments().thenReturn(kfsWidgetTransformer);
        when(configUtils.getEncryptionData()).thenReturn(Optional.empty());

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TABLE_V4);
        slotInfo.setContentId("TABLE_1");
        widgetTypeList.add(slotInfo);


        when(kfsWidgetTransformer.buildtableWidgetData(kfsDetailsPageDataSourceResponse, widgetTypeList.get(0)))
                .thenReturn(tableWidgetData);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("KFS_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("",pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(TableWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test(expected = PinakaException.class)
    public void kfsScreenTableWidgetTestInvalidInteractionKey() throws Exception {
        KFSDetailsPageDataSource kfsDetailsPageDataSource = mock(KFSDetailsPageDataSource.class);
        KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse = new KFSDetailsPageDataSourceResponse();
        KFSWidgetTransformer kfsWidgetTransformer = mock(KFSWidgetTransformer.class);
        TableWidgetData tableWidgetData = new TableWidgetData();

        whenNew(KFSDetailsPageDataSource.class).withNoArguments().thenReturn(kfsDetailsPageDataSource);

        MockHelper.setFinalStatic(KFSDetailsPageDataSource.class.getDeclaredField("configUtils"), configUtils);

        when(kfsDetailsPageDataSource.getData(applicationDataResponse)).thenReturn(kfsDetailsPageDataSourceResponse);

        whenNew(KFSWidgetTransformer.class).withNoArguments().thenReturn(kfsWidgetTransformer);
        when(configUtils.getEncryptionData()).thenReturn(Optional.empty());

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TABLE_V4);
        slotInfo.setContentId("TABLE_1");
        widgetTypeList.add(slotInfo);


        when(kfsWidgetTransformer.buildtableWidgetData(kfsDetailsPageDataSourceResponse, widgetTypeList.get(0)))
                .thenReturn(tableWidgetData);

        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("INVALID");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("",pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(TableWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void kfsOTPtest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "KFS_OTP");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void bankDetailsTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "BANK_DETAILS");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationDataResponse applicationDataResponse;
        InputStream inputStream = FetchPageDataHandlerImpl.class.getClassLoader().getResourceAsStream("OfferScreenData.json");
        applicationDataResponse = ObjectMapperUtil.get().readValue(inputStream, ApplicationDataResponse.class);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void applicationStatusTest() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        BannerWidgetData bannerWidgetData = new BannerWidgetData();
        BannerWidgetTransformer bannerWidgetTransformer = mock(BannerWidgetTransformer.class);
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        RichMessageWidgetData richMessageWidgetData = new RichMessageWidgetData();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);

        when(pageDataSourceFactory.getBannerWidgetTransformer(applicationDataResponse, "APPLICATION_STATUS", dynamicBucket))
                .thenReturn(bannerWidgetTransformer);
        when(bannerWidgetTransformer.buildBannerWidgetData(applicationDataResponse))
                .thenReturn(bannerWidgetData);
        when(applicationStatusWidgetTransformer.buildtextV2WidgetData(applicationStatusPageDataSourceResponse)).thenReturn(richMessageWidgetData);

        module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TEXT_V2);
        slotInfo.setContentId("LOGO");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("APPLICATION_STATUS");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(RichMessageWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void applicationStatusStepperWidgetTest() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        StepperWidgetData stepperWidgetData = new StepperWidgetData();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);

        when(applicationStatusPageDataSource.getData(applicationDataResponse)).thenReturn(applicationStatusPageDataSourceResponse);
        when(applicationStatusWidgetTransformer.buildstepperWidgetData(applicationStatusPageDataSourceResponse)).thenReturn(stepperWidgetData);

        module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo;
        slotInfo = new SlotInfo(0, WidgetTypeV4.STEPPER_WIDGET);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("applicationStatus");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(StepperWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void applicationStatusAccordionWidgetTest() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        ApplicationStatusWidgetTransformer applicationStatusWidgetTransformer = mock(ApplicationStatusWidgetTransformer.class);
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse = new ApplicationStatusPageDataSourceResponse();
        AccordionWidgetData accordionWidgetData = new AccordionWidgetData();
        ApplicationStatusPageDataSource applicationStatusPageDataSource = mock(ApplicationStatusPageDataSource.class);

        whenNew(ApplicationStatusPageDataSourceResponse.class).withNoArguments().thenReturn(applicationStatusPageDataSourceResponse);
        whenNew(ApplicationStatusPageDataSource.class).withNoArguments().thenReturn(applicationStatusPageDataSource);
        whenNew(ApplicationStatusWidgetTransformer.class).withNoArguments().thenReturn(applicationStatusWidgetTransformer);

        when(applicationStatusPageDataSource.getData(applicationDataResponse)).thenReturn(applicationStatusPageDataSourceResponse);
        when(applicationStatusWidgetTransformer.buildaccordianWidgetData(applicationStatusPageDataSourceResponse)).thenReturn(accordionWidgetData);

        module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo;
        slotInfo = new SlotInfo(0, WidgetTypeV4.ACCORDION_V4);
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("applicationStatus");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        String json = mapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = mapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AccordionWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void accordianJackSon() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
        mapper.registerModule(module);
        String accordianWidgetJson = TransformerUtils.readFileasString("template/idfc/ApplicationStatusAccordian.json");
        AccordionWidgetData accordionWidgetData = mapper.readValue(accordianWidgetJson, AccordionWidgetData.class);
        assertEquals(AccordionWidgetData.class, accordionWidgetData.getClass());
    }

    @Test
    public void repaymentScheduleTest()
            throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.TABLE_V4);
        slotInfo.setContentId("REPAYMENT_SCHEDULE");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, mockContext);
        ApplicationDataResponse applicationDataResponse;
        InputStream inputStream = FetchPageDataHandlerImpl.class.getClassLoader().getResourceAsStream("OfferScreenData.json");
        applicationDataResponse = ObjectMapperUtil.get().readValue(inputStream, ApplicationDataResponse.class);
        applicationDataResponse.getPendingTask().get(0).setTaskKey("repaymentSchedule");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(TableWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    //     todo: rohan arunav
    @Test
    public void emandateRetryTest() throws IOException, PinakaException, PinakaClientException {

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        when(errorScreenTransformer.buildemandateWaitingScreen()).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        ApplicationState state = mock(ApplicationState.class);
        mockStatic(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("EMANDATE_RETRY_TIMER");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    @Test
    public void announcementWidgetOfferScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        OfferScreenTransformer offerScreenTransformer = mock(OfferScreenTransformer.class);
        whenNew(OfferScreenTransformer.class).withNoArguments().thenReturn(offerScreenTransformer);
        when(offerScreenTransformer.buildOfferScreen(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("OFFER_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetRejectScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        RejectScreenTransformer rejectScreenTransformer = mock(RejectScreenTransformer.class);
        whenNew(RejectScreenTransformer.class).withNoArguments().thenReturn(rejectScreenTransformer);
        when(rejectScreenTransformer.buildRejectScreen(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("REJECT_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetGenerateEbpOtpScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        ETBAnnouncementTransformer etbAnnouncementTransformer = mock(ETBAnnouncementTransformer.class);
        whenNew(ETBAnnouncementTransformer.class).withNoArguments().thenReturn(etbAnnouncementTransformer);
        when(etbAnnouncementTransformer.buildWidgetData()).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("GENERATE_ETB_OTP_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetEbpOtpVerificationScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        ETBAnnouncementTransformer etbAnnouncementTransformer = mock(ETBAnnouncementTransformer.class);
        whenNew(ETBAnnouncementTransformer.class).withNoArguments().thenReturn(etbAnnouncementTransformer);
        when(etbAnnouncementTransformer.buildVerifyOtpWidgetData()).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("ETB_OTP_VERIFICATION");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetRetryScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        RetryScreenWidgetTransformer retryScreenWidgetTransformer = mock(RetryScreenWidgetTransformer.class);
        whenNew(RetryScreenWidgetTransformer.class).withNoArguments().thenReturn(retryScreenWidgetTransformer);
        when(retryScreenWidgetTransformer.buildRetryScreen(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("RETRY_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetRetryScreenOffer() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        RetryScreenWidgetTransformer retryScreenWidgetTransformer = mock(RetryScreenWidgetTransformer.class);
        whenNew(RetryScreenWidgetTransformer.class).withNoArguments().thenReturn(retryScreenWidgetTransformer);
        when(retryScreenWidgetTransformer.buildRetryScreen(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("RETRY_SCREEN_OFFER");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetRetryScreen1() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        RetryScreenWidgetTransformer retryScreenWidgetTransformer = mock(RetryScreenWidgetTransformer.class);
        whenNew(RetryScreenWidgetTransformer.class).withNoArguments().thenReturn(retryScreenWidgetTransformer);
        when(retryScreenWidgetTransformer.buildRetryScreen(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("RETRY_SCREEN_1");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetRetryScreen2() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        RetryScreenWidgetTransformer retryScreenWidgetTransformer = mock(RetryScreenWidgetTransformer.class);
        whenNew(RetryScreenWidgetTransformer.class).withNoArguments().thenReturn(retryScreenWidgetTransformer);
        when(retryScreenWidgetTransformer.buildRetryScreen(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("RETRY_SCREEN_2");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetOnboardRetryScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        OnBoardRetryAnnouncementTransformer onBoardRetryAnnouncementTransformer = mock(OnBoardRetryAnnouncementTransformer.class);
        whenNew(OnBoardRetryAnnouncementTransformer.class).withNoArguments().thenReturn(onBoardRetryAnnouncementTransformer);
        when(onBoardRetryAnnouncementTransformer.buildWidgetData(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("ONBOARD_RETRY_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }


    @Test
    public void announcementWidgetSuccessScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        when(successScreenTransformer.buildWidgetData(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("SUCCESS");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetCustomScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        when(customScreenTransformer.buildWidgetData(applicationDataResponse)).thenReturn(announcementV2WidgetData);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("CUSTOM_SCREEN");
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetRejectedScreen() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("REJECTED");
        when(errorScreenTransformer.buildofferRejectionErrorWidgetData(applicationDataResponse, slotInfo)).thenReturn(announcementV2WidgetData);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void announcementWidgetCreateProfileEnd() throws Exception {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.ANNOUNCEMENT_V2);
        widgetTypeList.add(slotInfo);
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        mockStatic(ApplicationState.class);
        ApplicationState state = mock(ApplicationState.class);
        when(ApplicationState.create(applicationDataResponse)).thenReturn(state);
        when(amsBridge.getInteractionKey(state)).thenReturn("CREATE_PROFILE_END");
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        mockStatic(ObjectMapperUtil.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        when(mockObjectMapper.readValue(anyString(), any(Class.class))).thenReturn(announcementV2WidgetData);
        List<WidgetEntity> pageResponse = getPageHandler().createPageHandlerResponse("", pageServiceRequest, applicationDataResponse);
        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(AnnouncementV2WidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(announcementV2WidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void offerScreenTest() throws IOException, PinakaException {
        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "OFFER_SCREEN");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        List<WidgetEntity> pageResponse = getWidgetEntities(pageServiceRequest);

        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
    }

    private List<WidgetEntity> getWidgetEntities(PageServiceRequest pageServiceRequest) throws PinakaException {
        return getPageHandler()
                .createPageHandlerResponse("merchantId", pageServiceRequest, applicationDataResponse);
    }

    @Test
    public void testLv3NamePageFormWidgetTransformer() throws Exception {
        GroupedFormWidgetData groupedFormWidgetData = new GroupedFormWidgetData();
        when(groupedFormV4WidgetTransformer.buildWidgetData("LEAD_V3_PAGE_1_FORM", applicationDataResponse)).thenReturn(groupedFormWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.GROUPED_FORM_V4, "LEAD_V3_PAGE_1_FORM");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        List<WidgetEntity> pageResponse = getWidgetEntities(pageServiceRequest);

        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);

        verify(groupedFormV4WidgetTransformer, times(1)).buildWidgetData("LEAD_V3_PAGE_1_FORM", applicationDataResponse);
        assertEquals(GroupedFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(groupedFormWidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @Test
    public void testLv3Page3FormWidgetTransformer() throws Exception {
        GenericFormWidgetData genericFormWidgetData = new GenericFormWidgetData();
        when(groupedFormV4WidgetTransformer.buildWidgetData("LEAD_V3_PAGE_3_FORM", applicationDataResponse)).thenReturn(genericFormWidgetData);

        List<SlotInfo> widgetTypeList = new ArrayList<>();
        SlotInfo slotInfo = new SlotInfo(1, WidgetTypeV4.FORM_V4, "LEAD_V3_PAGE_3_FORM");
        widgetTypeList.add(slotInfo);
        PageServiceRequest pageServiceRequest = new PageServiceRequest("asdkashdlajsdl", "asdkashdlajsdl", "131o237123", widgetTypeList, "123");
        List<WidgetEntity> pageResponse = getWidgetEntities(pageServiceRequest);

        FetchBulkDataResponseV2 fetchBulkDataResponseV2 = new FetchBulkDataResponseV2(pageResponse);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(fetchBulkDataResponseV2);
        System.out.println(json);
        FetchBulkDataResponseV2 fetchBulkDataResponseV21 = objectMapper.readValue(json, FetchBulkDataResponseV2.class);

        verify(groupedFormV4WidgetTransformer, times(1)).buildWidgetData("LEAD_V3_PAGE_3_FORM", applicationDataResponse);
        assertEquals(GenericFormWidgetData.class, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData().getClass());
        assertEquals(genericFormWidgetData, fetchBulkDataResponseV21.getWidgetEntityList().get(0).getWidgetData());
    }

    @NotNull
    private PageHandler getPageHandler() {
        return new PageHandler(
                amsBridge,
                documentService,
                repeatLoanOfferPageDataSource,
                repeatLoanOfferPageTransformer,
                submitButtonWidgetTransformerFactory,
                sandboxOfferScreenTransformer,
                dynamicBucket,
                decrypter,
                errorScreenTransformer,
                new HashMap<>(),
                new HashMap<>(),
                "",
                new HashMap<>(),
                new HashMap<>(),
                pageDataSourceFactory,
                successScreenTransformer,
                customScreenTransformer,
                groupedFormV4WidgetTransformer,
                cardWidgetTransformer);
    }

}


