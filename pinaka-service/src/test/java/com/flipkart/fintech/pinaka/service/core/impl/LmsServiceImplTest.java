package com.flipkart.fintech.pinaka.service.core.impl;

import com.flipkart.fintech.pinaka.service.data.impl.DisbursalsDao;
import com.flipkart.fintech.pinaka.service.data.model.DisbursalsEntity;
import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.ParseException;
import java.util.Optional;

import static com.flipkart.fintech.pinaka.service.utils.DateUtils.simpleDateFormat1;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class LmsServiceImplTest {

    @Mock
    private DisbursalsDao disbursalsDao;

    private LmsServiceImpl lmsService;

    @BeforeEach
    public void setUp() throws Exception {
        this.lmsService = new LmsServiceImpl(disbursalsDao);
    }

    @Test
    public void getDaysPastDisbursal() throws ParseException {
        DisbursalsEntity disbursalsEntity = new DisbursalsEntity();
        disbursalsEntity.setDisbursalDate(DateUtils.getDateFromString("2023-12-30 00:00:00", simpleDateFormat1));
        disbursalsEntity.setLender("AXIS");
        disbursalsEntity.setSmUserId("sm1");
        disbursalsEntity.setApplicationId("APP123");
        Mockito.when(disbursalsDao.getByApplicationId("APP123")).thenReturn(Optional.of(disbursalsEntity));
        Assertions.assertTrue(lmsService.getDaysPastDisbursal("APP123")>30);
    }
}