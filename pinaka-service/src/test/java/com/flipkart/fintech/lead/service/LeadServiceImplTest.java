package com.flipkart.fintech.lead.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.ams.ApplicationService;
import com.flipkart.ams.ApplicationServiceV2;
import com.flipkart.ams.LEAD_APPLICATION_TYPES;
import com.flipkart.fintech.lead.model.LeadResponse;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.model.UserScores;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.service.adaptor.v6.UniversalPersonalDetailsAdapter;
import com.flipkart.fintech.pinaka.service.core.page.PageActionResponseHandler;
import com.flipkart.fintech.pinaka.service.core.v7.CreateLeadRequestFactory;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.helper.ApplicationUserInputData;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.response.ProfileCRUDResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.sumo.crisys.api.CremoScores;
import com.sumo.crisys.client.CrisysClientException;
import org.junit.Before;
import org.junit.Test;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV3Util.class,
        ApplicationUserInputData.class,
        ObjectMapperUtil.class
})
public class LeadServiceImplTest {

    @Mock
    private ApplicationService applicationService;
    @Mock
    private ApplicationServiceV2 applicationServiceV2;
    @Mock
    private PageActionResponseHandler pageActionResponseHandler;
    @Mock
    private UserProfileScores userProfileScores;
    @Mock
    private UniversalPersonalDetailsAdapter universalPersonalDetailsAdapter;
    @Mock
    private CreateLeadRequestFactory createLeadRequestFactory;
    @Mock
    private ProfileService profileService;
    @Mock
    private DynamicBucket dynamicBucket;

    private LeadServiceImpl leadService;

    private MerchantUser merchantUser;
    private ApplicationDataResponse applicationDataResponse;
    private PageActionResponse pageActionResponse;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        merchantUser = MerchantUser.getMerchantUser("testMerchant", "testAccount", "testSmUser");

        applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationId()).thenReturn("testAppId");
        when(applicationDataResponse.getApplicationState()).thenReturn(LeadDetails.LeadState.NAME_PAGE.name());
        when(applicationDataResponse.getApplicationData()).thenReturn(new HashMap<>());

        pageActionResponse = mock(PageActionResponse.class);

        // Initialize leadService manually since @InjectMocks doesn't work well with PowerMock
        leadService = new LeadServiceImpl(
                universalPersonalDetailsAdapter,
                createLeadRequestFactory,
                applicationService,
                pageActionResponseHandler,
                profileService,
                userProfileScores,
                dynamicBucket,
                new Random(),
                applicationServiceV2,
                mock(LeadV4DataGatheringService.class)
        );
    }

    // Removed getLeadService() method - use @InjectMocks leadService directly
    private LeadService getLeadService() {
        return new LeadServiceImpl(
                universalPersonalDetailsAdapter,
                createLeadRequestFactory,
                applicationService,
                pageActionResponseHandler,
                profileService,
                userProfileScores,
                dynamicBucket,
                new Random(),
                applicationServiceV2,
                mock(LeadV4DataGatheringService.class)
        );
    }


    @Test
    public void testGetCurrentLeadStatus_getUserScores() throws PinakaException, CrisysClientException {
        String requestId = "testRequestId";
        String applicationId = "activeAppId";

        ApplicationDataResponse activeApp = mock(ApplicationDataResponse.class);
        when(activeApp.getApplicationId()).thenReturn(applicationId);
        when(activeApp.getApplicationState()).thenReturn(LeadDetails.LeadState.NAME_PAGE.name());
        when(activeApp.getApplicationData()).thenReturn(new HashMap<>());

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.of(activeApp));
        when(applicationService.fetchApplicationData(merchantUser, applicationId))
                .thenReturn(activeApp);

        PageActionResponse mockPageActionResponse = mock(PageActionResponse.class);
        when(pageActionResponseHandler.create(requestId, merchantUser, activeApp))
                .thenReturn(mockPageActionResponse);

        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);

        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);

        assertNotNull(response);
        assertNotNull(response.getLeadDetails());
        assertEquals(applicationId, response.getLeadDetails().getLeadId());

        UserScores userScores = response.getLeadDetails().getUserScores();
        assertNotNull(userScores);
        assertEquals(2.0, userScores.getBinScore(), 0.0);
        assertEquals(3, (double) userScores.getLosV2CremoBand(), 0.0);
        assertEquals(4, (double) userScores.getIdfcCremoBand(), 0.0);
        assertEquals(4.5, userScores.getGranularCremoBand(), 0.0);
    }

    @Test
    public void testGetCurrentLeadStatus_getUserScoresWhenCremoScoreIsNull() throws PinakaException, CrisysClientException {
        String requestId = "testRequestId";
        String applicationId = "activeAppId";

        ApplicationDataResponse activeApp = mock(ApplicationDataResponse.class);
        when(activeApp.getApplicationId()).thenReturn(applicationId);
        when(activeApp.getApplicationState()).thenReturn(LeadDetails.LeadState.NAME_PAGE.name());
        when(activeApp.getApplicationData()).thenReturn(new HashMap<>());

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.of(activeApp));
        when(applicationService.fetchApplicationData(merchantUser, applicationId))
                .thenReturn(activeApp);

        PageActionResponse mockPageActionResponse = mock(PageActionResponse.class);
        when(pageActionResponseHandler.create(requestId, merchantUser, activeApp))
                .thenReturn(mockPageActionResponse);

        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenThrow(new CrisysClientException("Crisys client exception"));

        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);

        assertNotNull(response);
        assertNotNull(response.getLeadDetails());
        assertEquals(applicationId, response.getLeadDetails().getLeadId());

        UserScores userScores = response.getLeadDetails().getUserScores();
        assertNotNull(userScores);
        assertEquals(2.0, userScores.getBinScore(), 0.0);
        assertNull(userScores.getLosV2CremoBand());
        assertNull(userScores.getIdfcCremoBand());
        assertNull(userScores.getGranularCremoBand());
    }

    @Test
    public void testGetCurrentLeadStatusV3_noActiveApplication() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;

        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.empty());

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(true);
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        ApplicationDataResponse applicationResponse = new ApplicationDataResponse();
        applicationResponse.setApplicationId("abcd");
        applicationResponse.setApplicationState(LeadDetails.LeadState.LEAD_V3_PAGE_1.name());
        when(applicationServiceV2.createApplication(mockReq)).thenReturn(applicationResponse);
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        verify(createLeadRequestFactory, times(1)).create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD_V3.name());
        assertEquals(LeadDetails.LeadState.LEAD_V3_PAGE_1.name(), response.getLeadDetails().getStateOfLead().name());
    }

    @Test
    public void testGetCurrentLeadStatusV3_noActiveApplicationAndApplicationTypeLV2() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;

        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.empty());

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(false);
        when(dynamicBucket.getInt(anyString())).thenReturn(101);
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        ApplicationDataResponse applicationResponse = new ApplicationDataResponse();
        applicationResponse.setApplicationId("abcd");
        applicationResponse.setApplicationState(LeadDetails.LeadState.NAME_PAGE.name());
        when(applicationServiceV2.createApplication(mockReq)).thenReturn(applicationResponse);
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        verify(createLeadRequestFactory, times(1)).create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD_V2.name());
        assertEquals(LeadDetails.LeadState.NAME_PAGE.name(), response.getLeadDetails().getStateOfLead().name());
    }

    @Test
    public void testGetCurrentLeadStatusV3_noActiveApplicationAndApplicationTypeLead() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;

        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.empty());

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(false);
        when(dynamicBucket.getInt(anyString())).thenReturn(0);
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        ApplicationDataResponse applicationResponse = new ApplicationDataResponse();
        applicationResponse.setApplicationId("abcd");
        applicationResponse.setApplicationState(LeadDetails.LeadState.NAME_PAGE.name());
        when(applicationServiceV2.createApplication(mockReq)).thenReturn(applicationResponse);
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        verify(createLeadRequestFactory, times(1)).create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD.name());
        assertEquals(LeadDetails.LeadState.NAME_PAGE.name(), response.getLeadDetails().getStateOfLead().name());
    }

    @Test
    public void testGetCurrentLeadStatusV3WithValidApplication() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;
        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
        Map<String, Object> applicationData = new HashMap<>();
        applicationDataResponse.setApplicationData(applicationData);
        applicationDataResponse.setApplicationType(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationDataResponse.setApplicationState(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationId("abcd");
        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData applicationUserInputData = new ApplicationUserInputData();
        applicationUserInputData.setIncome("1231");
        applicationUserInputData.setBonusIncome("123123");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(applicationUserInputData);
        applicationDataResponse.setApplicationState(LeadDetails.LeadState.LEAD_V3_PAGE_1.name());
        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);
        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.of(applicationDataResponse));

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(true);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        assertEquals(LeadDetails.LeadState.LEAD_V3_PAGE_1.name(), response.getLeadDetails().getStateOfLead().name());
    }

    @Test(expected = PinakaException.class)
    public void testGetCurrentLeadStatusV3WithValidApplicationWhenExceptionOccurs() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;
        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
        Map<String, Object> applicationData = new HashMap<>();
        applicationDataResponse.setApplicationData(applicationData);
        applicationDataResponse.setApplicationType(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationDataResponse.setApplicationState(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationId("abcd");
        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData applicationUserInputData = new ApplicationUserInputData();
        applicationUserInputData.setIncome("1231");
        applicationUserInputData.setBonusIncome("abcd");
        applicationUserInputData.setDob("19/02/1998");
        applicationUserInputData.setEmploymentType(EmploymentType.Salaried);
        applicationUserInputData.setPincode(123123);
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(applicationUserInputData);
        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        ProfileCRUDResponse profileCRUDResponse = ProfileCRUDResponse.builder().profileId(1231L).build();
        when(mockObjectMapper.convertValue(any(), eq(ProfileCRUDResponse.class))).thenReturn(profileCRUDResponse);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setUserEnteredPincode(123123);
        profileDetailedResponse.setEmploymentType(EmploymentType.Salaried);
        profileDetailedResponse.setDob("19/02/1997");
        when(profileService.getProfileById(profileCRUDResponse.getProfileId())).thenReturn(null);
        applicationDataResponse.setApplicationState(LeadDetails.LeadState.CREATE_PROFILE_END.name());
        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);
        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.of(applicationDataResponse));

        LeadService leadService = spy(getLeadService());
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(true);

        PageActionResponse pageActionResponse = PageActionResponse.builder().build();
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        assertEquals(LeadDetails.LeadState.CREATE_PROFILE_END.name(), response.getLeadDetails().getStateOfLead().name());
        assertEquals(1231, response.getLeadDetails().getMonthlyIncome(), 0.0);
    }

    @Test
    public void testGetCurrentLeadStatusV3WithValidApplicationWhenMonthlyIncomeIsNotInteger() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;
        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
        Map<String, Object> applicationData = new HashMap<>();
        applicationDataResponse.setApplicationData(applicationData);
        applicationDataResponse.setApplicationType(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationDataResponse.setApplicationState(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationId("abcd");
        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData applicationUserInputData = new ApplicationUserInputData();
        applicationUserInputData.setIncome("abcd");
        applicationUserInputData.setBonusIncome("123123");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(applicationUserInputData);
        applicationDataResponse.setApplicationState(LeadDetails.LeadState.LEAD_V3_PAGE_1.name());
        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);
        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.of(applicationDataResponse));

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(true);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        assertEquals(LeadDetails.LeadState.LEAD_V3_PAGE_1.name(), response.getLeadDetails().getStateOfLead().name());
        assertEquals(0, response.getLeadDetails().getMonthlyIncome(), 0.0);
    }

    @Test
    public void testGetCurrentLeadStatusV3WithValidApplicationWhenBonusIncomeIsNotInteger() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;
        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
        Map<String, Object> applicationData = new HashMap<>();
        applicationDataResponse.setApplicationData(applicationData);
        applicationDataResponse.setApplicationType(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationDataResponse.setApplicationState(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationId("abcd");
        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData applicationUserInputData = new ApplicationUserInputData();
        applicationUserInputData.setIncome("1231");
        applicationUserInputData.setBonusIncome("abcd");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(applicationUserInputData);
        applicationDataResponse.setApplicationState(LeadDetails.LeadState.LEAD_V3_PAGE_1.name());
        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);
        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.of(applicationDataResponse));

        LeadService leadService = spy(getLeadService());
//        mockStatic(LV3Util.class);
//        when(LV3Util.isLv3ApplicationType(LeadDetails.LeadState.LEAD_V3_PAGE_1.name())).thenReturn(true);
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        ApplicationDataResponse applicationResponse = new ApplicationDataResponse();
        applicationResponse.setApplicationId("abcd");
        when(applicationServiceV2.createApplication(mockReq)).thenReturn(applicationResponse);

        PageActionResponse pageActionResponse = PageActionResponse.builder().build();
        when(applicationService.fetchApplicationData(merchantUser, applicationResponse.getApplicationId())).thenReturn(applicationDataResponse);
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        assertEquals(LeadDetails.LeadState.LEAD_V3_PAGE_1.name(), response.getLeadDetails().getStateOfLead().name());
        assertEquals(1231, response.getLeadDetails().getMonthlyIncome(), 0.0);
    }

    @Test
    public void testGetCurrentLeadStatusV3WithValidApplicationWhenLeadStateIsCreateProfileEndReadRepairNotNeeded() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;
        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationData(applicationData);
        applicationDataResponse.setApplicationType(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationId("abcd");
        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData applicationUserInputData = new ApplicationUserInputData();
        applicationUserInputData.setIncome("1231");
        applicationUserInputData.setBonusIncome("abcd");
        applicationUserInputData.setDob("19/02/1998");
        applicationUserInputData.setEmploymentType(EmploymentType.Salaried);
        applicationUserInputData.setPincode(123123);
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(applicationUserInputData);
        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        ProfileCRUDResponse profileCRUDResponse = ProfileCRUDResponse.builder().profileId(1231L).build();
        when(mockObjectMapper.convertValue(any(), eq(ProfileCRUDResponse.class))).thenReturn(profileCRUDResponse);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setUserEnteredPincode(123123);
        profileDetailedResponse.setEmploymentType(EmploymentType.Salaried);
        profileDetailedResponse.setDob("19/02/1998");
        when(profileService.getProfileById(profileCRUDResponse.getProfileId())).thenReturn(profileDetailedResponse);
        applicationDataResponse.setApplicationState(LeadDetails.LeadState.CREATE_PROFILE_END.name());
        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);
        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.of(applicationDataResponse));

        LeadService leadService = spy(getLeadService());
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        ApplicationDataResponse applicationResponse = new ApplicationDataResponse();
        applicationResponse.setApplicationId("abcd");
        when(applicationServiceV2.createApplication(mockReq)).thenReturn(applicationResponse);

        PageActionResponse pageActionResponse = PageActionResponse.builder().build();
        when(applicationService.fetchApplicationData(merchantUser, applicationResponse.getApplicationId())).thenReturn(applicationDataResponse);
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        assertEquals(LeadDetails.LeadState.CREATE_PROFILE_END.name(), response.getLeadDetails().getStateOfLead().name());
        assertEquals(1231, response.getLeadDetails().getMonthlyIncome(), 0.0);
    }

    @Test
    public void testGetCurrentLeadStatusV3WithValidApplicationWhenLeadStateIsCreateProfileEndReadRepairNeeded() throws Exception {
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;
        ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
        Map<String, Object> applicationData = new HashMap<>();
        applicationDataResponse.setApplicationData(applicationData);
        applicationDataResponse.setApplicationType(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationDataResponse.setApplicationState(LEAD_APPLICATION_TYPES.LEAD_V3.name());
        applicationData.put("leadV3Key", "value");
        applicationDataResponse.setApplicationId("abcd");
        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData applicationUserInputData = new ApplicationUserInputData();
        applicationUserInputData.setIncome("1231");
        applicationUserInputData.setBonusIncome("abcd");
        applicationUserInputData.setDob("19/02/1998");
        applicationUserInputData.setEmploymentType(EmploymentType.Salaried);
        applicationUserInputData.setPincode(123123);
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(applicationUserInputData);
        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        ProfileCRUDResponse profileCRUDResponse = ProfileCRUDResponse.builder().profileId(1231L).build();
        when(mockObjectMapper.convertValue(any(), eq(ProfileCRUDResponse.class))).thenReturn(profileCRUDResponse);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setUserEnteredPincode(123123);
        profileDetailedResponse.setEmploymentType(EmploymentType.Salaried);
        profileDetailedResponse.setDob("19/02/1997");
        when(profileService.getProfileById(profileCRUDResponse.getProfileId())).thenReturn(profileDetailedResponse);
        applicationDataResponse.setApplicationState(LeadDetails.LeadState.CREATE_PROFILE_END.name());
        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        CremoScores cremoScores = mock(CremoScores.class);
        when(cremoScores.losV2CremoBand()).thenReturn("3");
        when(cremoScores.idfcCremoBand()).thenReturn("4");
        when(cremoScores.granularCremoBand()).thenReturn("4.5");
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenReturn(cremoScores);
        // mocking
        when(applicationService.findLatestActiveApplicationV2(merchantUser, productType))
                .thenReturn(Optional.of(applicationDataResponse));

        LeadService leadService = spy(getLeadService());
        CreateApplicationRequest mockReq = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(any(MerchantUser.class), any(ProductType.class), anyString(), anyString())).thenReturn(mockReq);
        ApplicationDataResponse applicationResponse = new ApplicationDataResponse();
        applicationResponse.setApplicationId("abcd");
        when(applicationServiceV2.createApplication(mockReq)).thenReturn(applicationResponse);

        PageActionResponse pageActionResponse = PageActionResponse.builder().build();
        when(applicationService.fetchApplicationData(merchantUser, applicationResponse.getApplicationId())).thenReturn(applicationDataResponse);
        when(pageActionResponseHandler.create(anyString(), any(MerchantUser.class), any(ApplicationDataResponse.class)))
                .thenReturn(pageActionResponse);

        // action
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);
        // assertion
        assertEquals(LeadDetails.LeadState.CREATE_PROFILE_END.name(), response.getLeadDetails().getStateOfLead().name());
        assertEquals(1231, response.getLeadDetails().getMonthlyIncome(), 0.0);

    }

    @Test
    public void testGetCurrentLeadStatus_NoActiveApplication() throws PinakaException {
        // Given
        String requestId = "testRequestId";

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.empty());

        CreateApplicationRequest createRequest = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(eq(merchantUser), eq(ProductType.LEAD), eq(requestId), anyString()))
                .thenReturn(createRequest);


        when(applicationServiceV2.createApplication(createRequest))
                .thenReturn(applicationDataResponse);

        when(pageActionResponseHandler.create(eq(requestId), eq(merchantUser), eq(applicationDataResponse)))
                .thenReturn(pageActionResponse);

        // When
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);

        // Then
        assertNotNull(response);
        assertNotNull(response.getLeadDetails());
        assertEquals("testAppId", response.getLeadDetails().getLeadId());

        verify(applicationService).findLatestActiveApplicationV2(merchantUser, ProductType.LEAD);
        verify(applicationServiceV2).createApplication(createRequest);
    }

    @Test
    public void testFindActiveApplication_WithActiveApplication() throws PinakaException {
        // Given
        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.of(applicationDataResponse));

        // When
        Optional<ApplicationDataResponse> result = leadService.findActiveApplication(merchantUser);

        // Then
        assertTrue(result.isPresent());
        assertEquals(applicationDataResponse, result.get());
        verify(applicationService).findLatestActiveApplicationV2(merchantUser, ProductType.LEAD);
    }

    @Test
    public void testFindActiveApplication_NoActiveApplication() throws PinakaException {
        // Given
        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.empty());

        // When
        Optional<ApplicationDataResponse> result = leadService.findActiveApplication(merchantUser);

        // Then
        assertFalse(result.isPresent());
        verify(applicationService).findLatestActiveApplicationV2(merchantUser, ProductType.LEAD);
    }

    @Test
    public void testCreatePageResponse_UsesApplicationServiceV2() throws PinakaException {
        // Given - This test specifically covers the change in branch 2370-reduce-calls-from-pinaka-to-winterfell
        String requestId = "testRequestId";

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.empty());

        CreateApplicationRequest createRequest = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(eq(merchantUser), eq(ProductType.LEAD), eq(requestId), anyString()))
                .thenReturn(createRequest);

        // Key change: ApplicationServiceV2.createApplication returns ApplicationDataResponse directly
        // instead of needing separate create + fetch calls
        when(applicationServiceV2.createApplication(createRequest))
                .thenReturn(applicationDataResponse);

        when(pageActionResponseHandler.create(eq(requestId), eq(merchantUser), eq(applicationDataResponse)))
                .thenReturn(pageActionResponse);

        // When
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);

        // Then
        assertNotNull(response);
        assertNotNull(response.getLeadDetails());
        assertEquals("testAppId", response.getLeadDetails().getLeadId());

        // Verify that ApplicationServiceV2.createApplication was called (the key change)
        verify(applicationServiceV2).createApplication(createRequest);
        // Verify that ApplicationService.fetchApplicationData was NOT called (optimization)
        verify(applicationService, never()).fetchApplicationData(any(MerchantUser.class), anyString());
        verify(pageActionResponseHandler).create(requestId, merchantUser, applicationDataResponse);
    }

    @Test
    public void testCreatePageResponse_WithDynamicBucketConfiguration() throws PinakaException {
        // Given - Test the dynamic bucket logic for LEAD vs LEAD_V2
        String requestId = "testRequestId";

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.empty());

        // Mock dynamic bucket to return specific configuration
        when(dynamicBucket.getStringArray(anyString())).thenReturn(Arrays.asList());
        when(dynamicBucket.getInt(anyString())).thenReturn(50); // 50% chance for LEAD_V2

        CreateApplicationRequest createRequest = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(eq(merchantUser), eq(ProductType.LEAD), eq(requestId), anyString()))
                .thenReturn(createRequest);

        when(applicationServiceV2.createApplication(createRequest))
                .thenReturn(applicationDataResponse);

        when(pageActionResponseHandler.create(eq(requestId), eq(merchantUser), eq(applicationDataResponse)))
                .thenReturn(pageActionResponse);

        // When
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);

        // Then
        assertNotNull(response);
        verify(createLeadRequestFactory).create(eq(merchantUser), eq(ProductType.LEAD), eq(requestId), anyString());
        verify(applicationServiceV2).createApplication(createRequest);
    }

    // Tests for getMonthlyIncomeFromApplication method
    @Test
    public void testGetMonthlyIncomeFromApplication_WithReviewPage2() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");

        Map<String, Object> applicationData = new HashMap<>();
        Map<String, Object> reviewPage2 = new HashMap<>();
        reviewPage2.put("income", "50000");
        reviewPage2.put("bonusIncome", "10000");
        applicationData.put("reviewPage2", reviewPage2);

        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        when(mockObjectMapper.convertValue(reviewPage2, Map.class)).thenReturn(reviewPage2);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplication", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("60000", result);
    }

    @Test
    public void testGetMonthlyIncomeFromApplication_WithoutReviewPage2() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        when(appResponse.getApplicationData()).thenReturn(new HashMap<>());

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplication", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("0", result);
    }

    @Test
    public void testGetMonthlyIncomeFromApplication_WithInvalidIncome() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        when(appResponse.getApplicationId()).thenReturn("testAppId");

        Map<String, Object> applicationData = new HashMap<>();
        Map<String, Object> reviewPage2 = new HashMap<>();
        reviewPage2.put("income", "invalid");
        reviewPage2.put("bonusIncome", "also_invalid");
        applicationData.put("reviewPage2", reviewPage2);

        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        when(mockObjectMapper.convertValue(reviewPage2, Map.class)).thenReturn(reviewPage2);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplication", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("0", result);
    }

    @Test
    public void testGetMonthlyIncomeFromApplication_WithNullApplicationData() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        when(appResponse.getApplicationData()).thenReturn(null);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplication", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("0", result);
    }

    // Tests for getMonthlyIncomeFromApplicationV3 method
    @Test
    public void testGetMonthlyIncomeFromApplicationV3_WithValidIncome() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");

        Map<String, Object> applicationData = new HashMap<>();
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData inputData = new ApplicationUserInputData();
        inputData.setIncome("75000");
        inputData.setBonusIncome("15000");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(inputData);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplicationV3", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("90000", result);
    }

    @Test
    public void testGetMonthlyIncomeFromApplicationV3_WithBlankIncome() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");

        Map<String, Object> applicationData = new HashMap<>();
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData inputData = new ApplicationUserInputData();
        inputData.setIncome("");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(inputData);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplicationV3", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("0", result);
    }

    @Test
    public void testGetMonthlyIncomeFromApplicationV3_WithInvalidIncome() throws Exception {
        // Given
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        when(appResponse.getApplicationId()).thenReturn("testAppId");

        Map<String, Object> applicationData = new HashMap<>();
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData inputData = new ApplicationUserInputData();
        inputData.setIncome("invalid");
        inputData.setBonusIncome("also_invalid");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(inputData);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getMonthlyIncomeFromApplicationV3", ApplicationDataResponse.class);
        method.setAccessible(true);
        String result = (String) method.invoke(leadService, appResponse);

        // Then
        assertEquals("0", result);
    }

    // Note: getApplicationType method tests removed as the method doesn't exist in the current implementation
    // The application type logic is now handled directly in createPageResponse and createPageResponseV3 methods





    // Tests for getPageActionResponse method
    @Test
    public void testGetPageActionResponse_WithNullApplicationDataResponse() throws PinakaException {
        // Given
        String requestId = "testRequestId";

        // When
        PageActionResponse result = leadService.getPageActionResponse(null, requestId, merchantUser);

        // Then
        assertNotNull(result);
        // The method should return NoActiveApplicationErrorBehavior.getPageActionResponse()
        // We can't easily verify the exact response without mocking the static method
    }

    @Test
    public void testGetPageActionResponse_WithValidApplicationDataResponse() throws PinakaException {
        // Given
        String requestId = "testRequestId";
        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        PageActionResponse expectedResponse = mock(PageActionResponse.class);

        when(pageActionResponseHandler.create(requestId, merchantUser, appResponse))
                .thenReturn(expectedResponse);

        // When
        PageActionResponse result = leadService.getPageActionResponse(appResponse, requestId, merchantUser);

        // Then
        assertEquals(expectedResponse, result);
        verify(pageActionResponseHandler).create(requestId, merchantUser, appResponse);
    }

    // Tests for fetchCremoScores method
    @Test
    public void testFetchCremoScores_Success() throws Exception {
        // Given
        CremoScores expectedScores = mock(CremoScores.class);
        when(userProfileScores.getCremoScore(merchantUser)).thenReturn(expectedScores);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("fetchCremoScores", MerchantUser.class);
        method.setAccessible(true);
        CremoScores result = (CremoScores) method.invoke(leadService, merchantUser);

        // Then
        assertEquals(expectedScores, result);
        verify(userProfileScores).getCremoScore(merchantUser);
    }

    @Test
    public void testFetchCremoScores_WithException() throws Exception {
        // Given
        when(userProfileScores.getCremoScore(merchantUser)).thenThrow(new CrisysClientException("Test exception"));

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("fetchCremoScores", MerchantUser.class);
        method.setAccessible(true);
        CremoScores result = (CremoScores) method.invoke(leadService, merchantUser);

        // Then
        assertNull(result);
        verify(userProfileScores).getCremoScore(merchantUser);
    }

    // Tests for getUserScores method
    @Test
    public void testGetUserScores_WithAllScores() throws Exception {
        // Given
        String requestId = "testRequestId";
        Double binScore = 2.5;
        CremoScores cremoScores = mock(CremoScores.class);

        when(userProfileScores.getUserBin(merchantUser, requestId)).thenReturn(binScore);
        when(userProfileScores.getCremoScore(merchantUser)).thenReturn(cremoScores);
        when(cremoScores.losV2CremoBand()).thenReturn("3.0");
        when(cremoScores.idfcCremoBand()).thenReturn("4.0");
        when(cremoScores.granularCremoBand()).thenReturn("5.0");

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getUserScores", MerchantUser.class, String.class);
        method.setAccessible(true);
        UserScores result = (UserScores) method.invoke(leadService, merchantUser, requestId);

        // Then
        assertNotNull(result);
        assertEquals(binScore, result.getBinScore());
        assertEquals(Double.valueOf(3.0), result.getLosV2CremoBand());
        assertEquals(Double.valueOf(4.0), result.getIdfcCremoBand());
        assertEquals(Double.valueOf(5.0), result.getGranularCremoBand());
    }

    @Test
    public void testGetUserScores_WithNullCremoScores() throws Exception {
        // Given
        String requestId = "testRequestId";
        Double binScore = 2.5;

        when(userProfileScores.getUserBin(merchantUser, requestId)).thenReturn(binScore);
        when(userProfileScores.getCremoScore(merchantUser)).thenThrow(new CrisysClientException("Test exception"));

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("getUserScores", MerchantUser.class, String.class);
        method.setAccessible(true);
        UserScores result = (UserScores) method.invoke(leadService, merchantUser, requestId);

        // Then
        assertNotNull(result);
        assertEquals(binScore, result.getBinScore());
        assertNull(result.getLosV2CremoBand());
        assertNull(result.getIdfcCremoBand());
        assertNull(result.getGranularCremoBand());
    }

    // Tests for readRepair method
    @Test
    public void testReadRepair_WithLV3Enabled() throws Exception {
        // Given
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setUserEnteredPincode(123456);
        profile.setEmploymentType(EmploymentType.Salaried);
        profile.setDob("01/01/1990");
        profile.setProfileId(12345L);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        Map<String, Object> applicationData = new HashMap<>();
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Application(appResponse)).thenReturn(true);

        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData inputData = new ApplicationUserInputData();
        inputData.setPincode(123456);
        inputData.setEmploymentType(EmploymentType.Salaried);
        inputData.setDob("01/01/1990");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(inputData);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("readRepair", ProfileDetailedResponse.class, ApplicationDataResponse.class);
        method.setAccessible(true);
        method.invoke(leadService, profile, appResponse);

        // Then - verify LV3Util.isLv3Enabled was called
        // The method should delegate to readRepairV3 and profile should remain unchanged
        assertEquals(Integer.valueOf(123456), profile.getUserEnteredPincode());
        assertEquals(EmploymentType.Salaried, profile.getEmploymentType());
        assertEquals("01/01/1990", profile.getDob());
    }

    @Test
    public void testReadRepair_WithBasicDetailsMatch() throws Exception {
        // Given
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setUserEnteredPincode(123456);
        profile.setEmploymentType(EmploymentType.Salaried);
        profile.setDob("01/01/1990");

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        Map<String, Object> basicDetails = new HashMap<>();
        basicDetails.put("pincode", "123456");
        basicDetails.put("employmentType", "Salaried");
        basicDetails.put("dob", "01/01/1990");
        applicationData.put("basicDetails", basicDetails);
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Application(appResponse)).thenReturn(false);

        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        when(mockObjectMapper.convertValue(basicDetails, Map.class)).thenReturn(basicDetails);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("readRepair", ProfileDetailedResponse.class, ApplicationDataResponse.class);
        method.setAccessible(true);
        method.invoke(leadService, profile, appResponse);

        // Then - profile should remain unchanged
        assertEquals(Integer.valueOf(123456), profile.getUserEnteredPincode());
        assertEquals(EmploymentType.Salaried, profile.getEmploymentType());
        assertEquals("01/01/1990", profile.getDob());
    }

    @Test
    public void testReadRepair_WithBasicDetailsMismatch() throws Exception {
        // Given
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setUserEnteredPincode(123456);
        profile.setEmploymentType(EmploymentType.Salaried);
        profile.setDob("01/01/1990");
        profile.setProfileId(12345L);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        Map<String, Object> basicDetails = new HashMap<>();
        basicDetails.put("pincode", "654321");
        basicDetails.put("employmentType", "SelfEmployed");
        basicDetails.put("dob", "02/02/1991");
        applicationData.put("basicDetails", basicDetails);
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Application(appResponse)).thenReturn(false);

        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        when(mockObjectMapper.convertValue(basicDetails, Map.class)).thenReturn(basicDetails);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("readRepair", ProfileDetailedResponse.class, ApplicationDataResponse.class);
        method.setAccessible(true);
        method.invoke(leadService, profile, appResponse);

        // Then - profile should be updated with application data
        assertEquals(Integer.valueOf(654321), profile.getUserEnteredPincode());
        assertEquals(EmploymentType.SelfEmployed, profile.getEmploymentType());
        assertEquals("02/02/1991", profile.getDob());
    }

    @Test
    public void testReadRepair_WithReviewPage1() throws Exception {
        // Given
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setUserEnteredPincode(123456);
        profile.setEmploymentType(EmploymentType.Salaried);
        profile.setDob("01/01/1990");

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        Map<String, Object> reviewPage1 = new HashMap<>();
        reviewPage1.put("pincode", "123456");
        reviewPage1.put("employmentType", "Salaried");
        reviewPage1.put("dob", "01/01/1990");
        applicationData.put("reviewPage1", reviewPage1);
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Application(appResponse)).thenReturn(false);

        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        when(mockObjectMapper.convertValue(reviewPage1, Map.class)).thenReturn(reviewPage1);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("readRepair", ProfileDetailedResponse.class, ApplicationDataResponse.class);
        method.setAccessible(true);
        method.invoke(leadService, profile, appResponse);

        // Then - profile should remain unchanged
        assertEquals(Integer.valueOf(123456), profile.getUserEnteredPincode());
        assertEquals(EmploymentType.Salaried, profile.getEmploymentType());
        assertEquals("01/01/1990", profile.getDob());
    }

    // Tests for readRepairV3 method
    @Test
    public void testReadRepairV3_WithDataMatch() throws Exception {
        // Given
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setUserEnteredPincode(123456);
        profile.setEmploymentType(EmploymentType.Salaried);
        profile.setDob("01/01/1990");
        profile.setProfileId(12345L);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        Map<String, Object> applicationData = new HashMap<>();
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData inputData = new ApplicationUserInputData();
        inputData.setPincode(123456);
        inputData.setEmploymentType(EmploymentType.Salaried);
        inputData.setDob("01/01/1990");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(inputData);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("readRepairV3", ProfileDetailedResponse.class, ApplicationDataResponse.class);
        method.setAccessible(true);
        method.invoke(leadService, profile, appResponse);

        // Then - profile should remain unchanged
        assertEquals(Integer.valueOf(123456), profile.getUserEnteredPincode());
        assertEquals(EmploymentType.Salaried, profile.getEmploymentType());
        assertEquals("01/01/1990", profile.getDob());
    }

    @Test
    public void testReadRepairV3_WithDataMismatch() throws Exception {
        // Given
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setUserEnteredPincode(123456);
        profile.setEmploymentType(EmploymentType.Salaried);
        profile.setDob("01/01/1990");
        profile.setProfileId(12345L);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getSmUserId()).thenReturn("testUser");
        Map<String, Object> applicationData = new HashMap<>();
        when(appResponse.getApplicationData()).thenReturn(applicationData);

        mockStatic(ApplicationUserInputData.class);
        ApplicationUserInputData inputData = new ApplicationUserInputData();
        inputData.setPincode(654321);
        inputData.setEmploymentType(EmploymentType.SelfEmployed);
        inputData.setDob("02/02/1991");
        when(ApplicationUserInputData.getApplicationResponseData(applicationData)).thenReturn(inputData);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("readRepairV3", ProfileDetailedResponse.class, ApplicationDataResponse.class);
        method.setAccessible(true);
        method.invoke(leadService, profile, appResponse);

        // Then - profile should be updated with application data
        assertEquals(Integer.valueOf(654321), profile.getUserEnteredPincode());
        assertEquals(EmploymentType.SelfEmployed, profile.getEmploymentType());
        assertEquals("02/02/1991", profile.getDob());
    }

    // Additional edge case tests for getCurrentLeadStatus
    @Test
    public void testGetCurrentLeadStatus_WithCreateProfileEndAndNullProfile() throws PinakaException {
        // Given
        String requestId = "testRequestId";
        String applicationId = "activeAppId";

        ApplicationDataResponse activeApp = mock(ApplicationDataResponse.class);
        when(activeApp.getApplicationId()).thenReturn(applicationId);
        when(activeApp.getApplicationState()).thenReturn(LeadDetails.LeadState.CREATE_PROFILE_END.name());

        Map<String, Object> applicationData = new HashMap<>();
        Map<String, Object> createProfile = new HashMap<>();
        createProfile.put("profileId", 12345L);
        applicationData.put("createProfile", createProfile);
        when(activeApp.getApplicationData()).thenReturn(applicationData);

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.of(activeApp));

        mockStatic(ObjectMapperUtil.class);
        ObjectMapper mockObjectMapper = mock(ObjectMapper.class);
        when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
        ProfileCRUDResponse profileCRUDResponse = ProfileCRUDResponse.builder().profileId(12345L).build();
        when(mockObjectMapper.convertValue(createProfile, ProfileCRUDResponse.class)).thenReturn(profileCRUDResponse);

        when(profileService.getProfileById(12345L)).thenReturn(null);

        // When & Then
        try {
            leadService.getCurrentLeadStatus(merchantUser, requestId);
            fail("Expected PinakaException to be thrown");
        } catch (PinakaException e) {
            assertTrue(e.getMessage().contains("Profile fetch by ID failed: 12345"));
        }
    }

    @Test
    public void testGetCurrentLeadStatus_WithEmptyMonthlyIncome() throws PinakaException, CrisysClientException {
        // Given
        String requestId = "testRequestId";
        String applicationId = "activeAppId";

        ApplicationDataResponse activeApp = mock(ApplicationDataResponse.class);
        when(activeApp.getApplicationId()).thenReturn(applicationId);
        when(activeApp.getApplicationState()).thenReturn(LeadDetails.LeadState.NAME_PAGE.name());
        when(activeApp.getApplicationData()).thenReturn(new HashMap<>());
        when(activeApp.getSmUserId()).thenReturn("testUser");

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenReturn(Optional.of(activeApp));

        PageActionResponse mockPageActionResponse = mock(PageActionResponse.class);
        when(pageActionResponseHandler.create(requestId, merchantUser, activeApp))
                .thenReturn(mockPageActionResponse);

        when(userProfileScores.getUserBin(eq(merchantUser), eq(requestId))).thenReturn(2.0);
        when(userProfileScores.getCremoScore(eq(merchantUser))).thenThrow(new CrisysClientException("Test exception"));

        // When
        LeadResponse response = leadService.getCurrentLeadStatus(merchantUser, requestId);

        // Then
        assertNotNull(response);
        assertNotNull(response.getLeadDetails());
        assertEquals(applicationId, response.getLeadDetails().getLeadId());
        // Monthly income should be 0 when empty (not null, because getMonthlyIncomeFromApplication returns "0")
        assertEquals(Integer.valueOf(0), response.getLeadDetails().getMonthlyIncome());
    }

    @Test
    public void testGetCurrentLeadStatus_WithException() throws PinakaException {
        // Given
        String requestId = "testRequestId";

        when(applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        try {
            leadService.getCurrentLeadStatus(merchantUser, requestId);
            fail("Expected PinakaException to be thrown");
        } catch (PinakaException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("Database error", e.getCause().getMessage());
        }
    }

    @Test
    public void testCreatePageResponse_WithWhitelistedUser() throws Exception {
        // Given
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(false);
        when(dynamicBucket.getStringArray(anyString())).thenReturn(Arrays.asList(merchantUser.getSmUserId()));

        CreateApplicationRequest createRequest = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(eq(merchantUser), eq(productType), eq(requestId), eq(LEAD_APPLICATION_TYPES.LEAD_V2.name())))
                .thenReturn(createRequest);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getApplicationId()).thenReturn("testAppId");
        when(appResponse.getApplicationState()).thenReturn(LeadDetails.LeadState.NAME_PAGE.name());
        when(appResponse.getApplicationData()).thenReturn(new HashMap<>());
        when(applicationServiceV2.createApplication(createRequest)).thenReturn(appResponse);

        PageActionResponse pageResponse = mock(PageActionResponse.class);
        when(pageActionResponseHandler.create(requestId, merchantUser, appResponse)).thenReturn(pageResponse);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("createPageResponse", String.class, MerchantUser.class, ProductType.class);
        method.setAccessible(true);
        LeadResponse result = (LeadResponse) method.invoke(leadService, requestId, merchantUser, productType);

        // Then
        assertNotNull(result);
        verify(createLeadRequestFactory).create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD_V2.name());
    }

    @Test
    public void testCreatePageResponse_WithRandomTrafficForLEAD() throws Exception {
        // Given
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(false);
        when(dynamicBucket.getStringArray(anyString())).thenReturn(Arrays.asList());
        when(dynamicBucket.getInt(anyString())).thenReturn(50);

        // Create a new LeadServiceImpl with a fixed Random that returns 75 (>= 50)
        Random fixedRandom = mock(Random.class);
        when(fixedRandom.nextInt(100)).thenReturn(75);

        LeadServiceImpl testService = new LeadServiceImpl(
                universalPersonalDetailsAdapter,
                createLeadRequestFactory,
                applicationService,
                pageActionResponseHandler,
                profileService,
                userProfileScores,
                dynamicBucket,
                fixedRandom,
                applicationServiceV2,
                mock(LeadV4DataGatheringService.class)
        );

        CreateApplicationRequest createRequest = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(eq(merchantUser), eq(productType), eq(requestId), eq(LEAD_APPLICATION_TYPES.LEAD.name())))
                .thenReturn(createRequest);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getApplicationId()).thenReturn("testAppId");
        when(appResponse.getApplicationState()).thenReturn(LeadDetails.LeadState.NAME_PAGE.name());
        when(appResponse.getApplicationData()).thenReturn(new HashMap<>());
        when(applicationServiceV2.createApplication(createRequest)).thenReturn(appResponse);

        PageActionResponse pageResponse = mock(PageActionResponse.class);
        when(pageActionResponseHandler.create(requestId, merchantUser, appResponse)).thenReturn(pageResponse);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("createPageResponse", String.class, MerchantUser.class, ProductType.class);
        method.setAccessible(true);
        LeadResponse result = (LeadResponse) method.invoke(testService, requestId, merchantUser, productType);

        // Then
        assertNotNull(result);
        verify(createLeadRequestFactory).create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD.name());
    }

    // Tests for createPageResponseV3 method
    @Test
    public void testCreatePageResponseV3_Success() throws Exception {
        // Given
        String requestId = "testRequestId";
        ProductType productType = ProductType.LEAD;

        mockStatic(LV3Util.class);
        when(LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())).thenReturn(true);

        CreateApplicationRequest createRequest = mock(CreateApplicationRequest.class);
        when(createLeadRequestFactory.create(eq(merchantUser), eq(productType), eq(requestId), eq(LEAD_APPLICATION_TYPES.LEAD_V3.name())))
                .thenReturn(createRequest);

        ApplicationDataResponse appResponse = mock(ApplicationDataResponse.class);
        when(appResponse.getApplicationId()).thenReturn("testAppId");
        when(appResponse.getApplicationState()).thenReturn(LeadDetails.LeadState.LEAD_V3_PAGE_1.name());
        when(appResponse.getApplicationData()).thenReturn(new HashMap<>());
        when(applicationServiceV2.createApplication(createRequest)).thenReturn(appResponse);

        PageActionResponse pageResponse = mock(PageActionResponse.class);
        when(pageActionResponseHandler.create(requestId, merchantUser, appResponse)).thenReturn(pageResponse);

        // When - using reflection to test private method
        java.lang.reflect.Method method = LeadServiceImpl.class.getDeclaredMethod("createPageResponseV3", String.class, MerchantUser.class, ProductType.class);
        method.setAccessible(true);
        LeadResponse result = (LeadResponse) method.invoke(leadService, requestId, merchantUser, productType);

        // Then
        assertNotNull(result);
        assertNotNull(result.getLeadDetails());
        assertEquals("testAppId", result.getLeadDetails().getLeadId());
        assertEquals(LeadDetails.LeadState.LEAD_V3_PAGE_1, result.getLeadDetails().getStateOfLead());
        assertEquals(pageResponse, result.getPageActionResponse());
        verify(createLeadRequestFactory).create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD_V3.name());
    }
}
