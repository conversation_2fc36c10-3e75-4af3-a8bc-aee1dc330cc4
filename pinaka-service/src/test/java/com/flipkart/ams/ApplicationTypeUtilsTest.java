package com.flipkart.ams;

import static org.junit.Assert.*;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import org.junit.Before;
import org.junit.Test;

public class ApplicationTypeUtilsTest {

  @Before
  public void setUp() throws Exception {
  }

  @Test
  public void getApplicationTypeForAxis() {
    String applicationType = ApplicationTypeUtils.getApplicationType(ProductType.PERSONAL_LOAN, "AXIS");
    assertEquals("ApplicationType logic changed", "PERSONAL_LOAN", applicationType);
  }

  @Test
  public void getApplicationTypeNotForAxis() {
    String applicationType = ApplicationTypeUtils.getApplicationType(ProductType.PERSONAL_LOAN, "AXIS");
    assertNotEquals("ApplicationType logic changed", "PERSONAL_LOAN_AXIS", applicationType);
  }

  @Test
  public void getApplicationTypeForIDFC() {
    String applicationType = ApplicationTypeUtils.getApplicationType(ProductType.PERSONAL_LOAN, "IDFC");
    assertEquals("ApplicationType logic changed", "PERSONAL_LOAN_IDFC", applicationType);
  }
}