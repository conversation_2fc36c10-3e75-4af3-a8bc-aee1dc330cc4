package com.flipkart.fintech.pinaka.service.utils.FormUtils;

import static org.junit.Assert.assertEquals;

import java.text.ParseException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.Tenure;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.TenureUnit;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.ApprovedOfferWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

public class OfferScreenUtilsTest {

    @Test
    public void testGetEmiAmount() throws ParseException {
        double emiAmount = OfferScreenUtils.getEmiAmount(13.58, 51);
        double emiAmount2 = OfferScreenUtils.getEmiAmount(13.58, 48);
        double expectedEmiAmount = 25.92;
        double expectedEmiAmount2 = 27.12;
        assertEquals(expectedEmiAmount, emiAmount, 0.00001);
        assertEquals(expectedEmiAmount2, emiAmount2, 0.00001);
    }

    @Test
    public void test2() throws JsonProcessingException {
        String json = TransformerUtils.readFileasString("template/landingPages/OfferDetailsApprovedOffer.json");
        ApprovedOfferWidgetData approvedOfferWidgetData = ObjectMapperUtil.get().readValue(json, ApprovedOfferWidgetData.class);
        return;
    }

    @org.junit.jupiter.api.Test
    void getEmiTest1() {
        Double loanAmount = 10000D;
        Double roi = 36D;
        Tenure tenure = new Tenure();
        tenure.setUnit(TenureUnit.MONTH);
        tenure.setValue(2);
        double emi = OfferScreenUtils.getEmi(loanAmount, tenure, roi);
        Assertions.assertEquals(emi, 5226.11, 0.5);
    }

    @org.junit.jupiter.api.Test
    void getEmiTest2() {
        Double loanAmount = 20000D;
        Double roi = 24.45D;
        Tenure tenure = new Tenure();
        tenure.setUnit(TenureUnit.MONTH);
        tenure.setValue(5);
        double emi = OfferScreenUtils.getEmi(loanAmount, tenure, roi);
        Assertions.assertEquals(emi, 4247.79, 0.5);
    }

}
