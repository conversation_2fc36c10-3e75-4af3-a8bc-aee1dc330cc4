package com.flipkart;

import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.SandboxWebhooksRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.api.response.v6.WebhooksResponse;
import com.flipkart.fintech.pinaka.service.ams.AxisResumeApplicationBuilder;
import com.flipkart.fintech.pinaka.service.core.actionfactory.ResumeActionBuilder;
import com.flipkart.fintech.pinaka.service.core.v6.EmploymentService;
import com.flipkart.fintech.pinaka.service.core.v6.LoanHandler;
import com.flipkart.fintech.pinaka.service.core.v6.impl.EventHandlerImpl;
import com.flipkart.fintech.winterfell.api.request.UpdatePartnerStateRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.AmsBridge;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.mockito.Mockito.when;

class EventHandlerImplTest {

    @Mock
    private ApplicationService applicationService;
    @Mock
    private LoanHandler loanHandler;
    @Mock
    private ResumeActionBuilder resumeActionBuilder;
    @Mock
    private AxisResumeApplicationBuilder axisResumeApplicationBuilder;
    @Mock
    private AmsBridge amsBridge;
    @Mock
    private PlClient plClient;
    @Mock
    private EmploymentService employmentService;

    private MerchantUser merchantUser;
    private ProductType productType;
    private ApplicationDataResponse applicationDataResponse;
    private EventHandlerImpl eventHandler;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        eventHandler = new EventHandlerImpl(applicationService, loanHandler, resumeActionBuilder,
                axisResumeApplicationBuilder, amsBridge, plClient, employmentService);

        applicationDataResponse = new ApplicationDataResponse();
        applicationDataResponse.setApplicationId("APP123");
        applicationDataResponse.setExternalUserId("user-xyz");
        applicationDataResponse.setApplicationType("PL");
        applicationDataResponse.setTenant(Tenant.CALM);
        applicationDataResponse.setSmUserId("sm-user-1");
        applicationDataResponse.setApplicationState("INITIATED");
        applicationDataResponse.setMerchantId("MERCHANT123");
        applicationDataResponse.setPartnerState("PENDING");
        applicationDataResponse.setPartnerSubState("DOC_UPLOAD");
        applicationDataResponse.setCreatedAt(new Date());
        applicationDataResponse.setUpdatedAt(new Date());
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("financial_provider", "FIBE");
        applicationData.put("leadId", "XX22");
        applicationDataResponse.setApplicationData(applicationData);
    }

    @Test
    void testSubmitSandboxLenderEvent_successFlow() throws Exception {
        SandboxWebhooksRequest request = new SandboxWebhooksRequest();
        request.setLspApplicationId("APP123");
        request.setApplicationState("APPROVED");
        request.setStateTransitionTime(1727272727223L);

        when(applicationService.fetchApplicationData(eq("APP123"), any())).thenReturn(applicationDataResponse);

        WebhooksResponse response = eventHandler.submitSandboxLenderEvent(request);

        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        verify(applicationService).fetchApplicationData(eq("APP123"), any());
        verify(applicationService).updatePartnerState(any(UpdatePartnerStateRequest.class));
    }

    @Test
    void testSubmitSandboxLenderEvent_userCancelled_withDiscardError() throws Exception {
        SandboxWebhooksRequest request = new SandboxWebhooksRequest();
        request.setLspApplicationId("APP123");
        request.setApplicationStatus("USER_CANCELLED");
        request.setStateTransitionTime(1727272727223L);

        when(applicationService.fetchApplicationData(eq("APP123"), any())).thenReturn(applicationDataResponse);
        doThrow(new RuntimeException("discard error"))
                .when(applicationService)
                .discardApplication(any(MerchantUser.class), eq("APP123"));

        WebhooksResponse response = eventHandler.submitSandboxLenderEvent(request);

        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());
        verify(applicationService).fetchApplicationData(eq("APP123"), any());
        verify(applicationService).updatePartnerState(any(UpdatePartnerStateRequest.class));
    }

    @Test
    void testSubmitSandboxLenderEvent_failureInFetch() throws Exception {
        SandboxWebhooksRequest request = new SandboxWebhooksRequest();
        request.setLspApplicationId("APP123");
        when(applicationService.fetchApplicationData(eq("APP123"), any())).thenThrow(new RuntimeException("fetch error"));
        WebhooksResponse response = eventHandler.submitSandboxLenderEvent(request);
        assertNotNull(response);
        assertEquals(Status.FAILURE, response.getStatus());
        verify(applicationService).fetchApplicationData(eq("APP123"), any());
    }

    @Test
    void testSubmitSandboxLenderEvent_userCancelled_discardSuccess() throws Exception {
        SandboxWebhooksRequest request = new SandboxWebhooksRequest();
        request.setLspApplicationId("APP123");
        request.setApplicationStatus("USER_CANCELLED");
        request.setStateTransitionTime(1727272727223L);

        when(applicationService.fetchApplicationData(eq("APP123"), any()))
                .thenReturn(applicationDataResponse);
        when(applicationService.discardApplication(any(MerchantUser.class), eq("APP123")))
                .thenReturn(true);

        WebhooksResponse response = eventHandler.submitSandboxLenderEvent(request);

        assertNotNull(response);
        assertEquals(Status.SUCCESS, response.getStatus());

        verify(applicationService).fetchApplicationData(eq("APP123"), any());
        verify(applicationService).updatePartnerState(any(UpdatePartnerStateRequest.class));
        verify(applicationService).discardApplication(any(MerchantUser.class), eq("APP123"));
    }

}
