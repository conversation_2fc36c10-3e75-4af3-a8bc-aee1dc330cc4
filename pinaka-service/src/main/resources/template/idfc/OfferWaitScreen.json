{"items": [{"title": {"type": "RichTextValue", "text": "Choosing a longer loan tenure will help lower your monthly EMIs"}, "subTitle": {"type": "RichTextValue", "text": "Choosing a longer loan tenure will help lower your monthly EMIs"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/00b4b758-77cc-49ef-9268-11c7ef17af7e.png?q={@quality}", "height": 0, "type": "ImageValue", "width": 0}, "type": "ImageTextValue"}, {"title": {"type": "RichTextValue", "text": "Paying your EMIs on time, will improve your credit score"}, "subTitle": {"type": "RichTextValue", "text": "Paying your EMIs on time, will improve your credit score"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/a5a41d35-ba75-4326-93f8-63907112dc0f.png?q={@quality}", "height": 0, "type": "ImageValue", "width": 0}, "type": "ImageTextValue"}, {"title": {"type": "RichTextValue", "text": "Choosing a shorter loan tenure will help save more on interest"}, "subTitle": {"type": "RichTextValue", "text": "Choosing a shorter loan tenure will help save more on interest"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/89ec64b7-c62a-4d92-ae9c-3686d9db4ad1.png?q={@quality}", "height": 0, "type": "ImageValue", "width": 0}, "type": "ImageTextValue"}], "footerNote": {"text": "This takes around 60 seconds.", "subText": "Do not press back or refresh"}, "pollingContext": {"action": {"params": {}, "screenType": "multiWidgetPage", "tracking": {}, "type": "POLLING", "url": "/api/sm/1/application/state"}, "interval": 2000, "threshold": 300000}, "progressBarMetaData": {"progressBarAColor": "#2A55E5", "progressBarBColor": "#F0F5FF"}, "fallbackValue": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/10/2023/554b30c9-4309-4815-ac8b-2ccb42c51a4a.png?q={@quality}", "height": 192, "width": 192, "aspectRatio": "1:1", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Something went wrong", "style": {"color": "#000000", "fontSize": 18, "fontWeight": "bold", "textAlign": "center", "lineHeight": 28}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Our team is working hard to fix it, please check back again later", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}