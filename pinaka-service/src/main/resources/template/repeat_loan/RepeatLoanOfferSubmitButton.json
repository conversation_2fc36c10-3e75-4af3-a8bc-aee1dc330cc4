{"submitButton": {"type": "SubmitButtonValue", "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"utmSource": "REPEAT_LOAN_LANDING_PAGE_BANNER"}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__STATUS_ACTION", "url": "/api/fpg/1/action/view", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue Application"}}, "secondaryButtons": [{"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "", "taskKey": "", "applicationId": "", "taskId": "", "token": ""}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__STATUS_ACTION", "url": "/api/fpg/1/action/view", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "View loans", "buttonColor": "#FFFFFF", "borderColor": "transparent", "buttonTextColor": "var(--primary)"}}]}}