{"subWidget": null, "clientPowered": false, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": null, "trackingData": null, "value": {"borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000", "type": "RichTextButtonValue", "title": "Unlock offer now"}, "action": {"type": "CLIENT__INLINE_NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "6/pl/apply-now", "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F9FAFB", "orientation": "", "theme": "light"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "", "pageSubtitle": "", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2502041932335592866489436724399367995", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "25b04894-2a9b-4b55-b6aa-6343b6654005", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 0, "widgetWidth": 12}, "dataId": "-********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "REVIEW_PAGE_1", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": " ", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "url": "https://rukminim1.flixcart.com/www/288/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/288/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "height": 144, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "width": 288}}, "subTitle": {"text": "Make sure your name  is as per PAN", "style": {"fontFamily": "<PERSON><PERSON>", "color": "#4D43FE", "fontSize": 20, "lineHeight": 24, "letterSpacing": "2", "width": 100}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "Name", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "lineHeight": 20, "letterSpacing": "2", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "lineHeight": 18, "letterSpacing": "1", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "lineHeight": 16, "letterSpacing": 0}}, "value": "<PERSON>"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "9413439226", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "lineHeight": 20, "letterSpacing": "2", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "lineHeight": 18, "letterSpacing": 0}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "lineHeight": 16, "letterSpacing": 0}}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "-RsPa5_sMrCj0E6Q", "taskKey": "reviewPage1", "applicationId": "APP2502041932335592866489436724399367995", "taskId": "2sJ11aBS3pipSBM-", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCCyfyZEwSXJ45Pc2h9AZ52JmAI9isC9V/qJcIGHXfrJLqsWQPS7T5sKIyfxxIR2NJqBicz3dPc+RQnPQMZ9DAX"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form?page=review_details_2", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Confirm", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "borderRadius": 8, "richText": {"type": "RichTextValue", "style": {"color": "#1D2939", "fontSize": 18, "lineHeight": 22, "fontFamily": "<PERSON><PERSON>"}}, "text": "Continue"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I hereby agree and apply for L&T and further accept that."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": "NO_SHADOW"}}, "tracking": {}}}]}}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": {"publicKey": null, "keyId": null}, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": null, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": null}}