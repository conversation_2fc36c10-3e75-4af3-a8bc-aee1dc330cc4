{"orientation": "VERTICAL_CARD_ANIMATION", "renderableComponents": [{"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "4px", "borderRadius": "8px", "backgroundImage": "linear-gradient(to right, #DDFAE8, #F9FAFB)", "padding": "8px"}, "subStatusText": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Get a ₹500 Flipkart voucher", "textColor": "#667085"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "once the cash is in your account", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "4px", "borderRadius": "8px", "background": "linear-gradient(90deg, #E5E8FD 0%, #F9FAFB 100%)", "padding": "8px"}, "subStatusText": null, "tagValue": null, "title": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "3-10 months of easy EMI's", "textColor": "#344054"}}, "description": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "repay as per your comfort", "textColor": "#344054"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "4px", "borderRadius": "8px", "backgroundImage": "linear-gradient(to right, #FFE5FC80 0%, #FFE5FC80 50%)", "padding": "8px"}, "subStatusText": null, "tagValue": null, "title": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Safe and secure cash", "textColor": "#344054"}}, "description": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Trusted by over 20L+ customers", "textColor": "#344054"}}, "viewType": "PRIMITIVE_CARD"}}]}