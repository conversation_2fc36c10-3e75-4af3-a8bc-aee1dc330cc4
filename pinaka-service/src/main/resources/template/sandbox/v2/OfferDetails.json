{"formId": "LOAN_EMI_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"customFieldData": {"customFieldType": "BANK_OFFER_FIELD_V2", "amountSlider": {"value": {"type": "RangeFormFieldValue", "defaultValue": 350000, "disabled": false, "formFieldType": "RANGE", "label": "Your maximum loan amount is", "mandatory": true, "minValue": 100000, "maxValue": 500000, "name": "amountSlider", "stepperValue": {"stepperUnit": "RUPEE", "stepSize": 5000, "type": "StepperValue"}}}, "amountRangeOffers": [{"charges": [{"chargeType": "PROCESSING_FEE", "name": {"text": "Processing fee"}, "gst": 18, "type": "PERCENTAGE", "value": 0.5}, {"chargeType": "STAMP_DUTY", "name": {"text": "Stamp duty"}, "type": "FLAT", "value": 200}], "interest": {"value": 12}, "max": 200000, "min": 100000, "repaymentDetails": {"startDate": "5 April 2024", "endDate": "5 April 2029", "tenure": {"value": 60, "unit": "MONTH"}}}, {"charges": [{"chargeType": "PROCESSING_FEE", "name": {"text": "Processing fee"}, "gst": 18, "type": "PERCENTAGE", "value": 0.5}, {"chargeType": "STAMP_DUTY", "name": {"text": "Stamp duty"}, "type": "FLAT", "value": 200}], "interest": {"value": 12}, "max": 200000, "min": 100000, "repaymentDetails": {"startDate": "5 April 2024", "endDate": "5 April 2028", "tenure": {"value": 48, "unit": "MONTH"}}}, {"charges": [{"chargeType": "PROCESSING_FEE", "name": {"text": "Processing fee"}, "gst": 18, "type": "PERCENTAGE", "value": 0.5}, {"chargeType": "STAMP_DUTY", "name": {"text": "Stamp duty"}, "type": "FLAT", "value": 200}], "interest": {"value": 12}, "max": 200000, "min": 100000, "repaymentDetails": {"startDate": "5 April 2024", "endDate": "5 April 2027", "tenure": {"value": 36, "unit": "MONTH"}}}, {"charges": [{"chargeType": "PROCESSING_FEE", "name": {"text": "Processing fee"}, "gst": 18, "type": "PERCENTAGE", "value": 0.5}, {"chargeType": "STAMP_DUTY", "name": {"text": "Stamp duty"}, "type": "FLAT", "value": 200}], "interest": {"value": 12}, "max": 200000, "min": 100000, "repaymentDetails": {"startDate": "5 April 2024", "endDate": "5 April 2026", "tenure": {"value": 24, "unit": "MONTH"}}}, {"charges": [{"chargeType": "PROCESSING_FEE", "name": {"text": "Processing fee"}, "gst": 18, "type": "PERCENTAGE", "value": 0.5}, {"chargeType": "STAMP_DUTY", "name": {"text": "Stamp duty"}, "type": "FLAT", "value": 200}], "interest": {"value": 12}, "max": 200000, "min": 100000, "repaymentDetails": {"startDate": "5 April 2024", "endDate": "5 April 2025", "tenure": {"value": 12, "unit": "MONTH"}}}], "accountAggregatorAction": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": {"keyId": "sumo-end-key", "publicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmCrV0vRs4d+9pufICdn/QVYfmu0+4ZHqh+qRxLXxPqOUwCMJnVq2yT27JYoEOAe8xPXyQVIR6NM8+XojcO/rKZxPOV7iVfB+mcoZ06EaMp/S4NPJs+TjbEnSoAMJ7OL5PPQKmFGiKwAYQLrcAj08Fu0AlLDBUXrtg2i/W/occxZQnJTq0dUPoRCRb6YYD7DwezWGrHaGZUffzzYvDaBB1e0tBgpPF8HZ7K359xT6w6haUuYw9bnDu4FqW3rv82upU3dexi11txOUbuE4Xwg/4uVb0kmsgCDRVwafKx090cjJWbuHJ3ZcMzekJc/ueuqUr1dx7PJX1ClMhDhcMnRs4QIDAQAB"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"accountAggregator": ""}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__FORM_INTERACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "value": {"type": "RichTextButtonValue", "title": "Want more?"}}}, "disabled": false, "formFieldType": "CUSTOM", "label": "", "name": "bankOffer", "mandatory": true}}], "submitButton": {"type": "SubmitButtonValue", "button": {"action": {"url": "/api/sm/1/application/form", "params": {}, "loaderContent": {"type": "ANNOUNCEMENT_V2", "content": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262, "aspectRatio": "262:185", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Your offer is being saved... Please wait", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Do not press back or switch apps.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "type": "AnnouncementV2Value"}}}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue"}}, "lenderLogo": {"value": {"type": "ImageValue", "alternateText": "Go to top", "dynamicImageUrl": "", "height": 25, "width": 312}}}, "resultStoreKey": null, "subTitle": null, "title": null}