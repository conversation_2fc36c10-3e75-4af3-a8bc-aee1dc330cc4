INSERT INTO `merchants` (`merchant_key`, `merchant_name`, `status`, `created_at`, `updated_at`, `mod_count`)
VALUES
	('mp_flipkart', 'marketplace flipkart', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0);


INSERT INTO `product_configs` (`name`, `created_at`, `updated_at`, `product`, `lender`, `data`)
VALUES
	('axisCreditCardBins', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS', '[\"53702800\",\"46411805\",\"47186002\",\"53056201\",\"45145702\",\"41114607\",\"45145701\",\"46411801\",\"53056206\",\"53056208\",\"41114600\",\"41114601\",\"41114602\",\"41114603\",\"41114603\",\"41114604\",\"41114605\",\"41114606\",\"45145600\",\"45145602\",\"45145604\",\"45145700\",\"46411800\",\"46411900\",\"47186000\",\"47186001\",\"47186003\",\"47186100\",\"47186101\",\"47186102\",\"47186300\",\"47186301\",\"47186302\",\"52417800\",\"52417810\",\"52417811\",\"52450800\",\"52451200\",\"53056200\",\"53056202\",\"53056203\",\"53056205\",\"55934200\"]'),
	('cbcTnc', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS', '<h3>Terms & Conditions</h3>\n<p>I have read and fully accept the <a href=\"https://www.flipkart.com/pages/tnc-axis-cc-auth\">Credit Card authorization statement</a> , <a href=\"https://www.flipkart.com/pages/tnc-axis-member-agreement\">Card member agreement</a>, and <a href=\"https://www.flipkart.com/pages/cbc-axis-tnc\">Most Important terms and conditions</a>.</p>\n<p>I hereby confirm that I have read and consent to Flipkart\'s Terms and Conditions and Privacy Policy.</p>\n\n<p>I understand that in accordance with Flipkart’s Privacy Policy, information that is collected shall be stored and used for lawful purposes which may include use for their internal operations, fulfilling service requests, monitoring/maintaining functionality and security of services and promoting, marketing, facilitating, improving, developing, or otherwise providing services associated with the card by Flipkart. I also authorize Flipkart Internet Pvt Ltd to store my card details to facilitate faster payments on their platform.</p>\n\n<p>I authorize Flipkart Internet Pvt. Ltd. to exchange or share my information with Axis Bank for issuance of credit card and services associated with it.</p>\n\n<p>I further agree and consent for Axis Bank to share my information or data (including sensitive personal data) with Flipkart Internet Pvt Ltd in connection with the services for promoting, marketing, facilitating, improving, developing, or otherwise providing services associated with the card.</p>');


INSERT INTO `Lender` (`lender_id`, `lender_name`, `merchant_id`, `state`, `lender_type`, `stamp_created`, `stamp_modified`, `mod_count`)
VALUES
	('1', 'KISSHT', 'mp_flipkart', 'ACTIVE', 'BANK', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
	('2', 'INDIA_BULLS', 'mp_flipkart', 'ACTIVE', 'BANK', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0);

--populating trigger_configs and trigger_communication_configs
--Note : trigger_configs 'id' column is being used as foreign key in trigger_communication_configs table that'why below format of queries

INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NO_APPLICATION_STATE', '{\n\"relativeScheduleTime\" : 300,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"message\": \"Hey ! Don\'t miss out on applying for the Flipkart Axis Bank Credit Card ! Get it in less than 3 mins* !!!\",\n	\"title\": \"Flipkart Axis Bank Credit Card\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Oh, Oh! Couldn\'t Apply? Don\'t miss out on the Flipkart Axis Bank Credit Card that offers unlmited cashback! Get it in less than 3 mins*. Apply Now! https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETCC_APPLICATION_CREATED', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey! You are just 2 steps away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume your Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"You are just 2 steps away from getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"Almost there!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETCC_FETCH_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETCC_USER_INPUT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` ( `state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETCC_APPLY_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"You\'re just seconds away from getting unlimited Cashback with the Flipkart Axis Bank Credit Card. Resume Application. Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just seconds away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETB_PA_FETCH_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETB_PA_USER_INPUT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETB_PA_APPLY_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"You\'re just seconds away from getting unlimited Cashback with the Flipkart Axis Bank Credit Card. Resume Application. Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just seconds away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETB_NPA_FETCH_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETB_NPA_USER_INPUT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('ETB_NPA_APPLY_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"You\'re just seconds away from getting unlimited Cashback with the Flipkart Axis Bank Credit Card. Resume Application. Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just seconds away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NTB_USER_INPUT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
    VALUES
    	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Hey ! You\'re just 1 step away from getting unlimited cashback with the Flipkart Axis Bank Credit Card. Resume Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
    	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just 1 step away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NTB_APPLY_CONSENT', '{\n\"relativeScheduleTime\" : 600,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"You\'re just seconds away from getting unlimited Cashback with the Flipkart Axis Bank Credit Card. Resume Application. Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"message\": \"From getting unlimited cashback with the Flipkart Axis Bank Credit Card.\",\n	\"title\": \"You\'re just seconds away!\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/flipkart-axis-bank-credit-card-store\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('TTL_EXPIRY', '{\n\"relativeScheduleTime\" : 300,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');
INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"message\": \"Your credit card application has timed out. Restart Application!!\",\n	\"title\": \"Flipkart Axis Bank Credit Card\",\n	\"subType\": \"CBC\",\n	\"url\":\"https://www.flipkart.com/pages/mw/cbc/apply_now/loader\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('SMS', '{\n	\"templateId\": \"STNIT550L\",\n	\"message\": \"Oops ! Your application has timed out ! Don\'t miss out on unlimited cashbacks with the Flipkart Axis Bank Credit Card ! Restart Application ! Click here https://www.flipkart.com/flipkart-axis-bank-credit-card-store\",\n	\"smsContext\": \"CBC\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


--********************** SQL QUERIES FOR CBC NPS SURVEY ****************

INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_APPLICATION_IN_PROGRESS', '{\n\"relativeScheduleTime\" : 2160000,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_APPLIED_SURVEY_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-ASI0S\",\n	\"surveyId\": \"SU-ASI0S\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_APPLIED_SURVEY_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-ASI0S\",\n	\"surveyId\": \"SU-ASI0S\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_IPA_REJECTION', '{\n\"relativeScheduleTime\" : 0,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_IPA_REJECTED_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-KJQ8V\",\n	\"surveyId\": \"SU-KJQ8V\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_IPA_REJECTED_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-KJQ8V\",\n	\"surveyId\": \"SU-KJQ8V\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_FINAL_REJECTION', '{\n\"relativeScheduleTime\" : 0,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_REJECTED_SURVEY_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-7UWS1\",\n	\"surveyId\": \"SU-7UWS1\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_REJECTED_SURVEY_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-7UWS1\",\n	\"surveyId\": \"SU-7UWS1\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());

INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_APPROVED', '{\n\"relativeScheduleTime\" : 864000,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_APPROVED_SURVEY_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-7UWS1\",\n	\"surveyId\": \"SU-7UWS1\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_APPROVED_SURVEY_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-7UWS1\",\n	\"surveyId\": \"SU-7UWS1\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());

INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_FIRST_POST_APPROVAL', '{\n\"relativeScheduleTime\" : 6912000,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_ADOPTOR_SURVEY_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-EDFOE\",\n	\"surveyId\": \"SU-EDFOE\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_ADOPTOR_SURVEY_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-EDFOE\",\n	\"surveyId\": \"SU-EDFOE\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());



INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_SECOND_POST_APPROVAL', '{\n\"relativeScheduleTime\" : 20736000,\n\"maxExecutionLimit\" : 1 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_ADOPTOR_SURVEY_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-EDFOE\",\n	\"surveyId\": \"SU-EDFOE\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_ADOPTOR_SURVEY_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-EDFOE\",\n	\"surveyId\": \"SU-EDFOE\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());


--NOTE : MAX EXECUTION LIMIT FOR NPS_SUBSEQUENT_POST_APPROVAL IS INF (but setting it as 100 for 50 years) as we are
--triggering this event after every 6 month untill card is BLOCKED
INSERT INTO `trigger_configs` (`state`, `data`, `created_at`, `updated_at`, `product_type`, `lender`)
VALUES
	('NPS_SUBSEQUENT_POST_APPROVAL', '{\n\"relativeScheduleTime\" : 15552000,\n\"maxExecutionLimit\" : 100 \n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CBC', 'AXIS');

INSERT INTO `trigger_communication_configs` (`communication_mode`, `data`, `created_at`, `updated_at`, `trigger_config_id`)
VALUES
	('PN', '{\n	\"eventName\": \"CBC_ADOPTOR_SURVEY_EMAIL\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-EDFOE\",\n	\"surveyId\": \"SU-EDFOE\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID()),
	('PN', '{\n	\"eventName\": \"CBC_ADOPTOR_SURVEY_INAPP\",\n	\"referenceId\": \"cbc-nps-survey\",\n	\"evenType\": \"SURVEY_EVENT\",\n	\"surveyUrl\":\"https://www.flipkart.com/ama?id=SU-EDFOE\",\n	\"surveyId\": \"SU-EDFOE\",\n	\"message\": \"\"\n}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, LAST_INSERT_ID());
