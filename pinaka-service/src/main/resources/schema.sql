# ************************************************************
# Sequel Pro SQL dump
# Version 4541
#
# http://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 5.7.22)
# Database: pinaka
# Generation Time: 2019-05-19 16:31:48 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table addresses
# ------------------------------------------------------------

DROP TABLE IF EXISTS `addresses`;

CREATE TABLE `addresses` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL,
  `address_line1` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `address_line2` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `city` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `postal_code` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `address_type` varchar(10) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `state` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `application_id` (`application_id`),
  CONSTRAINT `application_id` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table application_forms
# ------------------------------------------------------------

DROP TABLE IF EXISTS `application_forms`;

CREATE TABLE `application_forms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL,
  `email_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `first_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `pan_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `gender` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `pan_verified_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `highest_education_level` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `employment_status` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `occupation` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `job_title` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `employer_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `salary` double DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `application` (`application_id`),
  CONSTRAINT `application` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table applications
# ------------------------------------------------------------

DROP TABLE IF EXISTS `applications`;

CREATE TABLE `applications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `borrower_id` bigint(20) unsigned NOT NULL,
  `status` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expiry_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `reject_reason` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `aadhaar_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `tracking_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `lender` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `masked_aadhaar` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `external_ref_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tracking_id` (`tracking_id`),
  KEY `borrower` (`borrower_id`),
  KEY `tracking_id` (`tracking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table borrowers
# ------------------------------------------------------------

DROP TABLE IF EXISTS `borrowers`;

CREATE TABLE `borrowers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `enabled` tinyint(1) DEFAULT '1',
  `external_id` varchar(256) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `whitelist_id` bigint(20) unsigned NOT NULL,
  `metadata` text COLLATE utf8_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `external_id` (`external_id`(255)),
  KEY `whitelist_id_fk` (`whitelist_id`),
  CONSTRAINT `whitelist_id_fk` FOREIGN KEY (`whitelist_id`) REFERENCES `whitelist` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=168258650 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


# Dump of table communications
# ------------------------------------------------------------

DROP TABLE IF EXISTS `communications`;

CREATE TABLE `communications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL,
  `display_id` varchar(50) DEFAULT '',
  `type` varchar(64) DEFAULT NULL,
  `message_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `comm_application` (`application_id`),
  CONSTRAINT `comm_application` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;



# Dump of table ConfigurableParameter
# ------------------------------------------------------------

DROP TABLE IF EXISTS `ConfigurableParameter`;

CREATE TABLE `ConfigurableParameter` (
  `id` varchar(64) NOT NULL DEFAULT '',
  `type` varchar(16) NOT NULL DEFAULT '',
  `display_name` varchar(64) NOT NULL DEFAULT '',
  `multi_input` varchar(4) NOT NULL DEFAULT '',
  `data_type` varchar(16) NOT NULL DEFAULT '',
  `callback_uri` varchar(512) DEFAULT '',
  `searchable` varchar(4) NOT NULL DEFAULT '',
  `advanced` varchar(4) NOT NULL DEFAULT '',
  `enumerated_values` text,
  `stamp_created` timestamp NULL DEFAULT NULL,
  `stamp_modified` timestamp NULL DEFAULT NULL,
  `configuration_context` varchar(16) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;



# Dump of table Lender
# ------------------------------------------------------------

DROP TABLE IF EXISTS `Lender`;

CREATE TABLE `Lender` (
  `lender_id` varchar(64) NOT NULL DEFAULT '',
  `lender_name` varchar(11) DEFAULT NULL,
  `merchant_id` varchar(11) DEFAULT NULL,
  `state` varchar(11) DEFAULT NULL,
  `lender_type` varchar(11) NOT NULL DEFAULT '',
  `stamp_created` timestamp NULL DEFAULT NULL,
  `stamp_modified` timestamp NULL DEFAULT NULL,
  `mod_count` tinyint(11) DEFAULT NULL,
  PRIMARY KEY (`lender_id`),
  KEY `lender_id` (`lender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table LenderApproval
# ------------------------------------------------------------

DROP TABLE IF EXISTS `LenderApproval`;

CREATE TABLE `LenderApproval` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lender_id` varchar(250) NOT NULL,
  `applications_approved` int(11) DEFAULT NULL,
  `applications_rejected` int(11) DEFAULT NULL,
  `approval_weight` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `lender_id` (`lender_id`),
  CONSTRAINT `lenderapproval_ibfk_1` FOREIGN KEY (`lender_id`) REFERENCES `Lender` (`lender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table LenderFeatures
# ------------------------------------------------------------

DROP TABLE IF EXISTS `LenderFeatures`;

CREATE TABLE `LenderFeatures` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lender_id` varchar(250) NOT NULL,
  `feature` varchar(250) NOT NULL,
  `source` varchar(250) NOT NULL,
  `weight` int(11) DEFAULT NULL,
  `value` varchar(250) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `lender_id` (`lender_id`),
  CONSTRAINT `lenderfeatures_ibfk_1` FOREIGN KEY (`lender_id`) REFERENCES `Lender` (`lender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table locks
# ------------------------------------------------------------

DROP TABLE IF EXISTS `locks`;

CREATE TABLE `locks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `resource_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `lock_expiry_time` datetime NOT NULL,
  `caller_key` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_resource_key` (`resource_name`,`key`),
  KEY `index_locks_on_caller_key` (`caller_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table merchants
# ------------------------------------------------------------

DROP TABLE IF EXISTS `merchants`;

CREATE TABLE `merchants` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_key` varchar(64) NOT NULL,
  `merchant_name` varchar(255) DEFAULT NULL,
  `status` varchar(64) NOT NULL DEFAULT '',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `mod_count` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table outbound_message_groups
# ------------------------------------------------------------

DROP TABLE IF EXISTS `outbound_message_groups`;

CREATE TABLE `outbound_message_groups` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `expires_at` datetime NOT NULL,
  `sidelined` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `exchange_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_outbound_message_groups_on_group_id_and_exchange_name` (`group_id`,`exchange_name`),
  KEY `index_outbound_message_groups_on_expires_at_and_sidelined` (`expires_at`,`sidelined`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table outbound_messages
# ------------------------------------------------------------

DROP TABLE IF EXISTS `outbound_messages`;

CREATE TABLE `outbound_messages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `message_id` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `relayed` tinyint(1) DEFAULT NULL,
  `relayed_at` datetime DEFAULT NULL,
  `exchange_name` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `message` text COLLATE utf8_unicode_ci,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `inbound_message_id` int(11) DEFAULT NULL,
  `exchange_type` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `app_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `correlation_id` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `group_id` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `http_method` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `http_uri` varchar(4096) COLLATE utf8_unicode_ci DEFAULT NULL,
  `reply_to` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `reply_to_http_method` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `reply_to_http_uri` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `txn_id` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `routing_key` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `context` text COLLATE utf8_unicode_ci,
  `destination_response_status` int(11) DEFAULT NULL,
  `relay_error` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `retries` int(11) DEFAULT '0',
  `custom_headers` text COLLATE utf8_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_outbound_messages_on_message_id` (`message_id`),
  KEY `index_outbound_messages_on_relayed` (`relayed`),
  KEY `exchange_name_index` (`exchange_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table rule_attributes
# ------------------------------------------------------------

DROP TABLE IF EXISTS `rule_attributes`;

CREATE TABLE `rule_attributes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `group` int(11) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `name` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `value` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
  `source` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `grp_name` (`group`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table rule_configurations
# ------------------------------------------------------------

DROP TABLE IF EXISTS `rule_configurations`;

CREATE TABLE `rule_configurations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `product_type` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `lender_id` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `rule_config` (`name`,`product_type`),
  KEY `lender_id` (`lender_id`),
  CONSTRAINT `rule_configurations_ibfk_1` FOREIGN KEY (`lender_id`) REFERENCES `Lender` (`lender_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


# Dump of table rules
# ------------------------------------------------------------

DROP TABLE IF EXISTS `rules`;

CREATE TABLE `rules` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `active` tinyint(1) DEFAULT '1',
  `rule_sequence` int(11) NOT NULL,
  `rule_attr_grp` bigint(20) unsigned DEFAULT NULL,
  `expression` text COLLATE utf8_unicode_ci NOT NULL,
  `rule_configuration_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `rule_config` (`rule_configuration_id`),
  CONSTRAINT `rule_config` FOREIGN KEY (`rule_configuration_id`) REFERENCES `rule_configurations_old` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table sub_applications
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sub_applications`;

CREATE TABLE `sub_applications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL,
  `status` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` varchar(200) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `data` text COLLATE utf8_unicode_ci,
  `external_ref_id` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sub_application_application` (`application_id`),
  KEY `external_ref_id` (`external_ref_id`),
  CONSTRAINT `sub_application_application` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



# Dump of table whitelist
# ------------------------------------------------------------

DROP TABLE IF EXISTS `whitelist`;

CREATE TABLE `whitelist` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `whitelist_name` varchar(64) NOT NULL,
  `whitelist_desc` varchar(255) DEFAULT NULL,
  `product_type` varchar(64) NOT NULL DEFAULT '',
  `merchant_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `meta_data` text,
  `rule_configs` text,
  `enabled` tinyint(1) DEFAULT NULL,
  `lender` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `whitelist_name_product_unique` (`whitelist_name`,`product_type`),
  KEY `merchant_id_fk_2` (`merchant_id`),
  CONSTRAINT `merchant_id_fk_2` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8;


# Dump of table workflows
# ------------------------------------------------------------

DROP TABLE IF EXISTS `workflows`;

CREATE TABLE `workflows` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `status` varchar(20) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `correlation_id` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL,
  `application_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `workflow_application` (`application_id`),
  CONSTRAINT `workflow_application` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Dump of table tokens
# ------------------------------------------------------------
DROP TABLE IF EXISTS `tokens`;

CREATE TABLE `tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` varchar(64) NOT NULL DEFAULT '',
  `product_type` varchar(64) NOT NULL DEFAULT '',
  `lender` varchar(64) NOT NULL DEFAULT '',
  `token` varchar(255) NOT NULL DEFAULT '',
  `device_id` varchar(255),
  `status` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `product_lender_token` (`product_type`,`lender`,`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Dump of table one_time_passwords
# ------------------------------------------------------------
DROP TABLE IF EXISTS `one_time_passwords`;

CREATE TABLE `one_time_passwords` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `reference_id` varchar(255) NOT NULL,
  `otp` varchar(10),
  `mobile_number` varchar(20) NOT NULL,
  `attempt_count` tinyint(2) DEFAULT NULL,
  `resend_count` tinyint(2) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `validation_token` varchar(255),
  `device_id` varchar(255),
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `application_id` (`application_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Dump of table product_configs
# ------------------------------------------------------------
DROP TABLE IF EXISTS `product_configs`;

CREATE TABLE `product_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(256) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `product` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `lender` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
  `data` text COLLATE utf8_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `product_config_key` (`name`(255),`product`,`lender`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Dump of table pincode
# ------------------------------------------------------------
DROP TABLE IF EXISTS `pincode`;

CREATE TABLE `pincode` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `lender` varchar(50) DEFAULT NULL,
  `pincode` varchar(10) DEFAULT NULL,
  `blacklisted` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `nudges`;

CREATE TABLE `nudges` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `application_ref_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `product_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `request_data` varchar(4096) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `response_data` varchar(4096) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `pincode_groups`;

CREATE TABLE `pincode_groups` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `region` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `pincode` varchar(4096) COLLATE utf8_unicode_ci ,
  `holidays` varchar(4096) COLLATE utf8_unicode_ci ,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2194 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `capacities`;

CREATE TABLE `capacities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `slot_details` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `group_id` bigint(20) NOT NULL,
  `initial_capacity` bigint(20) NOT NULL,
  `current_capacity` bigint(20) NOT NULL,
  `slot_date` timestamp  NULL ,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13735 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

# Dump of table company_name
# ------------------------------------------------------------
DROP TABLE IF EXISTS `company_name`;

CREATE TABLE `company_name` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `company_name` varchar(50) NOT NUll ,
  `occupation_type` varchar(20) NOT NUll ,
  `status` tinyint(1) NOT NUll,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

