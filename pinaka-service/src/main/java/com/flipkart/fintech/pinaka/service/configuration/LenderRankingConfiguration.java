package com.flipkart.fintech.pinaka.service.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;

public class LenderRankingConfiguration {

    @JsonProperty
    private int featureWeight;

    @JsonProperty
    private int approvalWeight;

    public int getFeatureWeight() {
        return featureWeight;
    }

    public void setFeatureWeight(int featureWeight) {
        this.featureWeight = featureWeight;
    }

    public int getApprovalWeight() {
        return approvalWeight;
    }

    public void setApprovalWeight(int approvalWeight) {
        this.approvalWeight = approvalWeight;
    }
}
