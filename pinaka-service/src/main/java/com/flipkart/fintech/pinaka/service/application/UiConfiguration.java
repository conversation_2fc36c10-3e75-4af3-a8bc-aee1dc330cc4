package com.flipkart.fintech.pinaka.service.application;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.Cohort;
import com.flipkart.fintech.pinaka.api.model.advanz.config.CohortInfoConfig;
import com.flipkart.fintech.pinaka.api.model.advanz.config.UiMetaDataConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UiConfiguration {

    List<Cohort> payLaterCohortPriorityList;

    List<Cohort> fsupCohortPriorityList;

    Map<Cohort, CohortInfoConfig> cohorts;

    Map<ApplicationStatus, UiMetaDataConfig> uiConfigMap;
}
