package com.flipkart.fintech.pinaka.service.application;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> 16/11/18
 */
public class TijoriConfiguration {
    @NotNull
    @JsonProperty("url")
    private String url;

    @NotNull
    @JsonProperty("clientName")
    private String clientName;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }
}
