package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.BankDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

public class BankDetailsFormTransformer {

    private BankDetailsPageDataSourceResponse bankDetailsPageDataSourceResponse;
    private static String genericFormjson;

    static {
        genericFormjson = TransformerUtils.readFileasString("template/idfc/BankDetails.json");

    }

    public GenericFormWidgetData buildWidgetData(BankDetailsPageDataSourceResponse pageDataSourceReponse)
            throws JsonProcessingException {
        bankDetailsPageDataSourceResponse = pageDataSourceReponse;
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(genericFormjson, GenericFormWidgetData.class);
        updateSubmitButton(genericFormWidgetData.getSubmitButton(),bankDetailsPageDataSourceResponse);
        return genericFormWidgetData;
    }

    private static void updateSubmitButton(SubmitButtonValue submitButton,BankDetailsPageDataSourceResponse bankDetailsPageDataSourceResponse) {
        Action action = submitButton.getButton().getAction();
        action.setParams(bankDetailsPageDataSourceResponse.getQueryParams());
        action.setEncryption(bankDetailsPageDataSourceResponse.getEncryptionData());
    }


}