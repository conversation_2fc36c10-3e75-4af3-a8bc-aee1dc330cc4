package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.Objects;

@CustomLog
public class OfferScreenTransformer {

    private static String offerJson;
    static {
        offerJson = TransformerUtils.readFileasString("template/sandbox/OfferScreen.json");
    }

    public AnnouncementV2WidgetData buildOfferScreen(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        try {
            AnnouncementV2WidgetData announcementV2WidgetData = ObjectMapperUtil.get().readValue(offerJson, AnnouncementV2WidgetData.class);
            String offer=null;
            if(!Objects.isNull(applicationDataResponse.getApplicationData())){
                GetOfferResponse sandboxOfferResponse = ObjectMapperUtil.get()
                        .convertValue(applicationDataResponse.getApplicationData().get("getOffer"),
                                GetOfferResponse.class);
                offer=String.valueOf(sandboxOfferResponse.getGeneratedOffer().getOffer().getMaxSanctionedAmount());
            }
            announcementV2WidgetData.getData().getValue().getTitle().getValue().setText(offer);

            announcementV2WidgetData.getData().getValue().getRichButton().getAction().setParams(WidgetTransformerUtils.getButtonParams(applicationDataResponse));
            return announcementV2WidgetData;
        } catch (Exception e) {
            log.error("AnnouncementV2WidgetData build offer page failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

}
