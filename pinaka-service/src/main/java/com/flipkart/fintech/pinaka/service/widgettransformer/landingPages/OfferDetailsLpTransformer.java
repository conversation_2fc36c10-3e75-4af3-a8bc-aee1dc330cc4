package com.flipkart.fintech.pinaka.service.widgettransformer.landingPages;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.OfferEntity;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.TenureUnit;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.service.pagedatasource.SandboxOfferScreenDataSource;
import com.flipkart.fintech.pinaka.service.response.SandboxOfferScreenResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.OfferScreenUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.AnnouncementCardWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ApprovedOfferWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.value.ApprovedOfferRepaymentValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.ApprovedOfferValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.AnnouncementCardWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.ApprovedOfferWidgetData;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils.getToken;

@CustomLog
public class OfferDetailsLpTransformer implements AnnouncementCardWidgetTransformer, ApprovedOfferWidgetTransformer {

    private static final String approvedOfferJson;
    private static final String anotherLenderAnnouncementJson;

    static {
        approvedOfferJson = TransformerUtils.readFileasString("template/landingPages/OfferDetailsApprovedOffer.json");
        anotherLenderAnnouncementJson = TransformerUtils.readFileasString("template/landingPages/OfferDetailsAnnouncementCard.json");
    }

    @Override
    public AnnouncementCardWidgetData buildAnnouncementCardWidgetData(ApplicationDataResponse applicationDataResponse) {
        AnnouncementCardWidgetData announcementCardWidgetData = null;
        try {
            announcementCardWidgetData = ObjectMapperUtil.get().readValue(anotherLenderAnnouncementJson, AnnouncementCardWidgetData.class);
            Map<String, Object> params = new HashMap<>();
            params.put("applicationId", applicationDataResponse.getApplicationId());
            params.put("token", getToken(applicationDataResponse.getApplicationId()));
            announcementCardWidgetData.getAnnouncementDetails().getButton().getAction().setParams(params);
            // set CALM__TRY_ANOTHER lender action here
        } catch (JsonProcessingException e) {
            log.error("Error in reading announcement card json");
            log.error(e.getMessage(), e);
        }
        return announcementCardWidgetData;
    }

    @Override
    public ApprovedOfferWidgetData buildApprovedOfferWidgetData(ApplicationDataResponse applicationDataResponse) {
        ApprovedOfferWidgetData approvedOfferWidgetData = null;
        try {
            approvedOfferWidgetData = ObjectMapperUtil.get().readValue(approvedOfferJson, ApprovedOfferWidgetData.class);
            SandboxOfferScreenResponse sandboxOfferScreenDataSource = new SandboxOfferScreenDataSource().getData(applicationDataResponse);
            updateOfferDetails(approvedOfferWidgetData, sandboxOfferScreenDataSource);
            updateButton(approvedOfferWidgetData, sandboxOfferScreenDataSource);
            // set CALM__STATUS_ACTION with Resume operation here
        } catch (JsonProcessingException e) {
            log.error("Error in reading announcement card json");
            log.error(e.getMessage(), e);
        }
        return approvedOfferWidgetData;
    }

    private void updateOfferDetails(ApprovedOfferWidgetData approvedOfferWidgetData, SandboxOfferScreenResponse sandboxOfferScreenDataSource) {
        GetOfferResponse offer = sandboxOfferScreenDataSource.getGetOfferResponse();
        ApprovedOfferValue approvedOfferValue = approvedOfferWidgetData.getOfferDetails();
        Double maxSanctionedAmount = offer.getGeneratedOffer().getOffer().getMaxSanctionedAmount();
        Objects.requireNonNull(approvedOfferValue.getOfferAmount()).get(1).setText(
                String.valueOf(maxSanctionedAmount));
        List<ApprovedOfferRepaymentValue> repaymentDetails =  approvedOfferValue.getRepaymentDetails();

        // set emi
        List<OfferEntity> offerEntities = offer.getGeneratedOffer().getOffer().getOfferTable();
        offerEntities = offerEntities.stream().filter(offerEntity -> Objects.equals(offerEntity.getSanctionedAmount().getMax(), maxSanctionedAmount))
                .collect(Collectors.toList());
        int tenure = 0;
        OfferEntity chosenOfferEntity = null;
        for(OfferEntity offerEntity: offerEntities){
            if(getTenureInMonths(offerEntity) > tenure){
                tenure = getTenureInMonths(offerEntity);
                chosenOfferEntity = offerEntity;
            }
        }

        Double emi = OfferScreenUtils.getEmi(maxSanctionedAmount, chosenOfferEntity.getTenure(), chosenOfferEntity.getRoi());

        // set emi
        approvedOfferValue.getRepaymentDetails().get(0).getValue().setText(String.valueOf(emi));
        // set tenure
        approvedOfferValue.getRepaymentDetails().get(1).getValue().setText(String.valueOf(chosenOfferEntity.getTenure().getValue()));
        // set roi
        approvedOfferValue.getRepaymentDetails().get(2).getValue().setText(String.valueOf(chosenOfferEntity.getRoi()));

        approvedOfferWidgetData.setOfferDetails(approvedOfferValue);

    }

    private void updateButton(ApprovedOfferWidgetData approvedOfferWidgetData, SandboxOfferScreenResponse response){
        Map<String, Object> queryParams = response.getQueryParams();
        Objects.requireNonNull((approvedOfferWidgetData.getOfferDetails()).getButton().getAction()).setParams(queryParams);
    }

    private int getTenureInMonths(OfferEntity offerEntity){
        if(offerEntity.getTenure().getUnit().equals(TenureUnit.YEAR)){
            return offerEntity.getTenure().getValue()*12;
        }
        return offerEntity.getTenure().getValue();
    }

}
