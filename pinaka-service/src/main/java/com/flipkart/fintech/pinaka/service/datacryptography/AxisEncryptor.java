package com.flipkart.fintech.pinaka.service.datacryptography;

import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.security.aes.AESService;
import lombok.experimental.UtilityClass;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;


@UtilityClass
public class AxisEncryptor {

    private static final String encryptKey = "cGlpRW5jcnlwdGlvbktleQ==";

    public IdentifyCustomerRequest getAxisEncryptedIdentifyCustomerRequest(IdentifyCustomerRequest request) {
        IdentifyCustomerRequest identifyCustomerRequest =  IdentifyCustomerRequest.builder()
                .panNumber(getAxisEncryptedField(request.getPanNumber()))
                .dob(formatDob(getDecryptedField(request.getDob())))
                .firstName(getAxisEncryptedField(request.getFirstName()))
                .lastName(getAxisEncryptedField(request.getLastName()))
                .gender(request.getGender())
                .shippingAddressId(request.getShippingAddressId())
                .loanPurpose(request.getLoanPurpose())
                .consentMetaData(request.getConsentMetaData())
                .houseNumber(getAxisEncryptedField(request.getHouseNumber()))
                .area(getAxisEncryptedField(request.getArea()))
                .city(getAxisEncryptedField(request.getCity()))
                .state(getAxisEncryptedField(request.getState()))
                .pincode(request.getPincode())
                .binScore(request.getBinScore())
                .panConsentProvided(request.isPanConsentProvided())
                .build();

        identifyCustomerRequest.setApplicationId(request.getApplicationId());
        identifyCustomerRequest.setAccountId(request.getAccountId());
        identifyCustomerRequest.setSmUserId(request.getSmUserId());
        identifyCustomerRequest.setRequestId(request.getRequestId());
        identifyCustomerRequest.setCategoryType(request.getCategoryType());

        return identifyCustomerRequest;
    }

    public EligibleOfferRequest getAxisEncryptedEligibleOfferRequest(EligibleOfferRequest request) {
        EligibleOfferRequest eligibleOfferRequest = EligibleOfferRequest.builder()
                .employmentType(request.getEmploymentType())
                .employerId(request.getEmployerId())
                .employerName(request.getEmployerName())
                .monthlyIncome(request.getMonthlyIncomeWithBonus())
                .industryId(request.getIndustryId())
                .industryName(request.getIndustryName())
                .annualTurnOver(request.getAnnualTurnOver())
                .binScore(request.getBinScore())
                .shippingAddressId(request.getShippingAddressId())
                .loanPurpose(request.getLoanPurpose())
                .consentMetaData(request.getConsentMetaData())
                .consentMetaDataList(request.getConsentMetaDataList())
                .houseNumber(getAxisEncryptedField(request.getHouseNumber()))
                .area(getAxisEncryptedField(request.getArea()))
                .city(getAxisEncryptedField(request.getCity()))
                .state(getAxisEncryptedField(request.getState()))
                .pincode(request.getPincode())
                .firstName(getAxisEncryptedField(request.getFirstName()))
                .lastName(getAxisEncryptedField(request.getLastName()))
                .panConsentProvided(request.isPanConsentProvided())
                .version(request.getVersion())
                .build();

        eligibleOfferRequest.setApplicationId(request.getApplicationId());
        eligibleOfferRequest.setAccountId(request.getAccountId());
        eligibleOfferRequest.setSmUserId(request.getSmUserId());
        eligibleOfferRequest.setRequestId(request.getRequestId());
        eligibleOfferRequest.setCategoryType(request.getCategoryType());

        return eligibleOfferRequest;
    }


    private String getAxisEncryptedField(String encryptedField) {
        return EncryptionUtil.encryptWithAes(getDecryptedField(encryptedField), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY);
    }

    private String getDecryptedField(String encryptedField) {
        byte[] plaintextBytes = AESService.decrypt(encryptKey, Base64.getDecoder().decode(encryptedField.getBytes(StandardCharsets.UTF_8)));
        return new String(plaintextBytes, StandardCharsets.UTF_8);
    }

    private String formatDob (String dob) {
        DateTimeFormatter outputDateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(dob, DateTimeFormatter.ofPattern("dd/MM/yyyy")).format(outputDateFormat);
    }

}
