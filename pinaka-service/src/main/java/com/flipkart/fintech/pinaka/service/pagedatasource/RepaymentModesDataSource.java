package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.api.model.pl.response.IfscResponse;
import com.flipkart.fintech.pinaka.service.response.RepaymentModesPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class RepaymentModesDataSource implements PageDataSource<RepaymentModesPageDataSourceResponse>{

    @Inject
    private static ConfigUtils configUtils;

    @Override
    public RepaymentModesPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        IfscResponse ifscResponse = ObjectMapperUtil.get()
                .convertValue(applicationDataResponse.getApplicationData().get("ifscDetails"),
                        IfscResponse.class);
        RepaymentModesPageDataSourceResponse repaymentModesPageDataSourceResponse = new RepaymentModesPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        repaymentModesPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        repaymentModesPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        repaymentModesPageDataSourceResponse.setIfscResponse(ifscResponse);
        return repaymentModesPageDataSourceResponse;
    }
}
