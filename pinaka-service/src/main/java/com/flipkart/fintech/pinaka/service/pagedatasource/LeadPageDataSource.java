package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.api.model.FullName;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.Utils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.profile.service.UpiUserService;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.upi.user.service.api.models.base.v1.enums.GenericSearchType;
import com.flipkart.upi.user.service.api.models.base.v1.request.search_management.GenericSearchRequestDTO;
import com.flipkart.upi.user.service.api.models.base.v1.response.search_management.GenericSearchResponseDTO;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;

import javax.inject.Inject;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 26/12/24
 */
@CustomLog
public class LeadPageDataSource implements PageDataSource<LeadPageDataSourceResponse>{

    @Inject
    private static ConfigUtils configUtils;
    @Inject
    private static ProfileService profileService;
    @Inject
    private static UpiUserService upiUserService;


    @Override
    public LeadPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        LeadPageDataSourceResponse leadPageDataSourceResponse = new LeadPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        leadPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        leadPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(applicationDataResponse.getExternalUserId(), applicationDataResponse.getSmUserId());
        // revisit when new profile service is live
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        try {
            profileDetailedResponse = profileService.getProfileByUserId(merchantUser.getMerchantUserId(),merchantUser.getSmUserId(), false);
        } catch (Exception exp) {
            //Logging non myntra cases as myntra will fail as of now. this will be fixed later
            if (!MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY.equals(merchantUser.getMerchantKey())) {
                log.error("Error while fetching the profile for user: {}", merchantUser, exp);
            }
        }
        if(StringUtils.isEmpty(profileDetailedResponse.getFirstName())){
            try {
                GenericSearchResponseDTO verifyVpaResponse = upiUserService.verifyVpa(GenericSearchRequestDTO.builder()
                        .genericPaymentAddress(profileDetailedResponse.getPhoneNo()).possibleTypes(Collections.singletonList(GenericSearchType.MOBILE_NUMBER)).build());
                FullName fullName = Utils.splitName(verifyVpaResponse.getPayeeDetails().getPayeeName());
                profileDetailedResponse.setFirstName(fullName.getFirstName());
                profileDetailedResponse.setLastName(fullName.getLastName());
                leadPageDataSourceResponse.setIsNameEncrypted(false);
            } catch (Exception e){
                log.error("Error in calling upi user service for sm_user_id ", merchantUser.getSmUserId(), e.getMessage(), e);
            }
        }
        leadPageDataSourceResponse.setProfile(profileDetailedResponse);
        return leadPageDataSourceResponse;
    }
}
