package com.flipkart.fintech.pinaka.service.application;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.flipkart.abservice.models.exceptions.ABInitializationException;
import com.flipkart.abservice.resources.ABService;
import com.flipkart.abservice.store.ConfigStore;
import com.flipkart.abservice.store.impl.ABApiConfigStore;
import com.flipkart.affordability.bnpl.tijori.client.StdTijoriClientConfig;
import com.flipkart.affordability.bnpl.tijori.client.TijoriClient;
import com.flipkart.affordability.bnpl.tijori.client.TijoriClientFactory;
import com.flipkart.affordability.clients.loginservice.LoginServiceClient;
import com.flipkart.affordability.clients.loginservice.LoginServiceClientImpl;
import com.flipkart.affordability.clients.oauth.OAuthClient;
import com.flipkart.affordability.clients.oauth.OAuthClientImpl;
import com.flipkart.affordability.clients.pandora.PandoraClient;
import com.flipkart.affordability.clients.pandora.PandoraClientImpl;
import com.flipkart.affordability.clients.userservice.USClient;
import com.flipkart.affordability.clients.userservice.USClientImpl;
import com.flipkart.affordability.collections.client.CollectionsClientConfiguration;
import com.flipkart.affordability.config.*;
import com.flipkart.affordability.khaata.client.KhaataClientConfig;
import com.flipkart.affordability.onboarding.client.OnboardingClientConfig;
import com.flipkart.affordability.robinhood.client.RobinHoodClient;
import com.flipkart.affordability.robinhood.client.StdRobinHoodClient;
import com.flipkart.affordability.underwriting.client.UnderwritingConfig;
import com.flipkart.fintech.ardour.client.ArdourClientConfig;
import com.flipkart.fintech.cryptex.LocalDynamicBucket;
import com.flipkart.fintech.cryptex.config.AuthNClientConfig;
import com.flipkart.fintech.kafka.config.KafkaConsumerConfiguration;
import com.flipkart.fintech.lead.service.LeadService;
import com.flipkart.fintech.lead.service.LeadServiceImpl;
import com.flipkart.fintech.lending.orchestrator.service.*;
import com.flipkart.fintech.logger.application.FintechLogConfig;
import com.flipkart.fintech.logger.application.FintechLogModule;
import com.flipkart.fintech.offer.orchestrator.cohortfinder.CohortFinder;
import com.flipkart.fintech.offer.orchestrator.cohortfinder.CohortFinderImpl;
import com.flipkart.fintech.offer.orchestrator.filters.FilterService;
import com.flipkart.fintech.offer.orchestrator.filters.FilterServiceImpl;
import com.flipkart.fintech.offer.orchestrator.module.OfferOrchestratorModule;
import com.flipkart.fintech.offer.orchestrator.offerDistributor.OfferDistributor;
import com.flipkart.fintech.offer.orchestrator.offerDistributor.OfferDistributorImpl;
import com.flipkart.fintech.offer.orchestrator.offerfetcher.OfferFetcher;
import com.flipkart.fintech.offer.orchestrator.offerfetcher.OfferFetcherImpl;
import com.flipkart.fintech.outbound.OutboundBundleConfiguration;
import com.flipkart.fintech.pandora.client.*;
import com.flipkart.fintech.pandoralite.client.PandoraLiteClientConfiguration;
import com.flipkart.fintech.pinaka.client.*;
import com.flipkart.fintech.pinaka.client.v6.PinakaClientV6;
import com.flipkart.fintech.pinaka.client.v6.PinakaClientV6Impl;
import com.flipkart.fintech.pinaka.client.v7.PinakaClientV7;
import com.flipkart.fintech.pinaka.client.v7.PinakaClientV7Impl;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClientConfig;
import com.flipkart.fintech.pinaka.common.varadhi.VaradhiClient;
import com.flipkart.fintech.pinaka.common.varadhi.VaradhiClientImpl;
import com.flipkart.fintech.pinaka.service.annotations.RateLimit;
import com.flipkart.fintech.pinaka.service.application.hawkeye.HawkeyeAsyncConfig;
import com.flipkart.fintech.pinaka.service.configuration.LenderRankingConfiguration;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.*;
import com.flipkart.fintech.pinaka.service.core.impl.*;
import com.flipkart.fintech.pinaka.service.core.v6.BulkDataRequestHandler;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.core.v6.impl.BulkDataRequestHandlerImpl;
import com.flipkart.fintech.pinaka.service.core.v7.AdaptedBorrowerServiceV7;
import com.flipkart.fintech.pinaka.service.core.v7.BorrowerServiceV7;
import com.flipkart.fintech.pinaka.service.data.BorrowerEntityDao;
import com.flipkart.fintech.pinaka.service.data.MerchantEntityDao;
import com.flipkart.fintech.pinaka.service.data.SessionEntityDao;
import com.flipkart.fintech.pinaka.service.data.impl.BorrowerEntityDaoImpl;
import com.flipkart.fintech.pinaka.service.data.impl.DisbursalsDao;
import com.flipkart.fintech.pinaka.service.data.impl.MerchantEntityEntityDaoImpl;
import com.flipkart.fintech.pinaka.service.data.impl.SessionEntityDaoImpl;
import com.flipkart.fintech.pinaka.service.datacryptography.DataEncryption;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataEncryption;
import com.flipkart.fintech.pinaka.service.external.PageServiceConfig;
import com.flipkart.fintech.pinaka.service.flags.FeatureFlag;
import com.flipkart.fintech.pinaka.service.kafka.RobinHoodEventHandler;
import com.flipkart.fintech.pinaka.service.kafka.RobinHoodEventHandlerImpl;
import com.flipkart.fintech.pinaka.service.pagedatasource.*;
import com.flipkart.fintech.pinaka.service.ruleengine.landingPageOrchestrator.ExpressionEvaluator;
import com.flipkart.fintech.pinaka.service.ruleengine.landingPageOrchestrator.PlLandingPageExpressionEvaluator;
import com.flipkart.fintech.pinaka.service.ruleengine.models.Filter;
import com.flipkart.fintech.pinaka.service.ruleengine.models.PlLandingPageStates;
import com.flipkart.fintech.pinaka.service.utils.HibernateUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.interceptors.RateLimitingInterceptor;
import com.flipkart.fintech.pinaka.service.web.SecurityResource;
import com.flipkart.fintech.pinaka.service.web.impl.SecurityResourceImpl;
import com.flipkart.fintech.pinaka.service.widgettransformer.BasicDetailsFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.CardWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.GroupedFormV4WidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage2FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage2FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LandingPageTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LenderCarouselTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4SubmitButtonWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.client.ProfileClientImpl;
import com.flipkart.fintech.profile.client.ProfileClientModule;
import com.flipkart.fintech.profile.common.AccountIdHelper;
import com.flipkart.fintech.profile.module.ProfileServiceModule;
import com.flipkart.fintech.profile.pagehandler.module.ProfileHandlerModule;
import com.flipkart.fintech.redis.configuration.RedisConfiguration;
import com.flipkart.fintech.security.gibraltar.GibraltarModule;
import com.flipkart.fintech.security.gibraltar.config.GibraltarClientConfig;
import com.flipkart.fintech.stratum.api.config.StratumD42Configuration;
import com.flipkart.fintech.userservice.client.UslClientConfig;
import com.flipkart.fintech.viesti.config.ViestiConsumerConfiguration;
import com.flipkart.fintech.viesti.config.ViestiConsumerModule;
import com.flipkart.fintech.winterfell.client.WinterfellClientConfig;
import com.flipkart.fintech.winterfell.client.WinterfellClientModule;
import com.flipkart.flux.client.config.FluxClientConfiguration;
import com.flipkart.flux.client.runtime.FluxRuntimeConnectorHttpImpl;
import com.flipkart.kloud.authn.AuthTokenService;
import com.flipkart.kloud.config.ConfigClient;
import com.flipkart.kloud.config.ConfigClientBuilder;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.kloud.config.error.ConfigServiceException;
import com.flipkart.mysql.lock.Lock;
import com.flipkart.mysql.lock.MysqlLockConfiguration;
import com.flipkart.mysql.lock.MysqlLockHibernate;
import com.flipkart.restbus.turbo.config.TurboConfig;
import com.flipkart.sa.model.SourceAttributionConfiguration;
import com.flipkart.sm.pages.module.PageServiceModule;
import com.google.inject.AbstractModule;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.matcher.Matchers;
import com.google.inject.multibindings.MapBinder;
import com.google.inject.name.Named;
import com.google.inject.name.Names;
import com.sumo.crisys.client.CrisysClientConfig;
import com.sumo.crisys.client.CrisysClientModule;
import com.supermoney.ams.bridge.models.MerchantJourneyConfig;
import io.dropwizard.hibernate.HibernateBundle;
import io.dropwizard.jackson.Jackson;
import lombok.SneakyThrows;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.glassfish.jersey.client.ClientProperties;
import org.hibernate.SessionFactory;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLFormDataConstants.BONUS_INCOME;

/**
 * Created by sujeetkumar.r on 09/08/17.
 */
public class PinakaModule extends AbstractModule {

    private final PinakaConfiguration configuration;
    private final DynamicBucket dynamicBucket;
    private final HibernateBundle<PinakaConfiguration> profileServiceBundle;

    public PinakaModule(PinakaConfiguration configuration, DynamicBucket dynamicBucket, HibernateBundle<PinakaConfiguration> profileServiceBundle) {
        this.configuration = configuration;
        this.dynamicBucket = dynamicBucket;
        this.profileServiceBundle = profileServiceBundle;
    }

    public static AmazonS3 getAmazonS3(StratumD42Configuration config) {
        AWSCredentials credentials = new BasicAWSCredentials(config.getAccessKey(), config.getSecretKey());

        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setProtocol(Protocol.HTTPS);
        clientConfiguration.setSignerOverride("AWSS3V4SignerType");
        clientConfiguration.setMaxConnections(config.getMaxConnections());
        clientConfiguration.setConnectionTimeout(config.getConnectionTimeout());

        AmazonS3 amazonS3 = new AmazonS3Client(credentials, clientConfiguration);
        amazonS3.setEndpoint(config.getEndPoint());
        amazonS3.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
        return amazonS3;
    }

    @SneakyThrows
    @Override
    protected void configure() {
        requestStaticInjection(BasicDetailsPageDataSource.class);
        requestStaticInjection(AddressDetailsPageDataSource.class);
        requestStaticInjection(WorkDetailsPageDataSource.class);
        requestStaticInjection(OfferScreenDataSource.class);
        requestStaticInjection(AadharFormPageDataSource.class);
        requestStaticInjection(KycDetailsPageDataSource.class);
        requestStaticInjection(LiveSelfiePageDataSource.class);
        requestStaticInjection(KFSOtpPageDataSource.class);
        requestStaticInjection(KFSDetailsPageDataSource.class);
        requestStaticInjection(OtpVerificationPageDataSource.class);
        requestStaticInjection(BankDetailsPageDataSource.class);
        requestStaticInjection(RepaymentModesDataSource.class);
        requestStaticInjection(ApplicationStatusPageDataSource.class);
        requestStaticInjection(ETBGenerateOtp.class);
        requestStaticInjection(ETBVerifyOtp.class);
        requestStaticInjection(InitialUserReviewDataSource.class);
        requestStaticInjection(LeadPageDataSource.class);


        bind(BorrowerService.class).to(BorrowerServiceImpl.class);
        bind(BorrowerServiceV7.class).to(AdaptedBorrowerServiceV7.class);
        bind(LeadService.class).to(LeadServiceImpl.class);
        bind(USClient.class).to(USClientImpl.class);
        bind(UserClient.class).to(FkUserClient.class);
        bind(LoginServiceClient.class).to(LoginServiceClientImpl.class);
        bind(OAuthClient.class).to(OAuthClientImpl.class);
        //        bind(BorrowerService.class).to(BorrowerServiceImpl.class);
        bind(BorrowerEntityDao.class).to(BorrowerEntityDaoImpl.class);
        //TODO : Figure out why ?
        bindInterceptor(Matchers.any(), Matchers.annotatedWith(RateLimit.class), new RateLimitingInterceptor(configuration.getRateLimitingConfig(), providesDynamicBucket()));

        bind(MerchantService.class).to(MerchantServiceImpl.class);
        bind(MerchantEntityDao.class).to(MerchantEntityEntityDaoImpl.class);
        bind(SessionEntityDao.class).to(SessionEntityDaoImpl.class);
        bind(SessionService.class).to(SessionServiceImpl.class);
        bind(LmsService.class).to(LmsServiceImpl.class);

        bind(PlOnboardingClient.class).to(PlOnboardingClientImpl.class);
        bind(OfferFetcher.class).to(OfferFetcherImpl.class);
        bind(CohortFinder.class).to(CohortFinderImpl.class);
        bind(OfferDistributor.class).to(OfferDistributorImpl.class);
        bind(FilterService.class).to(FilterServiceImpl.class);
        bind(BulkDataRequestHandler.class).to(BulkDataRequestHandlerImpl.class);
        bind(ProfileClient.class).to(ProfileClientImpl.class);
        bind(DataEncryption.class).to(FormDataEncryption.class);

        // SM changes
        this.bind(PandoraClient.class).to(PandoraClientImpl.class).in(Singleton.class);
        bind(PinakaClientV6.class).to(PinakaClientV6Impl.class);
        bindRejectScreens();
        bindSuccessScreens();
        bindLenderCustomScreens();
        bindFormFieldEnum();
        bindFirstInitialUserDataFieldForm();
        bindSecondInitialUserDataFieldForm();



        install(new PinakaClientModule());
        install(new GibraltarModule());
        install(new ViestiConsumerModule());
        install(new WinterfellClientModule(configuration.getWinterfellClientConfig()));

        install(new ProfileServiceModule(configuration.getUserServiceClientConfig(),
                configuration.getLoginServiceClientConfig(), configuration.getMultiTenantOAuthClientConfig(), provideClient(),
                profileServiceBundle.getSessionFactory(), configuration.getSmUserServiceClientConfig(), configuration.getProfileServiceConfig(),
                PinakaMetricRegistry.getMetricRegistry(), configuration.getBigtableConfig(), configuration.getPublisherConfigforCs()));
        install(new OfferOrchestratorModule(HibernateUtils.getInstance().getSessionFactory()));
        install(new ProfileHandlerModule(configuration.getProfileClientConfiguration()));
        install(new ProfileClientModule());
        install(new PageServiceModule(configuration.getPageServiceConfig(), configuration.getDatabaseConfig(), PinakaMetricRegistry.getMetricRegistry()));

        //todo: update this
        //        install(new StratumClientModule(null, configuration.getStratumD42Configuration()));
        //        install(new UslClientModule(providesUslClientConfig()));
        //        install(new PandoraLiteClientModule());
        install(new FintechLogModule(providesFintechLogConfig()));
        install(new CrisysClientModule(configuration.getCrisysClientConfig()));

        bind(SecurityService.class).to(SecurityServiceImpl.class);

        bind(SecurityResource.class).to(SecurityResourceImpl.class);

        // V3 bindings

        bind(OfferService.class).to(BasicOfferOrchestrator.class);
        bind(LendingOrchestrator.class).to(PersonalLoanOrchestrator.class);
        bind(ReadRepairDataService.class).to(ReadRepairDataServiceImpl.class);
        bind(RobinHoodEventHandler.class).to(RobinHoodEventHandlerImpl.class);
        bind(PinakaClientV7.class).to(PinakaClientV7Impl.class);
        bind(VaradhiClient.class).to(VaradhiClientImpl.class);


        install(new HystrixModule(configuration.getHystrixModuleConfiguration().getResourcesFilePath() + configuration.getHystrixModuleConfiguration().getHystrixPropertiesFileName()));

        AccountIdHelper.init(dynamicBucket);
    }

    private void bindRejectScreens() {
        MapBinder<String, String> rejectScreenBinder = MapBinder.newMapBinder(binder(), String.class, String.class,
            Names.named("merchantRejectScreens"));
        rejectScreenBinder.addBinding("MYNTRA").toInstance(provideMyntraRejectScreen());
    }

    private void bindSuccessScreens() {
        MapBinder<String, String> successScreenBinder = MapBinder.newMapBinder(binder(), String.class, String.class,
                Names.named("lenderSuccessScreens"));
        successScreenBinder.addBinding("HDFC").toInstance(provideHdfcSuccessScreen());
        successScreenBinder.addBinding("MPOCKKET").toInstance(provideMpockketSuccessScreen());
    }
    private void bindLenderCustomScreens() {
        MapBinder<String, Map> customScreensBinder = MapBinder.newMapBinder(binder(), String.class, Map.class,
                Names.named("lenderCustomScreens"));
        customScreensBinder.addBinding("MPOCKKET").toInstance(provideMpockketCustomScreens());
    }


    private void bindFormFieldEnum() {
        MapBinder<String, BasicDetailsFormTransformer.FormFields> panPageScreenBinder = MapBinder.newMapBinder(binder(), String.class, BasicDetailsFormTransformer.FormFields.class,
                Names.named("panPageScreen"));
        panPageScreenBinder.addBinding("firstName").toInstance(BasicDetailsFormTransformer.FormFields.FIRST_NAME);
        panPageScreenBinder.addBinding("lastName").toInstance(BasicDetailsFormTransformer.FormFields.LAST_NAME);
        panPageScreenBinder.addBinding("dob").toInstance(BasicDetailsFormTransformer.FormFields.DOB);
        panPageScreenBinder.addBinding("pan").toInstance(BasicDetailsFormTransformer.FormFields.PAN);
        panPageScreenBinder.addBinding("employmentType").toInstance(BasicDetailsFormTransformer.FormFields.EMPLOYMENT_TYPE);
        panPageScreenBinder.addBinding("gender").toInstance(BasicDetailsFormTransformer.FormFields.GENDER);
        panPageScreenBinder.addBinding("pincode").toInstance(BasicDetailsFormTransformer.FormFields.PINCODE);
        panPageScreenBinder.addBinding("email").toInstance(BasicDetailsFormTransformer.FormFields.EMAIL);

        MapBinder<String, NamePageFormTransformer.FormFields> namePageScreenBinder = MapBinder.newMapBinder(binder(), String.class, NamePageFormTransformer.FormFields.class,
                Names.named("plLeadNamePageScreen"));
        namePageScreenBinder.addBinding("firstName").toInstance(NamePageFormTransformer.FormFields.FIRST_NAME);
        namePageScreenBinder.addBinding("lastName").toInstance(NamePageFormTransformer.FormFields.LAST_NAME);
        namePageScreenBinder.addBinding("phoneNumber").toInstance(NamePageFormTransformer.FormFields.PHONE_NUMBER);
    }

    private void bindFirstInitialUserDataFieldForm() {
        MapBinder<String, ReviewPage1FormTransformer.FormFields> dataPageScreenBinder = MapBinder.newMapBinder(binder(), String.class, ReviewPage1FormTransformer.FormFields.class,
                Names.named("dataPageScreen"));
        dataPageScreenBinder.addBinding("panNumber").toInstance(ReviewPage1FormTransformer.FormFields.PAN_NUMBER);
        dataPageScreenBinder.addBinding("dob").toInstance(ReviewPage1FormTransformer.FormFields.DOB);
        dataPageScreenBinder.addBinding("email").toInstance(ReviewPage1FormTransformer.FormFields.EMAIL);
        dataPageScreenBinder.addBinding("houseNumber").toInstance(ReviewPage1FormTransformer.FormFields.HOUSE_NUMBER);
        dataPageScreenBinder.addBinding("area").toInstance(ReviewPage1FormTransformer.FormFields.AREA);
        dataPageScreenBinder.addBinding("pincodeDetails").toInstance(ReviewPage1FormTransformer.FormFields.PINCODE);
        dataPageScreenBinder.addBinding("gender").toInstance(ReviewPage1FormTransformer.FormFields.GENDER);
        dataPageScreenBinder.addBinding("employmentType").toInstance(ReviewPage1FormTransformer.FormFields.EMPLOYMENT_TYPE);
    }

    private void bindSecondInitialUserDataFieldForm() {
        MapBinder<String, ReviewPage2FormTransformer.FormField> page2ScreenBinder = MapBinder.newMapBinder(binder(), String.class, ReviewPage2FormTransformer.FormField.class,
                Names.named("page2Screen"));
        page2ScreenBinder.addBinding("organization").toInstance(ReviewPage2FormTransformer.FormField.COMPANY_NAME);
        page2ScreenBinder.addBinding("income").toInstance(ReviewPage2FormTransformer.FormField.MONTHLY_INCOME);
        page2ScreenBinder.addBinding(BONUS_INCOME).toInstance(ReviewPage2FormTransformer.FormField.BONUS_OR_OTHER_INCOME);
        page2ScreenBinder.addBinding("incomeSource").toInstance(ReviewPage2FormTransformer.FormField.INCOME_SOURCE);
        page2ScreenBinder.addBinding("industryName").toInstance(ReviewPage2FormTransformer.FormField.INDUSTRY_TYPE);
        page2ScreenBinder.addBinding("annualTurnOver").toInstance(ReviewPage2FormTransformer.FormField.ANNUAL_TURNOVER);
    }

    @Provides
    @Named("sandboxOfferScreen")
    @Singleton
    public String provideSandboxOfferScreen() {
        return TransformerUtils.readFileasString("template/sandbox/v2/OfferDetails.json");
    }

    @Provides
    @Named("offerDetailsApprovedOffer")
    @Singleton
    public String provideApprovedOffer() {
        return TransformerUtils.readFileasString("template/landingPages/OfferDetailsApprovedOffer.json");
    }

    @Provides
    @Singleton
    public FormWidgetDataPrefillUtils groupedWidgetDataPrefillUtils() {
        return new FormWidgetDataPrefillUtils();
    }

    @Provides
    @Singleton
    public FormWidgetDataJsonParser getGroupedWidgetDataJsonParser() {
        return new FormWidgetDataJsonParser();
    }

    @Provides
    @Singleton
    FormWidgetDataFetcher getGroupedWidgetDataFetcher() {
        return new FormWidgetDataFetcher();
    }

    @Provides
    @Named("anotherLenderAnnouncementCard")
    @Singleton
    public String provideAnotherLenderAnnouncementCard() {
        return TransformerUtils.readFileasString("template/landingPages/OfferDetailsAnnouncementCard.json");
    }

    @Provides
    @Named("plLandingPageFilterRules")
    @Singleton
    public LinkedHashMap<PlLandingPageStates, List<Filter>> providePlLandingPageFilterRules() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(TransformerUtils.readFileasString("filterRules/landingPageFilterRules.json"),
                new TypeReference<LinkedHashMap<PlLandingPageStates, List<Filter>>>(){});
    }

    @Provides
    @Named("plLandingPageExpressionEvaluator")
    @Singleton
    public ExpressionEvaluator providePlLandingPageExpressionEvaluator(){
        return new PlLandingPageExpressionEvaluator();
    }



    @Provides
    public SessionFactory getSessionFactory() { return HibernateUtils.getInstance().getSessionFactory(); }

    @Provides
    @Named("db_slave")
    public SessionFactory provideSlaveSessionFactory() {
        return HibernateUtils.getInstance().getSlaveSessionFactory();
    }

    @Provides
    @Singleton
    public PinakaClientConfig providePinakaClientConfig() {
        return configuration.getPinakaClientConfig();
    }

    @Provides
    @Singleton
    public PinakaCalvinClientConfig providePinakaCalvinClientConfig() {
        return configuration.getPinakaCalvinClientConfig();
    }

    @Provides
    @Singleton
    public ArdourClientConfig provideArdourClientConfig() {
        return configuration.getArdourClientConfig();
    }

    @Provides
    @Singleton
    public Client provideClient() {
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);
        return client;
    }

    @Provides
    @Singleton
    public GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer(
            LV3NamePageFormTransformer lv3NamePageFormTransformer,
            LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer,
            LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer,
            LV4LandingPageTransformer LV4LandingPageTransformer) {
        return new GroupedFormV4WidgetTransformer(lv3NamePageFormTransformer, lv3ReviewPage1FormTransformer, lv3ReviewPage2FormTransformer);
    }

    @Provides
    @Singleton
    public LV3NamePageFormTransformer lv3NamePageFormTransformer(
            DynamicBucket dynamicBucket,
            Decrypter decrypter,
            FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
            FormWidgetDataFetcher formWidgetDataFetcher,
            FormWidgetDataJsonParser formWidgetDataJsonParser,
            LocationRequestHandler locationRequestHandler,
            BqIngestionHelper bqIngestionHelper) {
        return new LV3NamePageFormTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataPrefillUtils,
                formWidgetDataFetcher,
                formWidgetDataJsonParser,
                locationRequestHandler,
                bqIngestionHelper
        );
    }

    @Provides
    @Singleton
    public LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer(
            DynamicBucket dynamicBucket,
            Decrypter decrypter,
            FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
            FormWidgetDataFetcher formWidgetDataFetcher,
            FormWidgetDataJsonParser formWidgetDataJsonParser,
            LocationRequestHandler locationRequestHandler,
            BqIngestionHelper bqIngestionHelper) {
        return new LV3ReviewPage1FormTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataPrefillUtils,
                formWidgetDataFetcher,
                formWidgetDataJsonParser,
                locationRequestHandler,
                bqIngestionHelper
        );
    }

    @Provides
    @Singleton
    public LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer(
            DynamicBucket dynamicBucket,
            Decrypter decrypter,
            FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
            FormWidgetDataFetcher formWidgetDataFetcher,
            FormWidgetDataJsonParser formWidgetDataJsonParser,
            LocationRequestHandler locationRequestHandler,
            BqIngestionHelper bqIngestionHelper) {
        return new LV3ReviewPage2FormTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataPrefillUtils,
                formWidgetDataFetcher,
                formWidgetDataJsonParser,
                locationRequestHandler,
                bqIngestionHelper
        );
    }

    @Provides
    @Singleton
    public LV4LandingPageTransformer leadV4LandingPageTransformer(
            Decrypter decrypter,
            DynamicBucket dynamicBucket,
            FormWidgetDataFetcher formWidgetDataFetcher,
            LocationRequestHandler locationRequestHandler,
            BqIngestionHelper bqIngestionHelper) {
        return new LV4LandingPageTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataFetcher,
                locationRequestHandler,
                bqIngestionHelper
        );
    }

    @Provides
    @Singleton
    public CardWidgetTransformer cardWidgetTransformer(LV4LandingPageTransformer lv4LandingPageTransformer, LV4SubmitButtonWidgetTransformer lv4SubmitButtonWidgetTransformer, LV4LenderCarouselTransformer lv4LenderCarouselTransformer) {
        return new CardWidgetTransformer(lv4LandingPageTransformer, lv4SubmitButtonWidgetTransformer, lv4LenderCarouselTransformer);
    }

    @Provides
    @Singleton
    @Named("elasticsearchClient")
    public EsClientConfig providesEsClientConfig() {
        return configuration.getEsClientConfig();
    }

    @Provides
    @Singleton
    @Named("elasticsearchClient")
    public HttpHost provideHttpHost() {
        return new HttpHost(providesEsClientConfig().getHostName(), providesEsClientConfig().getPort());
    }

    @Provides
    @Singleton
    @Named("elasticsearchClient")
    public RestHighLevelClient provideRestHighLevelClient() {
        return new RestHighLevelClient(RestClient.builder(provideHttpHost()).setRequestConfigCallback(
                requestConfigBuilder -> requestConfigBuilder.setConnectTimeout(providesEsClientConfig().getConnectionTimeout())));
    }

    @Provides
    @Singleton
    public ObjectMapper providesObjectMapper() {
        ObjectMapper objectMapper = Jackson.newObjectMapper();
        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+530"));
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.registerModule(new JavaTimeModule());
        return objectMapper;
    }

    @Provides
    @Singleton
    public UserServiceClientConfig provideUserServiceClientConfig() {
        return configuration.getUserServiceClientConfig();
    }

    @Provides
    @Singleton
    public OutboundBundleConfiguration providesOutboundBundleConfiguration() {
        return configuration.getOutboundBundleConfiguration();
    }

    @Provides
    @Singleton
    public OAuthServiceClientConfig provideOAuthServiceClientConfig() {
        return configuration.getOAuthServiceClientConfig();
    }

    @Provides
    @Singleton
    public LoginServiceClientConfig provideLoginServiceClientConfig() {
        return configuration.getLoginServiceClientConfig();
    }

    @Provides
    @Singleton
    public FluxClientConfig provideFluxAsyncClientConfig() {
        return configuration.getFluxAsyncClientConfig();
    }

    @Provides
    @Singleton
    public ConnektClientConfig provideConnektClientConfig() {
        return configuration.getConnektClientConfig();
    }

    @Provides
    @Singleton
    private RobinhoodAsyncClientConfig providesRobinhoodAsyncClientConfig() {
        return configuration.getRobinhoodAsyncClientConfig();
    }

    @Provides
    @Singleton
    private PandoraClientConfiguration providePandoraClientConfig() {
        return configuration.getPandoraClientConfiguration();
    }

    @Provides
    @Singleton
    private VaradhiClentConfig provideVaradhiClentConfig() {
        return configuration.getVaradhiClentConfig();
    }

    @Provides
    @Singleton
    private PandoraLiteClientConfiguration providePandoraLiteClientConfig() {
        return configuration.getPandoraLiteClientConfiguration();
    }

    @Provides
    @Singleton
    public FluxClientConfiguration provideFluxClientConfiguration() {
        return configuration.getFluxClientConfig();
    }

    @Provides
    @Singleton
    public OnboardingClientConfig provideOnboardingClientConfiguration() {
        return configuration.getOnboardingClientConfig();
    }

    @Provides
    @Singleton
    public FluxRuntimeConnectorHttpImpl provideFluxRuntimeConnector() {
        FluxClientConfiguration fluxClientConfiguration = configuration.getFluxClientConfig();
        return new FluxRuntimeConnectorHttpImpl(fluxClientConfiguration.getConnectionTimeout(),
                fluxClientConfiguration.getSocketTimeout(),
                fluxClientConfiguration.getFluxRuntimeUrl() + "/api/machines");
    }

    @Provides
    @Singleton
    public CryptoConfiguration provideCryptoConfig() {
        return configuration.getCryptoConfiguration();
    }

    @Provides
    @Singleton
    public UnderwritingConfig provideUnderwritingConfiguration() {
        return configuration.getUnderwritingConfig();
    }

    @Provides
    @Singleton
    @Named("gcp_pinaka")
    public PinakaClientV7 providesPinakaClientConfig() {
        return new PinakaClientV7Impl(configuration.getGcpPinakaClientConfig(), provideClient());
    }

    @Provides
    @Singleton
    public LenderConfiguration provideLenderConfiguration() {
        return configuration.getLenderConfiguration();
    }

    @Provides
    @Singleton
    public TijoriClient provideTijoriClient() {
        TijoriConfiguration tijoriServiceConfig = configuration.getTijoriConfiguration();
        StdTijoriClientConfig stdTijoriClientConfig = new StdTijoriClientConfig();
        stdTijoriClientConfig.setClientId(tijoriServiceConfig.getClientName());
        stdTijoriClientConfig.setEndpoint(tijoriServiceConfig.getUrl());
        return TijoriClientFactory.getStandardClient(stdTijoriClientConfig);
    }

    @Provides
    @Singleton
    public PageServiceConfig providesPageServiceConfig() {
        return configuration.getExternalClientConfig().getPageServiceConfig();
    }

    @Provides
    @Singleton
    public LenderRankingConfiguration getLenderRankingConfiguration() {
        return configuration.getLenderRankingConfiguration();
    }

    @Provides
    @Singleton
    public CollectionsClientConfiguration providesCollectionsClientConfiguration() {
        return configuration.getCollectionsClientConfiguration();
    }

    @Provides
    @Singleton
    public ContextWiseWhitelistConfiguration providesJourneyClientConfiguration() {
        return configuration.getContextWiseWhitelistConfiguration();
    }

    @Provides
    @Singleton
    public com.flipkart.sm.pages.config.PageServiceConfig providesInternalPageServiceConfig() {
        configuration.getPageServiceConfig().setDatabaseConfig(configuration.getDatabaseConfig());
        return configuration.getPageServiceConfig();
    }

    @Provides
    @Singleton
    public MysqlLockConfiguration provideMySqlLockConfiguration() {
        return configuration.getMysqlLockConfiguration();
    }

    @Provides
    @Singleton
    public ABConfiguration provideAbConfiguration() {
        return configuration.getAbConfiguration();
    }

    @Provides
    @Singleton
    public OnboardingConfiguration provideAdvanzOnboardingConfiguration() {
        return configuration.getOnboardingConfiguration();
    }

    @Provides
    @Singleton
    public UiConfiguration uiConfiguration() {
        return configuration.getUiConfiguration();
    }

    @Provides
    @Singleton
    public DataPointConfiguration getDataPointConfiguration() {
        return configuration.getDataPointConfiguration();
    }

    @Provides
    @Singleton
    public ABService provideABService() throws ABInitializationException {
        if (configuration.getAbConfiguration().isUnlayeredAbEnabled()) {
            ConfigStore configStore = ABApiConfigStore.initialize(
                    PinakaConstants.TENANT_FLIPKART,
                    configuration.getAbConfiguration().getEndpoint(),
                    configuration.getAbConfiguration().getClientSecretKey()
            );
            return ABService.initialize(configStore, configuration.getAbConfiguration().isUnlayeredAbEnabled());
        } else {

            ABApiConfigStore configStore =
                    ABApiConfigStore.initializeForLayers(
                            PinakaConstants.TENANT_FLIPKART,
                            configuration.getAbConfiguration().getEndpoint(),
                            Arrays.asList(configuration.getAbConfiguration().getLayerList().split(",")),
                            configuration.getAbConfiguration().getClientSecretKey()
                    );
            return ABService.initialize(configStore);
        }
    }

    @Provides
    @Named("extOfferServiceExecutor")
    @Singleton
    public ExecutorService provideMultiThreadedExecutor() {
        return Executors.newFixedThreadPool(15);
    }

    @Provides
    @Named("robinhoodExecutorService")
    @Singleton
    public ExecutorService provideMultiThreadedExecutorForCs() {
        return Executors.newFixedThreadPool(15);
    }

    @Provides
    @Named("experianRawReportExecutor")
    @Singleton
    public ExecutorService provideMultiThreadedExecutorforExperian() {
        return Executors.newFixedThreadPool(5);
    }

    @Provides
    @Named("provideClientForCsVaradhiEvent")
    public Client getClient() {
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);
        return client;
    }


    @Provides
    @Named("offerServiceTarget")
    @Singleton
    public WebTarget providesOfferServiceTarget() {
        return ClientBuilder.newClient().target(configuration.getOfferServiceConfig().getUrl());
    }

    @Provides
    @Singleton
    public AlfredClientConfig providesAlfredClientConfig() {
        return configuration.getAlfredClientConfig();
    }

    @Provides
    @Singleton
    public FkPayClientConfig providesFkPayClientConfig() {
        return configuration.getFkPayClientConfig();
    }

    @Provides
    @Singleton
    public NeoCrmClientConfig providesNeoCRMclientConfig() {
        return configuration.getNeoCrmClientConfig();
    }

    @Provides
    @Singleton
    public RedisConfiguration providesRedisConfig() {
        return configuration.getRedisConfiguration();
    }

    @Provides
    @Singleton
    public Map<String, MerchantJourneyConfig> provideMerchantJourneyUrlConfig() {
        return configuration.getMerchantJourneyUrlConfig();
    }

    @Provides
    public RobinHoodClient provideRobinhoodClient(RobinhoodAsyncClientConfig robinhoodAsyncClientConfig) {
        return new StdRobinHoodClient(robinhoodAsyncClientConfig.getUrl());
    }

    @Provides
    @Singleton
    public Lock providesMysqlLock(Provider<MysqlLockConfiguration> mysqlLockConfigurationProvider,
                                  Provider<SessionFactory> sessionFactoryProvider) {
        return new MysqlLockHibernate(sessionFactoryProvider.get(), mysqlLockConfigurationProvider.get());
    }

    @Provides
    @Singleton
    public PinakaConfiguration providesPinakaConfiguation() {
        return configuration;
    }

    @Provides
    @Singleton
    public SourceAttributionConfiguration providesSourceAttributionConfig() {
        return configuration.getSourceAttributionConfiguration();
    }


    @Provides
    @Singleton
    public SkylerClientConfig skylerClientConfig() {
        return configuration.getSkylerClientConfig();
    }

    @Provides
    @Singleton
    public KhaataClientConfig khaataClientConfig() {
        return configuration.getKhaataClientConfig();
    }

    @Provides
    @Singleton
    public CoinManagerClientConfig coinManagerClientConfig() {
        return configuration.getCoinManagerClientConfig();
    }

    @Provides
    @Singleton
    public WinterfellClientConfig providesWinterfellClientConfig() {
        return configuration.getWinterfellClientConfig();
    }

    @Provides
    @Singleton
    public HawkeyeAsyncConfig getHawkeyeAsyncConfig() {
        return configuration.getHawkeyeAsyncConfig();
    }

    @Provides
    @Singleton
    public LockinClientConfig providesLockinClientConfig() {
        return configuration.getLockinClientConfig();
    }

    @Provides
    @Singleton
    public KafkaConsumerConfiguration providesKafkaConsumerConfiguration() {
        return configuration.getKafkaConsumerConfiguration();
    }

    @Provides
    @Singleton
    public DynamicBucket providesDynamicBucket() throws ConfigServiceException, Exception {
        if (dynamicBucket == null) {
            if (configuration.getCryptexConfiguration().getDynamicBucketConfig().isEnableLocalDynamicBucket()) {
                return new LocalDynamicBucket(configuration.getCryptexConfiguration().getDynamicBucketConfig().getBucketName());
            }
            ConfigClient configClient = new ConfigClientBuilder().build();
            return configClient.getDynamicBucket(configuration.getCryptexConfiguration().getDynamicBucketConfig().getBucketName());
        } else {
            return dynamicBucket;
        }
    }

    @Provides
    @Singleton
    @Named("losFeatureFlag")
    public FeatureFlag provideLosFeatureFlag() {
        try {
            Boolean losActive = dynamicBucket.getBoolean("los.active");
            Integer losActivePercentage = dynamicBucket.getInt("los.active.percentage");
            List<String> userIds = dynamicBucket.getStringArray("los.enabled.users");
            return new FeatureFlag(losActive, losActivePercentage, userIds);
        } catch (Exception e) {
            throw new RuntimeException("Exception while setting up los feature flag: " + e.getMessage());
        }
    }

    @Provides
    @Singleton
    @Named("offerOrchestratorFlag")
    public FeatureFlag provideOfferOrchestratorFeatureFlag() {
        try {
            Boolean offerOrchestratorActive = dynamicBucket.getBoolean("oo.active");
            Integer offerOrchestratorActivePercentage = dynamicBucket.getInt("oo.active.percentage");
            List<String> userIds = dynamicBucket.getStringArray("oo.enabled.users");
            return new FeatureFlag(offerOrchestratorActive, offerOrchestratorActivePercentage, userIds);
        } catch (Exception e) {
            throw new RuntimeException("Exception while setting up offer orchestrator feature flag: " + e.getMessage());
        }
    }

    @Provides
    @Singleton
    public PandoraAsyncClientConfiguration providesPandoraAsyncClientConfiguration() {
        return configuration.getPandoraAsyncClientConfiguration();
    }

    @Provides
    @Singleton
    public TijoriAsyncClientConfig providesTijoriAsyncClientConfig() {
        return configuration.getTijoriAsyncClientConfig();
    }

    @Provides
    @Singleton
    public PinakaAsyncClientConfig providesPinakaAsyncClientConfig() {
        return configuration.getPinakaAsyncClientConfig();
    }

    @Provides
    @Singleton
    public AuthTokenService providesAuthTokenService() {
        AuthTokenService authTokenService = AuthTokenService.getInstance();
        if (!configuration.getCryptexConfiguration().isCryptexBundleEnabled()) {
            AuthNClientConfig authNClientConfig = configuration.getCryptexConfiguration().getAuthNClientConfig();
            AuthTokenService.init(authNClientConfig.getUrl(), authNClientConfig.getClientId(),
                    authNClientConfig.getClientSecret());
            return AuthTokenService.getInstance();
        }
        return authTokenService;
    }

    @Provides
    @Singleton
    public FintechUserServiceClientConfig fintechUserServiceClientConfig() {
        return configuration.getFintechUserServiceClientConfig();
    }

    @Provides
    @Singleton
    public UslClientConfig providesUslClientConfig() {
        return configuration.getUslClientConfig();
    }

    @Provides
    @Singleton
    public HystrixModuleConfiguration providesHystrixModuleConfiguration() {
        return configuration.getHystrixModuleConfiguration();
    }

    @Provides
    @Singleton
    public MultiTenantOAuthClientConfig providesMultiTenantOAuthClientConfig() {
        return configuration.getMultiTenantOAuthClientConfig();
    }

    @Provides
    @Singleton
    public MultiTenantConnektClientConfig providesMultiTenantConnektClientConfig() {
        return configuration.getMultiTenantConnektClientConfig();
    }

    @Provides
    @Singleton
    public DexterClientConfig providesDexterClientConfig() {
        return configuration.getDexterClientConfig();
    }

    @Provides
    @Singleton
    private TurboConfig providesTurboConfig() {
        return configuration.getTurboConfig();
    }

    @Provides
    @Singleton
    public CoreLogisticsClientConfig providesCoreLogisticsClientConfig() {
        return configuration.getCoreLogisticsClientConfig();
    }

    @Provides
    @Singleton
    public GibraltarClientConfig provideGibraltarClientConfig() {
        return configuration.getGibraltarClientConfig();
    }

    @Provides
    @Singleton
    public CriClientConfig providesCriClientUrl() {
        return configuration.getCriClientConfig();
    }

    @Provides
    @Singleton
    public ConsentConfig providesConsentConfig() {
        return configuration.getConsentConfig();
    }

    @Provides
    @Singleton
    public ViestiConsumerConfiguration providesViestiConfig() {
        return configuration.getViestiConfig();
    }

    @Provides
    @Singleton
    public FintechLogConfig providesFintechLogConfig() {
        return configuration.getFintechLogConfig();
    }

    @Provides
    @Singleton
    public SecurityKeyConfiguration providesSecurityKeyConfiguration() {
        return configuration.getSecurityKeyConfiguration();
    }

    @Provides
    @Singleton
    public AmazonS3 providesAmazonS3() {
        StratumD42Configuration config = configuration.getStratumD42Configuration();
        return getAmazonS3(config);
    }

    @Provides
    @Singleton
    public CrisysClientConfig provideCrisysClientConfig() {
        return configuration.getCrisysClientConfig();
    }

    @Provides
    @Singleton
    @Named("mandateWaitingScreen")
    public String provideMandateWaitingScreen() {
        return TransformerUtils.readFileasString("template/idfc/EMandateWaitingScreen.json");
    }


    @Provides
    @Singleton
    @Named("genericRejectScreen")
    public String provideGenericRejectScreen() {
        return TransformerUtils.readFileasString("template/idfc/OfferRejectionError.json");
    }

    @Provides
    @Singleton
    @Named("myntraRejectScreen")
    public String provideMyntraRejectScreen() {
        return TransformerUtils.readFileasString("template/common/OfferRejectionErrorMyntra.json");
    }

    @Provides
    @Singleton
    @Named("hdfcSuccessScreen")
    public String provideHdfcSuccessScreen() {
        return TransformerUtils.readFileasString("template/idfc/HDFCSuccess.json");
    }
    @Provides
    @Singleton
    @Named("mpockketSuccessScreen")
    public String provideMpockketSuccessScreen() {
        return TransformerUtils.readFileasString("template/mpockket/MPOCKKETSuccess.json");
    }

    @Provides
    @Singleton
    @Named("mpockketCustomScreens")
    public Map<String,String> provideMpockketCustomScreens() {
        Map<String,String> mpockketCustomScreenMap = new HashMap<>();
        mpockketCustomScreenMap.put("IN_PROGRESS",TransformerUtils.readFileasString("template/mpockket/MPockketProgress.json"));
        mpockketCustomScreenMap.put("USER_CONFIRMATION",TransformerUtils.readFileasString("template/mpockket/MPockketUserConfirmation.json"));
        return mpockketCustomScreenMap;
    }

    @Provides
    @Singleton
    @Named("creditScoreButton")
    public String provideCreditScoreButton() {
        return TransformerUtils.readFileasString("template/sandbox/CheckCreditScoreButton.json");
    }

    @Provides
    @Singleton
    @Named("tryAnotherLenderButton")
    public String provideTryAnotherLenderButton() {
        return TransformerUtils.readFileasString("template/common/TryWithAnotherLenderButtons.json");
    }


    @Provides
    @Singleton
    @Named("tryAnotherLenderAnnouncement")
    public String provideTryAnotherLenderAnnouncement() {
        return TransformerUtils.readFileasString("template/common/TryWithAnotherLenderAnnouncement.json");
    }


    @Provides
    @Singleton
    @Named("noLenderAssignedAnnouncement")
    public String provideNoLenderAssignedAnnouncement() {
        return TransformerUtils.readFileasString("template/common/NoLenderAssignedAnnouncement.json");
    }

    @Provides
    @Singleton
    public DisbursalsDao provideDisbursalDaoInstance(){
        return new DisbursalsDao(getSessionFactory());
    }
}
