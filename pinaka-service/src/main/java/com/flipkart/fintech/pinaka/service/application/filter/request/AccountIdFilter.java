package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.HeaderConstants;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.MultivaluedMap;
import java.io.IOException;
import java.util.Objects;
import org.glassfish.jersey.server.internal.routing.UriRoutingContext;
import org.glassfish.jersey.server.model.ResourceMethodInvoker;

@CustomLog
public class AccountIdFilter implements ContainerRequestFilter {
    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {
        if (Objects.isNull(RequestContextThreadLocal.REQUEST_CONTEXT.get())) {
            RequestContextThreadLocal.setRequestContext(new RequestContext(false));
        }

        RequestContext requestContext = RequestContextThreadLocal.REQUEST_CONTEXT.get();
        MultivaluedMap<String, String> headers = containerRequestContext.getHeaders();
        String merchantUserId = headers.getFirst(HeaderConstants.X_MERCHANT_USER_ID);
        String accountId = headers.getFirst(HeaderConstants.X_ACCOUNT_ID);
        String smUserId = headers.getFirst(HeaderConstants.X_SM_USER_ID);

        if (StringUtils.isEmpty(merchantUserId)) {
            log.info("merchantUserId is coming empty for the API: {}", containerRequestContext.getUriInfo().getPath());
            markMissingMerchantAccountIdIdMetric(containerRequestContext.getUriInfo().getPath());
            if (StringUtils.isNotEmpty(accountId)) {
                log.info("Using account id for merchant user id as merchant user is coming as null for API: {}",
                        containerRequestContext.getUriInfo().getPath());
                requestContext.setHeader(HeaderConstants.X_MERCHANT_USER_ID, accountId);
            }
        } else {
            requestContext.setHeader(HeaderConstants.X_MERCHANT_USER_ID, merchantUserId);
        }

        if (StringUtils.isEmpty(smUserId)) {
            log.info("smUserId is coming empty for the API: {}", containerRequestContext.getUriInfo().getPath());
            try{
                markMissingSMAccountIdIdMetric(
                    ((ResourceMethodInvoker) ((UriRoutingContext) containerRequestContext.getUriInfo()).getEndpoint()).getResourceMethod()
                        .getName());
            } catch (Exception e){
                log.error("can't emit metric: {}",e.getMessage());
            }
        } else {
            requestContext.setHeader(HeaderConstants.X_SM_USER_ID, smUserId);
        }
    }

    private void markMissingMerchantAccountIdIdMetric(String api) {
        PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), "missing.merchant.account.id")).mark();
    }

    private void markMissingSMAccountIdIdMetric(String api) {
        PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), api,"missing.sm.account.id")).mark();
    }
}
