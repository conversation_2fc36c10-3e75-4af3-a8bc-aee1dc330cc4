package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.KFSOtpPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class KFSOtpPageDataSource implements PageDataSource<KFSOtpPageDataSourceResponse>{

    @Inject
    private static ConfigUtils configUtils;
    @Override
    public KFSOtpPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        KFSOtpPageDataSourceResponse kfsOtpPageDataSourceResponse = new KFSOtpPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        kfsOtpPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        kfsOtpPageDataSourceResponse.setResendOtpformData(new QueryParamUtils().getResendOtpFormData());
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        kfsOtpPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return kfsOtpPageDataSourceResponse;
    }
}
