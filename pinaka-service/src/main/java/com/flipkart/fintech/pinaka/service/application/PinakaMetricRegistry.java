package com.flipkart.fintech.pinaka.service.application;

import com.codahale.metrics.MetricRegistry;

public class PinakaMetricRegistry {

    private volatile static MetricRegistry metricRegistry;

    private PinakaMetricRegistry() {
    }

    public static MetricRegistry getMetricRegistry() {
        if (metricRegistry == null) {
            synchronized (PinakaMetricRegistry.class) {
                if (metricRegistry == null) {
                    metricRegistry = new MetricRegistry();
                }
            }
        }
        return metricRegistry;
    }
}
