package com.flipkart.fintech.pinaka.service.application;

import com.flipkart.fintech.pinaka.api.model.LenderConfig;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON>kumar.<PERSON> on 15/08/18.
 */
public class LenderConfiguration {

    Map<String, List<LenderConfig>> configurations;

    public Map<String, List<LenderConfig>> getConfigurations() {
        return configurations;
    }

    public void setConfigurations(Map<String, List<LenderConfig>> configurations) {
        this.configurations = configurations;
    }
}
