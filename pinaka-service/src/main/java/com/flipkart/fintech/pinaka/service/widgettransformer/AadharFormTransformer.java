package com.flipkart.fintech.pinaka.service.widgettransformer;


import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.response.AadharFormPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

@CustomLog
public class AadharFormTransformer {

    private AadharFormPageDataSourceResponse aadharFormPageDataSourceResponse;
    private static String genericFormjson;

    static {
        genericFormjson = TransformerUtils.readFileasString("template/idfc/AadharForm.json");
    }

    public GenericFormWidgetData buildWidgetData(AadharFormPageDataSourceResponse pageDataSourceResponse)
            throws PinakaClientException {
        try {
            GenericFormWidgetData aadharFormWidgetCopy = ObjectMapperUtil.get().readValue(genericFormjson, GenericFormWidgetData.class);
            aadharFormPageDataSourceResponse = pageDataSourceResponse;
            updateSubmitButton(aadharFormWidgetCopy.getSubmitButton(),aadharFormPageDataSourceResponse);
            return aadharFormWidgetCopy;
        } catch (Exception e) {
            log.error("AadharForm build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }
    private static void updateSubmitButton(SubmitButtonValue submitButton,AadharFormPageDataSourceResponse aadharFormPageDataSourceResponse) {
        Action action = submitButton.getButton().getAction();
        action.setParams(aadharFormPageDataSourceResponse.getQueryParams());
        action.setEncryption(aadharFormPageDataSourceResponse.getEncryptionData());
    }
}