package com.flipkart.fintech.pinaka.service.widgettransformer;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule;
import com.flipkart.fintech.pinaka.service.response.ApplicationStatusPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ApplicationStatusSandboxPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.AccordionItemValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.ListRichKeyValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.RichKeyValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.AccordionWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.gamifiedOnboarding.StepperWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.ApplicationStatus.LOAN_ID_TEXT;

@CustomLog
@NoArgsConstructor
public class ApplicationStatusWidgetTransformer implements BannerWidgetTransformer {
    private static final String accordianWidgetJson;
    private static final String bannerWidgetJson;
    private static final String bannerWidgetPaymentDisbursedJson;
    private static final String stepperWidgetJson;
    private static final String textV2Json;

    private static final StepperWidgetData stepperWidgetData;
    private static final AccordionWidgetData accordionWidgetData;
    private static final BannerWidgetData bannerWidgetData;
    private static final BannerWidgetData bannerWidgetPaymentDisbursedData;
    private static final RichMessageWidgetData textV2WidgetData;


    static {
        accordianWidgetJson = TransformerUtils.readFileasString("template/idfc/ApplicationStatusAccordian.json");
        bannerWidgetJson = TransformerUtils.readFileasString("template/idfc/ApplicationStatusBanner.json");
        bannerWidgetPaymentDisbursedJson = TransformerUtils.readFileasString("template/idfc/ApplicationStatusBannerDisbursed.json");
        stepperWidgetJson = TransformerUtils.readFileasString("template/idfc/ApplicationStatusStepper.json");
        textV2Json = TransformerUtils.readFileasString("template/idfc/ApplicationStatusTextV2.json");
    }

    static {
        try {
            stepperWidgetData = ObjectMapperUtil.get().readValue(stepperWidgetJson, StepperWidgetData.class);
            textV2WidgetData = ObjectMapperUtil.get().readValue(textV2Json, RichMessageWidgetData.class);
            accordionWidgetData = ObjectMapperUtil.get().readValue(accordianWidgetJson, AccordionWidgetData.class);
            bannerWidgetData = ObjectMapperUtil.get().readValue(bannerWidgetJson, BannerWidgetData.class);
            bannerWidgetPaymentDisbursedData = ObjectMapperUtil.get().readValue(bannerWidgetPaymentDisbursedJson, BannerWidgetData.class);

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public ApplicationStatusWidgetTransformer(ApplicationStatusPageDataSourceResponse pageDataSourceResponse) {
        this.pageDataSourceResponse = pageDataSourceResponse;
    }

    private ApplicationStatusPageDataSourceResponse pageDataSourceResponse;

    public StepperWidgetData buildstepperWidgetData(ApplicationStatusPageDataSourceResponse pageDataSourceReponse) {
        if (stepperWidgetData != null &&
                stepperWidgetData.getSteps().size() == 4 &&
                pageDataSourceReponse.getLoanUtilityResponse() != null &&
                pageDataSourceReponse.getLoanUtilityResponse().getResourceData().getPaymentStatus().equals("SUCCESS")
        ) {
            stepperWidgetData.getSteps().get(3).setShowCompleted(true);
            stepperWidgetData.getSteps().get(3).setShowSelected(true);
        }
        return stepperWidgetData;
    }
    public RichMessageWidgetData buildtextV2WidgetData(ApplicationStatusPageDataSourceResponse pageDataSourceReponse) {
        String loanId = pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getLoanId();
        textV2WidgetData.getMessage().getValue().getTitle().setText(LOAN_ID_TEXT + loanId);
        return textV2WidgetData;
    }
    public RichMessageWidgetData buildtextV2WidgetData(ApplicationStatusSandboxPageDataSourceResponse pageDataSourceResponse){
        String loanId = pageDataSourceResponse.getLoanId();
        // todo: return lender application number of ams
        textV2WidgetData.getMessage().getValue().getTitle().setText(LOAN_ID_TEXT + loanId);
        return textV2WidgetData;
    }
    public AccordionWidgetData buildaccordianWidgetData(ApplicationStatusPageDataSourceResponse pageDataSourceReponse) {
        int noInstall=pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getRepaySchedule().size();
        List<RepaySchedule> repayScheduleList =  pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getRepaySchedule();
        Collections.sort(repayScheduleList, Comparator.nullsFirst(Comparator.comparing(inst -> {
            if (inst.getInstallmentNumber() == null) {
                return null;
            }
            try {
                return Integer.parseInt(inst.getInstallmentNumber());
            } catch (NumberFormatException e) {
                return null;
            }
        })));
        String firstEmiDate = repayScheduleList.get(0).getDueDate().split("T")[0];
        String lastEmiDate = repayScheduleList.get(noInstall-1).getDueDate().split("T")[0];
        AccordionItemValue accordionItemValue = accordionWidgetData.getRenderableComponents().get(0).getValue();
        ListRichKeyValue listRichKeyValue = (ListRichKeyValue) accordionItemValue.getContent();
        RichKeyValue loanAmtRichKeyValue = listRichKeyValue.getValues().get(0);
        loanAmtRichKeyValue.getValue().setText(
                pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getKfsDetails().get(0).getLoanAmt());
        RichKeyValue processingFeeRichKeyValue = listRichKeyValue.getValues().get(1);
        processingFeeRichKeyValue.getValue().setText(
                pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getKfsDetails().get(0).getProcessFee());
        RichKeyValue netAmtRichKeyValue = listRichKeyValue.getValues().get(2);
        netAmtRichKeyValue.getValue().setText(
                pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getKfsDetails().get(0).getNetDisbAmt());
        AccordionItemValue repaymentSchedule = accordionWidgetData.getRenderableComponents().get(1).getValue();
        ListRichKeyValue repaymentSchedulelist = (ListRichKeyValue) repaymentSchedule.getContent();
        RichKeyValue monthlyEmi = repaymentSchedulelist.getValues().get(0);
        monthlyEmi.getValue().setText(
                pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getKfsDetails().get(0).getAmtInstall());
        RichKeyValue loanPeriod = repaymentSchedulelist.getValues().get(1);
        loanPeriod.getValue().setText(
                pageDataSourceReponse.getAutoDisbursalResponse().getResourceData().get(0).getKfsDetails().get(0).getNumInstallment());
        RichKeyValue firstEmi = repaymentSchedulelist.getValues().get(2);
        firstEmi.getValue().setText(firstEmiDate);
        RichKeyValue lastEmi = repaymentSchedulelist.getValues().get(3);
        lastEmi.getValue().setText(lastEmiDate);
        updateSubmitButton(accordionWidgetData, pageDataSourceReponse);
        return accordionWidgetData;
    }

    private void updateSubmitButton(AccordionWidgetData accordionWidgetData,ApplicationStatusPageDataSourceResponse pageDataSourceReponse) {
        try {
            ListRichKeyValue listRichKeyValue = (ListRichKeyValue) accordionWidgetData.getRenderableComponents().get(1).getValue().getContent();
            listRichKeyValue.getButton().getAction().setParams(pageDataSourceReponse.getQueryParams());
            listRichKeyValue.getButton().getAction().setEncryption(pageDataSourceReponse.getEncryptionData());
        }
        catch(Exception e){
            log.error("Error while updating the submit button for applicationStatus Page with error : {}",e.getMessage());
        }
    }

    @Override
    public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) {
        if (pageDataSourceResponse.getLoanUtilityResponse() != null &&
                pageDataSourceResponse.getLoanUtilityResponse().getResourceData().getPaymentStatus().equals("SUCCESS")
        ) {
            return bannerWidgetPaymentDisbursedData;
        }
        return bannerWidgetData;
    }
}
