package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.response.AddressDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.insurtech.TextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

@CustomLog
public class AddressDetailsFormTransformer {
    private static String addressFormJson;

    private final AddressDetailsPageDataSourceResponse addressDetailsPageDataSourceResponse;
    //todo:move file name to constants
    static {
            addressFormJson = TransformerUtils.readFileasString("template/sandbox/AddressPage.json");
    }
    public AddressDetailsFormTransformer(AddressDetailsPageDataSourceResponse addressDetailsPageDataSourceResponse) {
        this.addressDetailsPageDataSourceResponse = addressDetailsPageDataSourceResponse;
    }

    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        try {//TODO test
            GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get()
                    .readValue(getFormJson(applicationDataResponse), GenericFormWidgetData.class);
            updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());

            Map<String,String> verifyPincode = (Map<String, String>) applicationDataResponse.getApplicationData().get("verifyPincode");
            if(!Objects.isNull(verifyPincode)){
                updatePincodeField(genericFormWidgetDataCopy, verifyPincode.get("pincode"));
                updateCityField(genericFormWidgetDataCopy, verifyPincode.get("city"));
                updateStateField(genericFormWidgetDataCopy, verifyPincode.get("state"));
            }
            return genericFormWidgetDataCopy;
        } catch (Exception e) {
            log.error("Address Details build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }
    private void updateCityField(GenericFormWidgetData genericFormWidgetDataCopy, String cityName) {
        TextBoxFormFieldValueV0 cityField = (TextBoxFormFieldValueV0) genericFormWidgetDataCopy.getRenderableComponents().get(3).getValue();
        if (!StringUtils.isEmpty(cityName) && !Objects.isNull(cityField)) {
            cityField.setValue(cityName);
            cityField.setDisabled(true);
        }
    }
    private void updatePincodeField(GenericFormWidgetData genericFormWidgetDataCopy, String pincode) {
        TextBoxFormFieldValueV0 pincodeField = (TextBoxFormFieldValueV0) genericFormWidgetDataCopy.getRenderableComponents().get(0).getValue();
        if (!StringUtils.isEmpty(pincode) && !Objects.isNull(pincodeField)) {
            pincodeField.setValue(pincode);
            pincodeField.setDisabled(true);
        }
    }

    private void updateStateField(GenericFormWidgetData genericFormWidgetDataCopy, String state) {
        TextBoxFormFieldValueV0 stateField = (TextBoxFormFieldValueV0) genericFormWidgetDataCopy.getRenderableComponents().get(4).getValue();
        if (!StringUtils.isEmpty(state) && !Objects.isNull(stateField)) {
            stateField.setValue(state);
            stateField.setDisabled(true);
        }
    }

    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(addressDetailsPageDataSourceResponse.getQueryParams());
        action.setEncryption(addressDetailsPageDataSourceResponse.getEncryptionData());
    }
    private String getFormJson(ApplicationDataResponse applicationDataResponse){
//        String lender = ApplicationTypeUtils.getLender(applicationDataResponse);
        return addressFormJson;
    }

}
