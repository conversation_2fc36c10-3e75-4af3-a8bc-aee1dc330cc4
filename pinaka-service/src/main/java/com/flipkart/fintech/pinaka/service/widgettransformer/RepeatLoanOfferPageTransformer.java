package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.core.v7.ApplicationHelper;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan.RepeatLoanOfferPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan.response.RepeatLoanOfferPageData;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.KeyValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.BannerImageValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.DynamicBannerImageValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.RichMultiImageBannerValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.RichMultiImageBannerWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.sms.ListKeyValue;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;

public class RepeatLoanOfferPageTransformer implements SubmitButtonWidgetTransformer {

  private static final String repeatLoanOfferMultiImageBannerJson = TransformerUtils.readFileasString(
      "template/repeat_loan/RepeatLoanOfferRichMultiImageBanner.json");
  private static final String repeatLoanOfferSubmitButtonJson = TransformerUtils.readFileasString(
      "template/repeat_loan/RepeatLoanOfferSubmitButton.json");

  public static final String OPENMARKET_BANNER_TITLE = "Congratulations, You are eligible for";
  public static final String PREAPPROVED_BANNER_TITLE = "Choose amount and get";
  public static final String PREAPPROVED_BANNER_SUBTITLE = "Instant Loan of";
  public static final String OPENMARKET_BANNER_SUBTITLE = "New Loan";

  private final RepeatLoanOfferPageDataSource repeatLoanOfferPageDataSource;

  @Inject
  public RepeatLoanOfferPageTransformer(
      RepeatLoanOfferPageDataSource repeatLoanOfferPageDataSource) {
    this.repeatLoanOfferPageDataSource = repeatLoanOfferPageDataSource;
  }

  @Override
  public SubmitButtonWidgetData buildSubmitButtonWidgetData(
      ApplicationDataResponse applicationDataResponse)
      throws JsonProcessingException, PinakaException {
    // todo: revisit the logic
    RepeatLoanOfferPageData data = repeatLoanOfferPageDataSource.getData(applicationDataResponse);
    SubmitButtonWidgetData submitButtonWidgetData = ObjectMapperUtil.get()
        .readValue(repeatLoanOfferSubmitButtonJson, SubmitButtonWidgetData.class);
    updateSubmitButtonWidgetData(submitButtonWidgetData, data);
    return submitButtonWidgetData;
  }

  public RichMultiImageBannerWidgetData buildRepeatLoanOfferMultiImageBannerWidgetData(
      RepeatLoanOfferPageData repeatLoanOfferPageData) throws JsonProcessingException {
    RichMultiImageBannerWidgetData richMultiImageBannerWidgetData = ObjectMapperUtil.get()
        .readValue(repeatLoanOfferMultiImageBannerJson, RichMultiImageBannerWidgetData.class);
    setRepeatLoanOfferAmount(repeatLoanOfferPageData, richMultiImageBannerWidgetData);
    return richMultiImageBannerWidgetData;
  }

  private void updateSubmitButtonWidgetData(SubmitButtonWidgetData submitButtonWidgetData,
      RepeatLoanOfferPageData data) {
    Action secondaryAction = Objects.requireNonNull(
            submitButtonWidgetData.getSubmitButton().getSecondaryButtons())
        .get(0)
        .getAction();
    Objects.requireNonNull(secondaryAction).setParams(data.getCurrentLoanQueryParams());
  }

  private void setRepeatLoanOfferAmount(RepeatLoanOfferPageData repeatLoanOfferPageData,
      RichMultiImageBannerWidgetData richMultiImageBannerWidgetData) {
    RichMultiImageBannerValue value = richMultiImageBannerWidgetData.getBanner().getValue();
    if (Objects.isNull(value)) {
      return;
    }

    RenderableComponent<BannerImageValue> bannerImage = value.getBannerImage();
    if (Objects.isNull(bannerImage)) {
      return;
    }
    RenderableComponent<RichTextValue> title = new RenderableComponent<>();
    RenderableComponent<RichTextValue> subTitle = new RenderableComponent<>();

    if(Objects.isNull(title.getValue()) || Objects.isNull(subTitle.getValue())){
      return;
    }

    title.getValue().setText(OPENMARKET_BANNER_TITLE);
    subTitle.getValue().setText(OPENMARKET_BANNER_SUBTITLE);

    DynamicBannerImageValue bannerImageValue = (DynamicBannerImageValue) bannerImage.getValue();
    if (Objects.isNull(bannerImageValue)) {
      return;
    }

    ListKeyValue listKeyValue = new ListKeyValue();
    listKeyValue.setValues(getKeyValueList(repeatLoanOfferPageData));

    RenderableComponent<ListKeyValue> listKeyValueRenderableComponent = new RenderableComponent<>();
    listKeyValueRenderableComponent.setValue(listKeyValue);
    bannerImageValue.setKeyValueList(listKeyValueRenderableComponent);
  }

  private static List<KeyValue> getKeyValueList(RepeatLoanOfferPageData repeatLoanOfferPageData) {
    List<KeyValue> keyValues = new ArrayList<>();

    KeyValue amountKeyValue = new KeyValue();
    amountKeyValue.setKey("approvedLimitAmount");
    KeyValue bannerTextKeyValue = new KeyValue();
    bannerTextKeyValue.setKey("preApprovedText");

    Long preApprovedOfferAmount = repeatLoanOfferPageData.getPreApprovedOfferAmount();
    if (Objects.nonNull(preApprovedOfferAmount)) {
      amountKeyValue.setValue(" ₹" + ApplicationHelper.format(preApprovedOfferAmount, false));
      bannerTextKeyValue.setValue("Pre approved for you");
    } else {
      amountKeyValue.setValue(" ₹10,00,000");
      bannerTextKeyValue.setValue("");
    }
    keyValues.add(amountKeyValue);
    keyValues.add(bannerTextKeyValue);

    return keyValues;
  }
}
