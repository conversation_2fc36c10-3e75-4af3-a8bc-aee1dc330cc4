package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.LiveSelfiePageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.LiveSelfieWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

public class LiveSelfieTransformer {

    private static String liveSelfieJson;

    static {
        liveSelfieJson = TransformerUtils.readFileasString("template/idfc/LiveSelfie.json");
    }
    public LiveSelfieWidgetData buildWidgetData(LiveSelfiePageDataSourceResponse liveSelfiePageDataSourceResponse)
            throws JsonProcessingException {
        LiveSelfieWidgetData liveSelfieWidgetDataDeepCopy = ObjectMapperUtil.get().readValue(liveSelfieJson, LiveSelfieWidgetData.class);
        updateAction(liveSelfiePageDataSourceResponse,liveSelfieWidgetDataDeepCopy);
        updateToken(liveSelfiePageDataSourceResponse,liveSelfieWidgetDataDeepCopy);
        liveSelfieWidgetDataDeepCopy.getContextMap().put("transactionId",liveSelfiePageDataSourceResponse.getApplicationId());
        return liveSelfieWidgetDataDeepCopy;

    }

    private void updateToken(LiveSelfiePageDataSourceResponse liveSelfiePageDataSourceResponse,LiveSelfieWidgetData liveSelfieWidgetDataDeepCopy) {
        liveSelfieWidgetDataDeepCopy.getContextMap().put("accessToken",liveSelfiePageDataSourceResponse.getAuthTokenResponse().getSelfieToken().getToken());
    }

    private void updateAction(LiveSelfiePageDataSourceResponse liveSelfiePageDataSourceResponse,LiveSelfieWidgetData liveSelfieWidgetDataDeepCopy) {
        Action action  = liveSelfieWidgetDataDeepCopy.getSuccessAction();
        action.setEncryption(liveSelfiePageDataSourceResponse.getEncryptionData());
        action.setParams(liveSelfiePageDataSourceResponse.getQueryParams());
    }


}