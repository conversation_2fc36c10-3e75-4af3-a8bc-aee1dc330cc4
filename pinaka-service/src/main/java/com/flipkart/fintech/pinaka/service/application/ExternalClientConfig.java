package com.flipkart.fintech.pinaka.service.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.service.external.PageServiceConfig;

/**
 * <AUTHOR> H Adavi 04/12/18
 */
public class ExternalClientConfig {

	@JsonProperty("page_service_config")
	private PageServiceConfig pageServiceConfig;

	public PageServiceConfig getPageServiceConfig() {
		return pageServiceConfig;
	}

	public void setPageServiceConfig(PageServiceConfig pageServiceConfig) {
		this.pageServiceConfig = pageServiceConfig;
	}

	@Override
	public String toString() {
		return "ExternalClientConfig{" +
				"pageServiceConfig=" + pageServiceConfig +
				'}';
	}
}
