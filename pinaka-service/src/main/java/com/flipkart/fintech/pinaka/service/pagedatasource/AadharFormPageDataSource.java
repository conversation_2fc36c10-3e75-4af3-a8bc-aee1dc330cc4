package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.AadharFormPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Optional;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class AadharFormPageDataSource implements PageDataSource {

    @Inject
    private static ConfigUtils configUtils;
    @Override
    public AadharFormPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        AadharFormPageDataSourceResponse aadharFormPageDataSourceResponse = new AadharFormPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        aadharFormPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        aadharFormPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return aadharFormPageDataSourceResponse;
    }
}