package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.Map;

@CustomLog
public class CustomScreenTransformer {
    private final Map<String, Map> successScreensMap;

    @Inject
    public CustomScreenTransformer(@Named("lenderCustomScreens") Map<String, Map> successScreensMap) {
        this.successScreensMap = successScreensMap;
    }

    public AnnouncementV2WidgetData buildWidgetData(
            ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        AnnouncementV2WidgetData announcementV2WidgetData;
        try {
            String lender = (String) applicationDataResponse.getApplicationData().get("financial_provider");
            String applicationState = applicationDataResponse.getApplicationState();
            announcementV2WidgetData = ObjectMapperUtil.get().readValue((String)successScreensMap.get(lender).get(applicationState), AnnouncementV2WidgetData.class);
            announcementV2WidgetData.getData().getValue().getRichButton().getAction().setParams(WidgetTransformerUtils.getButtonParams(applicationDataResponse));
            return announcementV2WidgetData;
        } catch (Exception e) {
            log.error("AnnouncementV2WidgetData build offerRejectionWidget Data failed with error : {}",
                    e.getMessage());
            throw new PinakaClientException(e);
        }
    }

}
