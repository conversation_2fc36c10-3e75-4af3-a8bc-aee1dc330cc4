package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_LEAD_V4_FLOW;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_WHITELISTED_USERS;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_TRAFFIC_PERCENTAGE;

public class LV4Util {

    private LV4Util() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean isLv4Enabled(DynamicBucket dynamicBucket, String smUserId) {
        if (!dynamicBucket.getBoolean(ENABLE_LEAD_V4_FLOW)) {
            return false;
        }

        // Check whitelist first
        List<String> whitelistedUsers = dynamicBucket.getStringArray(LEAD_V4_WHITELISTED_USERS);
        if (whitelistedUsers != null && whitelistedUsers.contains(smUserId)) {
            return true;
        }

        // Percentage-based routing
        int percentage = dynamicBucket.getInt(LEAD_V4_TRAFFIC_PERCENTAGE);
        int hash = Math.abs(smUserId.hashCode() % 100);
        return hash < percentage;
    }

    public static boolean isLv4Application(ApplicationDataResponse applicationDataResponse) {
        if(applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
            return false;
        }
        if (StringUtils.isNotBlank(applicationDataResponse.getApplicationState()) &&
            applicationDataResponse.getApplicationState().contains("LEAD_V4")) {
            return true;
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        return isLv4Application(applicationData);
    }

    public static boolean isLv4Application(Map<String, Object> applicationData) {
        Set<String> keys = applicationData.keySet();
        if (!keys.isEmpty()) {
            for (String key : keys) {
                if (key.contains("leadV4")) {
                    return true;
                }
            }
        }
        return false;
    }
}
