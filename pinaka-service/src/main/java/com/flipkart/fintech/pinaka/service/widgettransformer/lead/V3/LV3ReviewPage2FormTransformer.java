package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.FormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import lombok.CustomLog;
import org.apache.commons.text.StringSubstitutor;

import java.util.Map;
import java.util.Objects;

import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.getLeadEvents;

@CustomLog
public class LV3ReviewPage2FormTransformer implements FormWidgetTransformer {

    private static final String ONLY_WORK_DETAILS_FORM_TEMPLATE;

    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final FormWidgetDataJsonParser formWidgetDataJsonParser;
    private final LocationRequestHandler locationRequestHandler;
    private final BqIngestionHelper bqIngestionHelper;

    private final FormConfig formConfig = new FormConfig("18", "90",
            PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);

    static public class WorkDetailsBannerFormTransformer implements BannerWidgetTransformer {

        private static final String NAME_PAGE_BANNER;

        static {
            NAME_PAGE_BANNER = TransformerUtils.readFileasString("template/lead/V3/ReviewScreenBanner.json");
        }

        @Override
        public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
            BannerWidgetData bannerWidgetData;
            try {
                bannerWidgetData = ObjectMapperUtil.get().readValue(NAME_PAGE_BANNER, BannerWidgetData.class);
            } catch (Exception e) {
                throw new PinakaException("Error while building WorkDetails Banner Widget for LV3, userId: " + applicationDataResponse.getSmUserId(), e);
            }
            return bannerWidgetData;
        }
    }

    static {
        ONLY_WORK_DETAILS_FORM_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/WorkDetails.json");
    }

    public LV3ReviewPage2FormTransformer(Decrypter decrypter, DynamicBucket dynamicBucket,
                                         FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
                                         FormWidgetDataFetcher formWidgetDataFetcher,
                                         FormWidgetDataJsonParser formWidgetDataJsonParser,
                                         LocationRequestHandler locationRequestHandler, BqIngestionHelper bqIngestionHelper) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.formWidgetDataJsonParser = formWidgetDataJsonParser;
        this.locationRequestHandler = locationRequestHandler;
        this.bqIngestionHelper = bqIngestionHelper;
    }

    @Override
    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        GenericFormWidgetData genericFormWidgetData = null;
        String pageState = "WORK_DETAILS";
        try {
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();
            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
            genericFormWidgetData = ObjectMapperUtil.get()
                    .readValue(getFormJson(ONLY_WORK_DETAILS_FORM_TEMPLATE, applicationDataResponse), GenericFormWidgetData.class);
            Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getFormFieldValueMapToPrefillForRenderableComponents(genericFormWidgetData.getRenderableComponents());
            LV3Util.updateGroupedWidgetSubmitButton(Objects.requireNonNull(genericFormWidgetData.getSubmitButton()), reviewUserDataSourceResponse);
            Map<String, Object> userData = this.formWidgetDataFetcher
                    .getDataForFields(formFieldValueMapToPrefill.keySet(), leadPageDataSourceResponse,
                    reviewUserDataSourceResponse, decrypter, locationRequestHandler);

            formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
            formWidgetDataJsonParser.updateFormFieldValueMapToPrefill(formFieldValueMapToPrefill, genericFormWidgetData.getRenderableComponents());
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), "V3"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V3"));
            throw new PinakaException("Error while building WorkDetails Widget for LV3, for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return genericFormWidgetData;
    }

    String getFormJson(String leadNamePage, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(leadNamePage);
    }
}
