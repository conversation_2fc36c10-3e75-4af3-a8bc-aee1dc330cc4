package com.flipkart.fintech.pinaka.service.datacryptography;

import com.flipkart.fintech.security.aes.AESService;

import java.nio.charset.StandardCharsets;
import java.util.*;

import lombok.CustomLog;
import javax.validation.constraints.NotNull;


@CustomLog
public class FormDataEncryption implements DataEncryption {

    private static final String ENCRYPT_KEY = "cGlpRW5jcnlwdGlvbktleQ==";

    @NotNull
    public String encryptString(@NotNull String data) {
        byte[] ciphertextBytes = AESService.encrypt(ENCRYPT_KEY, data.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(ciphertextBytes), StandardCharsets.UTF_8);
    }
}
