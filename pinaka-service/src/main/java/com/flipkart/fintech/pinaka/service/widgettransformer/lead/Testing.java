package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

public class Testing {

    public static void main(String[] args) {
        String regex = "^(\\S.*\\S|\\S)$";
        System.out.println("Empty matched " + "".matches(regex));
        System.out.println("A B matched " + "A B".matches(regex));
        System.out.println(" A B matched " + " A B".matches(regex));
        System.out.println("A B  matched " + "A B ".matches(regex));
        System.out.println(" A B  matched " + " A B ".matches(regex));
        System.out.println("A  matched " + " A ".matches(regex));

        Object s = null;
        String x = String.valueOf(s);
        String y = (String) (s);
    System.out.println(x == null);
    System.out.println("null".equals(x));
    System.out.println(y == null);
  }
}
