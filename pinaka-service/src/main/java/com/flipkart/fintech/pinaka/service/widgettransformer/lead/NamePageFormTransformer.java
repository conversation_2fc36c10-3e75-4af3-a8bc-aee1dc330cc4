package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.FormWidgetTransformer;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 25/12/24
 */
@CustomLog
public class NamePageFormTransformer implements BannerWidgetTransformer, FormWidgetTransformer {

    private static String leadNamePage;
    private Map<String, NamePageFormTransformer.FormFields> formFieldsMap;

    private static final String personalLoanBannerjson;

    private final Decrypter decrypter;

    private final DynamicBucket dynamicBucket;

    private final LeadPageDataSourceResponse leadPageDataSourceResponse;

    private static final BannerWidgetData bannerWidgetData;
    public static final String PL_DYNAMIC_IMAGE_BANNER_URL = "dynamicImageBannerUrl";

    private final FormConfig formConfig = new FormConfig("18", "90",
            PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);

    static {
        personalLoanBannerjson = TransformerUtils.readFileasString(
                "template/sandbox/PersonalLoanBanner.json");
        leadNamePage = TransformerUtils.readFileasString("template/lead/LeadNameScreen.json");
        try {
            bannerWidgetData = ObjectMapperUtil.get()
                    .readValue(personalLoanBannerjson, BannerWidgetData.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    public NamePageFormTransformer(LeadPageDataSourceResponse leadPageDataSourceResponse, Map<String, NamePageFormTransformer.FormFields> formFieldsMap,
                                   Decrypter decrypter, DynamicBucket dynamicBucket) {
        this.leadPageDataSourceResponse = leadPageDataSourceResponse;
        this.formFieldsMap = formFieldsMap;
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
    }

    public enum FormFields {
        FIRST_NAME {
            public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, Decrypter decrypter) {
                if (StringUtils.isNotEmpty(profile.getFirstName())) {
                    String prefilledValue = profile.getFirstName().trim();
                    formFieldValue.setValue(prefilledValue);
                    formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), prefilledValue));
                }
            }
        },
        LAST_NAME {
            public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, Decrypter decrypter) {
                if (StringUtils.isNotEmpty(profile.getLastName())) {
                    String prefilledValue = profile.getLastName().trim();
                    formFieldValue.setValue(prefilledValue);
                    formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), prefilledValue));
                }
            }
        },
        PHONE_NUMBER {
            public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, Decrypter decrypter) {
                if (StringUtils.isNotEmpty(profile.getPhoneNo())) {
                    String prefilledValue = profile.getPhoneNo();
                    formFieldValue.setValue(prefilledValue);
                    formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), prefilledValue));
                }
            }
        };
        public abstract void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, Decrypter decrypter);
    }


    @Override
    public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) {
        Objects.requireNonNull(Objects.requireNonNull(bannerWidgetData.getRenderableComponents()).get(0).getValue())
                .setDynamicImageUrl(dynamicBucket.getString(PL_DYNAMIC_IMAGE_BANNER_URL));
        return bannerWidgetData;
    }

    @Override
    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) {
        GenericFormWidgetData widgetData = null;
        try {
            widgetData = ObjectMapperUtil.get()
                    .readValue(getFormJson(leadNamePage, applicationDataResponse), GenericFormWidgetData.class);

            ProfileDetailedResponse profile = leadPageDataSourceResponse.getProfile();
            updateSubmitButton(widgetData.getSubmitButton());
            if (Objects.nonNull(profile)) {
                prefillLeadFormData(profile, widgetData);
            }

        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return widgetData;
    }

    private String getFormJson(String leadNamePage, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMap(
                applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(leadNamePage);
    }

    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(leadPageDataSourceResponse.getQueryParams());
        action.setEncryption(leadPageDataSourceResponse.getEncryptionData());
    }


    private void prefillLeadFormData(ProfileDetailedResponse profile, GenericFormWidgetData widgetData) {
        if(leadPageDataSourceResponse.getIsNameEncrypted() && StringUtils.isNotEmpty(profile.getFirstName()) && StringUtils.isNotEmpty(profile.getLastName())){
            profile.setFirstName(decrypter.decryptString(profile.getFirstName()));
            profile.setLastName(decrypter.decryptString(profile.getLastName()));
        }
        List<RenderableComponent<FormFieldValue>> renderableComponents = Objects.requireNonNull(
                widgetData.getRenderableComponents());

        for(RenderableComponent<FormFieldValue> renderableComponent: renderableComponents){
            if(formFieldsMap.containsKey(renderableComponent.getValue().getName())){
                try {
                    formFieldsMap.get(renderableComponent.getValue().getName())
                            .prefill(profile, renderableComponent.getValue(), decrypter);
                } catch (Exception e){
                    log.error("Prefill form data in name page got exception while prefilling for sm_user_id " +
                            leadPageDataSourceResponse.getProfile().getSmUserId()+ " for form key "+ renderableComponent.getValue().getName(), e);
                }
            }
        }
    }
}
