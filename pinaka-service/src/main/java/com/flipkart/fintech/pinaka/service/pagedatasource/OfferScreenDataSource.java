package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationStatusResponse;
import com.flipkart.fintech.pinaka.service.response.OfferScreenPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;


public class OfferScreenDataSource implements PageDataSource<OfferScreenPageDataSourceResponse> {

    @Inject
    private static ConfigUtils configUtils;
    @Override
    public OfferScreenPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        InitialOfferGenerationStatusResponse initialOfferGenerationStatusResponse = ObjectMapperUtil.get()
                .convertValue(applicationDataResponse.getApplicationData().get("pollOffer"),
                        InitialOfferGenerationStatusResponse.class);
        OfferScreenPageDataSourceResponse offerScreenPageDataSourceResponse = new OfferScreenPageDataSourceResponse();
        offerScreenPageDataSourceResponse.setInitialOfferGenerationStatusResponse(initialOfferGenerationStatusResponse);
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        offerScreenPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        offerScreenPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return offerScreenPageDataSourceResponse;
    }
}