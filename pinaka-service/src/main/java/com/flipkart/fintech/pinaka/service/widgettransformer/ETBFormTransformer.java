package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.ETBGenerateOtpResponse;
import com.flipkart.fintech.pinaka.service.response.ETBVerifyOTPResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichButtonValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.OtpCustomFormFieldData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ETBFormTransformer {
    private static String genericFormjson;
    private static String genericVerifyFormjson;
    private ETBGenerateOtpResponse etbGenerateOtpResponse;
    private ETBVerifyOTPResponse etbVerifyOTPResponse;

    static {
        genericFormjson = TransformerUtils.readFileasString("template/idfc/ETBGenerateOtpForm.json");
        genericVerifyFormjson = TransformerUtils.readFileasString("template/idfc/ETBVerifyOtp.json");
    }

    public GenericFormWidgetData buildWidgetData(ETBGenerateOtpResponse pageDataSourceReponse) throws JsonProcessingException {
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(genericFormjson, GenericFormWidgetData.class);
        etbGenerateOtpResponse = pageDataSourceReponse;
        updateOptions((DropdownFormFieldValue) genericFormWidgetData.getRenderableComponents().get(0).getValue());
        updateSubmitButtonParams(genericFormWidgetData.getSubmitButton());
        updateSecondaryButtonParams(genericFormWidgetData.getSubmitButton().getSecondaryButtons().get(0));
        return genericFormWidgetData;
    }

    private void updateResendAction(Action action) {
        action.setParams(etbVerifyOTPResponse.getResendOtpActionParams());
        action.setEncryption(etbVerifyOTPResponse.getEncryptionData());
    }

    private void updateOptions(DropdownFormFieldValue value) {
        List<DropdownValue> options = new ArrayList<>();
        for (String phoneNumber : etbGenerateOtpResponse.getPhoneNumberList()) {
            if(StringUtils.isNotBlank(phoneNumber)) {
                String maskPhoneNumber = getMaskPhoneNumber(phoneNumber);
                DropdownValue dropdownValue = new DropdownValue(phoneNumber, maskPhoneNumber);
                dropdownValue.setType(DropdownType.PLAIN_TEXT);
                options.add(dropdownValue);
            }
        }
        value.setOptions(options);
    }

    private String getMaskPhoneNumber(String phoneNumber) {
        String maskedPart = "XXXXXX";
        if(phoneNumber.startsWith("+91")){
            String secondPart = phoneNumber.substring(9);
            return "+91"+maskedPart+secondPart;
        }
        return maskedPart+ phoneNumber.substring(6);
    }

    public GenericFormWidgetData buildVerifyOtpWidgetData(ETBVerifyOTPResponse pageDataSourceReponse) throws JsonProcessingException {
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(genericVerifyFormjson, GenericFormWidgetData.class);
        etbVerifyOTPResponse = pageDataSourceReponse;
        CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) genericFormWidgetData.getRenderableComponents()
                .get(0).getValue();
        OtpCustomFormFieldData otpCustomFormFieldData  = (OtpCustomFormFieldData) customFormFieldValue.getCustomFieldData();
        updateResendAction(otpCustomFormFieldData.getResendText().getAction());
        updateVerifySubmitButton(genericFormWidgetData.getSubmitButton());
        return genericFormWidgetData;
    }

    public void updateSubmitButtonParams(SubmitButtonValue submitButton) {
        Map<String, Object> submitButtonParams = new HashMap<>(etbGenerateOtpResponse.getQueryParams());
        submitButton.getButton().getAction().setParams(submitButtonParams);
        submitButton.getButton().getAction().setEncryption(etbGenerateOtpResponse.getEncryptionData());
    }

    public void updateSecondaryButtonParams(RenderableComponent<RichButtonValue> richButtonValueRenderableComponent) {
        Map<String, Object> secondaryButtonParams = new HashMap<>(etbGenerateOtpResponse.getQueryParams());

        Map<String, Object> formData = new HashMap<>();
        formData.put("phoneNumber", "NTB");

        secondaryButtonParams.put("formData", formData);

        richButtonValueRenderableComponent.getAction().setParams(secondaryButtonParams);
    }


    private void updateVerifySubmitButton(SubmitButtonValue submitButton) {
        submitButton.getButton().getAction().setEncryption(etbVerifyOTPResponse.getEncryptionData());
        submitButton.getButton().getAction().setParams(etbVerifyOTPResponse.getQueryParams());
    }
}
