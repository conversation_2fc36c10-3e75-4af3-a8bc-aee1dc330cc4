package com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan;

import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.lending.orchestrator.service.OfferService;
import com.flipkart.fintech.offer.orchestrator.model.PreApprovedOfferDetails;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan.response.RepeatLoanOfferPageData;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class RepeatLoanOfferPageDataSource implements PageDataSource<RepeatLoanOfferPageData> {

  private final OfferService offerService;
  private final ApplicationService applicationService;

  @Inject
  public RepeatLoanOfferPageDataSource(OfferService offerService,
      ApplicationService applicationService) {
    this.offerService = offerService;
    this.applicationService = applicationService;
  }

  @Override
  public RepeatLoanOfferPageData getData(ApplicationDataResponse applicationDataResponse)
      throws PinakaException {
    MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(
        applicationDataResponse.getExternalUserId(), applicationDataResponse.getSmUserId(), applicationDataResponse.getMerchantId());
    return RepeatLoanOfferPageData.builder()
        .preApprovedOfferAmount(getPreApprovedOfferAmount(merchantUser))
        .currentLoanQueryParams(getQueryParams(applicationDataResponse))
        .build();
  }

  private Long getPreApprovedOfferAmount(MerchantUser merchantUser) {
    return offerService.getActivePreApprovedOffersForLead(merchantUser)
        .map(PreApprovedOfferDetails::getAmount)
        .orElse(null);
  }

  private Map<String, Object> getQueryParams(ApplicationDataResponse applicationDataResponse) {
    List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
    return QueryParamUtils.getQueryParams(queryParams);
  }
}
