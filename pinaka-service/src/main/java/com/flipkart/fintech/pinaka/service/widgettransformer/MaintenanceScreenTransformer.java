package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

@CustomLog
public class MaintenanceScreenTransformer implements SubmitButtonWidgetTransformer {

  private static String maintenanceJson;

  static {
    maintenanceJson = TransformerUtils.readFileasString("template/common/MaintenancePage.json");
  }

  public AnnouncementV2WidgetData buildMaintenanceScreenWidgetData() throws PinakaClientException {
    AnnouncementV2WidgetData announcementV2WidgetData;
    try {
      announcementV2WidgetData = ObjectMapperUtil.get()
          .readValue(maintenanceJson, AnnouncementV2WidgetData.class);
      return announcementV2WidgetData;
    } catch (Exception e) {
      log.error("AnnouncementV2WidgetData build maintenance Data failed with error : {}",
          e.getMessage());
      throw new PinakaClientException(e);
    }
  }

  @Override
  public SubmitButtonWidgetData buildSubmitButtonWidgetData(
      ApplicationDataResponse applicationDataResponse) {
    {
      return null;

    }
  }

}
