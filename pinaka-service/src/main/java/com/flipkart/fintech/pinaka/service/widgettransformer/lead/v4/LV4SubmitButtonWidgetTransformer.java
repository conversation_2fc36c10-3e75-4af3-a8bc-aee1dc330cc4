package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.GroupedFormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.LEAD_V4_LANDING_PAGE;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.*;

@CustomLog
public class LV4SubmitButtonWidgetTransformer implements GroupedFormWidgetTransformer {

    private static final String SUBMIT_BUTTON_TEMPLATE;
    private final FormConfig formConfig;
    private final DynamicBucket dynamicBucket;
    private final Decrypter decrypter;
    private final BqIngestionHelper bqIngestionHelper;
    private final FormWidgetDataJsonParser formWidgetDataJsonParser;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final LocationRequestHandler locationRequestHandler;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;

    static {
        SUBMIT_BUTTON_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageSubmitButton.json");
    }

    @Inject
    public LV4SubmitButtonWidgetTransformer(Decrypter decrypter,
                                            DynamicBucket dynamicBucket,
                                            BqIngestionHelper bqIngestionHelper,
                                            FormWidgetDataJsonParser formWidgetDataJsonParser,
                                            FormWidgetDataFetcher formWidgetDataFetcher,
                                            LocationRequestHandler locationRequestHandler,
                                            FormWidgetDataPrefillUtils formWidgetDataPrefillUtils) {
        formConfig = new FormConfig("18", "90",
                PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        this.dynamicBucket = dynamicBucket;
        this.decrypter = decrypter;
        this.bqIngestionHelper = bqIngestionHelper;
        this.formWidgetDataJsonParser = formWidgetDataJsonParser;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.locationRequestHandler = locationRequestHandler;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
    }

    @Override
    public GroupedFormWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        GroupedFormWidgetData groupedFormWidgetData;
        try {
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();

            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
            groupedFormWidgetData = ObjectMapperUtil.get().readValue(SUBMIT_BUTTON_TEMPLATE, GroupedFormWidgetData.class);

            // Update form fields with user data using LV3-style enum approach
            updateFormFieldsWithUserDataV3Style(groupedFormWidgetData, leadPageDataSourceResponse, reviewUserDataSourceResponse);

            // Update tracking data with correct accountId and applicationId
            updateTrackingData(groupedFormWidgetData, applicationDataResponse);

            // Update submit button with user data (V4 has different structure than V3)
            updateV4SubmitButton(groupedFormWidgetData, reviewUserDataSourceResponse);

            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, LEAD_V4_LANDING_PAGE, applicationDataResponse.getApplicationState(), "V3"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V3"));
            throw new PinakaException("Error while building widget Group Data for LV4 Landing Page for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return groupedFormWidgetData;
    }

    /**
     * Updates form fields with user data using LV3-style enum approach
     */
    private void updateFormFieldsWithUserDataV3Style(GroupedFormWidgetData groupedFormWidgetData,
                                                    LeadPageDataSourceResponse leadPageDataSourceResponse,
                                                    ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        try {
            // Extract form fields from the V4 structure using similar approach as LV3
            Map<String, FormFieldValue> formFieldValueMapToPrefill = getV4FormFieldValueMapToPrefill(groupedFormWidgetData);

            // Get user data for the fields using the same fetcher as LV3
            Map<String, Object> userData = this.formWidgetDataFetcher
                    .getDataForFields(formFieldValueMapToPrefill.keySet(), leadPageDataSourceResponse,
                            reviewUserDataSourceResponse, decrypter, locationRequestHandler);

            // Prefill form field values using the same utility as LV3
            this.formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);

            log.info("Successfully updated V4 form fields using LV3-style approach");
        } catch (Exception e) {
            log.error("Error updating form fields with user data using V3 style", e);
        }
    }

    /**
     * Extracts form field value map from V4 structure similar to LV3's FormWidgetDataJsonParser
     */
    private Map<String, FormFieldValue> getV4FormFieldValueMapToPrefill(GroupedFormWidgetData groupedFormWidgetData) {
        Map<String, FormFieldValue> formFieldValueMap = new HashMap<>();

        try {
            // Navigate to the form within the submit button's action params
            if (groupedFormWidgetData.getSubmitButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams() != null) {

                Map<String, Object> params = groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams();
                Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");

                if (pageResponse != null) {
                    extractFormFieldsFromPageResponse(pageResponse, formFieldValueMap);
                }
            }
        } catch (Exception e) {
            log.error("Error extracting form fields from V4 structure", e);
        }

        return formFieldValueMap;
    }

    /**
     * Extracts form fields from page response structure
     */
    private void extractFormFieldsFromPageResponse(Map<String, Object> pageResponse, Map<String, FormFieldValue> formFieldValueMap) {
        List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get("slots");

        if (slots != null) {
            for (Map<String, Object> slot : slots) {
                Map<String, Object> widget = (Map<String, Object>) slot.get("widget");
                if (widget != null) {
                    Map<String, Object> data = (Map<String, Object>) widget.get("data");
                    if (data != null) {
                        extractRenderableComponents(data, formFieldValueMap);
                    }
                }
            }
        }
    }

    /**
     * Extracts renderable components (form fields) from widget data
     */
    private void extractRenderableComponents(Map<String, Object> data, Map<String, FormFieldValue> formFieldValueMap) {
        try {
            List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) data.get("renderableComponents");

            if (renderableComponents != null && !renderableComponents.isEmpty()) {
                for (Map<String, Object> renderableComponent : renderableComponents) {
                    Map<String, Object> valueMap = (Map<String, Object>) renderableComponent.get("value");

                    if (valueMap != null && valueMap.get("name") != null) {
                        String fieldName = (String) valueMap.get("name");

                        // Convert Map to FormFieldValue object for compatibility with LV3 utilities
                        FormFieldValue formFieldValue = ObjectMapperUtil.get().convertValue(valueMap, FormFieldValue.class);
                        formFieldValueMap.put(fieldName, formFieldValue);

                        log.debug("Extracted form field: {} for prefilling", fieldName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting renderable components", e);
        }
    }



    /**
     * Updates tracking data with correct accountId and applicationId
     */
    private void updateTrackingData(GroupedFormWidgetData groupedFormWidgetData,
                                   ApplicationDataResponse applicationDataResponse) {
        try {
            // Update tracking data in the main submit button action
            if (groupedFormWidgetData.getSubmitButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams() != null) {

                Map<String, Object> params = groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams();

                // Update tracking data in the main action
                updateTrackingInParams(params, applicationDataResponse);

                // Update tracking data in pageResponse if it exists
                Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
                if (pageResponse != null) {
                    updateTrackingInPageResponse(pageResponse, applicationDataResponse);
                }
            }
        } catch (Exception e) {
            log.error("Error updating tracking data", e);
        }
    }

    /**
     * Updates tracking data in action params
     */
    private void updateTrackingInParams(Map<String, Object> params, ApplicationDataResponse applicationDataResponse) {
        // Update applicationId in params if it exists
        if (params.containsKey("applicationId")) {
            params.put("applicationId", applicationDataResponse.getApplicationId());
            log.debug("Updated applicationId in params: {}", applicationDataResponse.getApplicationId());
        }
    }

    /**
     * Updates tracking data in page response structure
     */
    private void updateTrackingInPageResponse(Map<String, Object> pageResponse, ApplicationDataResponse applicationDataResponse) {
        // Update tracking data in pageResponse.pageData.trackingContext.tracking
        Map<String, Object> pageData = (Map<String, Object>) pageResponse.get("pageData");
        if (pageData != null) {
            Map<String, Object> trackingContext = (Map<String, Object>) pageData.get("trackingContext");
            if (trackingContext != null) {
                Map<String, Object> tracking = (Map<String, Object>) trackingContext.get("tracking");
                if (tracking != null) {
                    // Update accountId and applicationId in tracking
                    tracking.put("accountId", applicationDataResponse.getExternalUserId());
                    tracking.put("applicationId", applicationDataResponse.getApplicationId());
                    log.info("Updated tracking data - accountId: {}, applicationId: {}",
                            applicationDataResponse.getExternalUserId(),
                            applicationDataResponse.getApplicationId());
                } else {
                    log.warn("Tracking object not found in trackingContext");
                }
            } else {
                log.warn("TrackingContext not found in pageData");
            }
        } else {
            log.warn("PageData not found in pageResponse");
        }
    }

    /**
     * Updates submit buttons in V4 structure which is different from V3
     * V4 has submit buttons nested within the CLIENT__INLINE_NAVIGATION action params
     */
    private void updateV4SubmitButton(GroupedFormWidgetData groupedFormWidgetData,
                                     ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        try {
            // Update the main submit button (top level)
            if (groupedFormWidgetData.getSubmitButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction() != null) {

                updateSubmitButtonAction(groupedFormWidgetData.getSubmitButton().getButton().getAction(),
                                       reviewUserDataSourceResponse);

                // Also update nested submit buttons within the page response
                updateNestedSubmitButtons(groupedFormWidgetData, reviewUserDataSourceResponse);
            }
        } catch (Exception e) {
            log.error("Error updating V4 submit button", e);
        }
    }

    /**
     * Updates submit button action with query params and encryption data
     * Merges query params with existing params to preserve pageResponse data
     */
    private void updateSubmitButtonAction(com.flipkart.rome.datatypes.response.common.Action action,
                                        ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (action != null) {
            // Merge query params with existing params instead of replacing
            Map<String, Object> existingParams = action.getParams();
            if (existingParams == null) {
                existingParams = new HashMap<>();
            }

            Map<String, Object> queryParams = reviewUserDataSourceResponse.getQueryParams();
            if (queryParams != null) {
                // Add query params to existing params without overwriting pageResponse
                existingParams.putAll(queryParams);
            }

            action.setParams(existingParams);
            action.setEncryption(reviewUserDataSourceResponse.getEncryptionData());
            log.debug("Updated submit button action with merged query params and encryption data");
        }
    }

    /**
     * Updates nested submit buttons within the page response structure
     */
    private void updateNestedSubmitButtons(GroupedFormWidgetData groupedFormWidgetData,
                                         ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        try {
            if (groupedFormWidgetData.getSubmitButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction() != null &&
                groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams() != null) {

                Map<String, Object> params = groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams();
                Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");

                if (pageResponse != null) {
                    updateSubmitButtonsInPageResponse(pageResponse, reviewUserDataSourceResponse);
                }
            }
        } catch (Exception e) {
            log.error("Error updating nested submit buttons", e);
        }
    }

    /**
     * Updates submit buttons found within the page response slots
     */
    private void updateSubmitButtonsInPageResponse(Map<String, Object> pageResponse,
                                                 ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get("slots");

        if (slots != null) {
            for (Map<String, Object> slot : slots) {
                Map<String, Object> widget = (Map<String, Object>) slot.get("widget");
                if (widget != null) {
                    Map<String, Object> data = (Map<String, Object>) widget.get("data");
                    if (data != null) {
                        // Check if this widget has a submit button
                        Map<String, Object> submitButton = (Map<String, Object>) data.get("submitButton");
                        if (submitButton != null) {
                            updateSubmitButtonInData(submitButton, reviewUserDataSourceResponse);
                        }
                    }
                }
            }
        }
    }

    /**
     * Updates a submit button found within widget data
     * Merges query params with existing params to preserve form data
     */
    private void updateSubmitButtonInData(Map<String, Object> submitButton,
                                        ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        try {
            Map<String, Object> button = (Map<String, Object>) submitButton.get("button");
            if (button != null) {
                Map<String, Object> action = (Map<String, Object>) button.get("action");
                if (action != null) {
                    // Merge query params with existing params instead of replacing
                    Map<String, Object> existingParams = (Map<String, Object>) action.get("params");
                    if (existingParams == null) {
                        existingParams = new HashMap<>();
                    }

                    Map<String, Object> queryParams = reviewUserDataSourceResponse.getQueryParams();
                    if (queryParams != null) {
                        // Add query params to existing params without overwriting form data
                        existingParams.putAll(queryParams);
                    }

                    action.put("params", existingParams);
                    action.put("encryption", reviewUserDataSourceResponse.getEncryptionData());
                    log.debug("Updated nested submit button with merged query params and encryption data");
                }
            }
        } catch (Exception e) {
            log.error("Error updating submit button in data", e);
        }
    }

}
