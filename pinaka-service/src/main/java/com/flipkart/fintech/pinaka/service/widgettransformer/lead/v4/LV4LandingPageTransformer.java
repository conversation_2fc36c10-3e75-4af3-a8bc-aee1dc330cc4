package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.lead.model.PaOffer;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ListFormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.supermoney.cards.PrimitiveCard;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.ListWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.text.StringSubstitutor;

import java.text.DecimalFormat;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.PA_OFFER;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.FIRST_NAME_STRING;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.PHONE_NUMBER_STRING;

/**
 * Transformer for Lead V4 Landing Page
 * Handles dynamic content based on user name and pre-approved offer availability
 * Supports 4 content scenarios: personalized with/without offer, generic with/without offer
 * Implements proper data prefilling similar to LV3NamePageFormTransformer
 */
@CustomLog
public class LV4LandingPageTransformer implements ListFormWidgetTransformer {

    private static final String LANDING_PAGE_WITH_NAME;
    private static final String LANDING_PAGE_WITHOUT_NAME;

    private static final long MIN_OFFER_AMOUNT = 1_00_000L; // Minimum offer amount to consider as valid
    private static final long MAX_OFFER_AMOUNT = 10_00_000L; // Maximum offer amount to consider as valid

    private static final Integer FNAME_ARRAY_INDEX = 0;
    private static final Integer AMOUNT_ARRAY_INDEX = 1;

    static {
        LANDING_PAGE_WITH_NAME = TransformerUtils.readFileasString("template/lead/V4/LandingPageSummaryListWithNameAndOffer.json");
        LANDING_PAGE_WITHOUT_NAME = TransformerUtils.readFileasString("template/lead/V4/LandingPageSummaryListWithNameAndOffer.json");
    }

    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final LocationRequestHandler locationRequestHandler;
    private final BqIngestionHelper bqIngestionHelper;
    private final FormConfig formConfig = new FormConfig("18", "90", PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);

    @Inject
    public LV4LandingPageTransformer(Decrypter decrypter, DynamicBucket dynamicBucket, FormWidgetDataFetcher formWidgetDataFetcher, LocationRequestHandler locationRequestHandler, BqIngestionHelper bqIngestionHelper) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.locationRequestHandler = locationRequestHandler;
        this.bqIngestionHelper = bqIngestionHelper;
    }

    private static String format(String pattern, Object value) {
        return new DecimalFormat(pattern).format(value);
    }

    private String formatNumber(double value) {
        String result;
        if (value < 1000) {
            result = format(Constants.UNDER_THOUSAND_INTEGER_PATTERN, value);
        } else {
            double hundreds = value % 1000;
            int other = (int) (value / 1000);
            result = format(",##", other) + ',' + format(Constants.OVER_THOUSAND_INTEGER_FLOW, hundreds);
        }
        return result;
    }

    @Override
    public CardSummaryListWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        CardSummaryListWidgetData listWidgetData;
        String pageState = "LV4_LANDING_PAGE";
        try {
            // Get data sources - exactly like LV3
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();

            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);

            // Derive content scenario from PA offer and user name - don't store it anywhere
            String templateToUse = deriveTemplate(applicationDataResponse, leadPageDataSourceResponse);

            // Use the same approach as LV3 - simple and straightforward
            listWidgetData = ObjectMapperUtil.get().readValue(getFormJson(templateToUse, applicationDataResponse), CardSummaryListWidgetData.class);

            // Get user data for prefilling
            Set<String> fieldsToFetch = new HashSet<>();
            fieldsToFetch.add(FIRST_NAME_STRING);
            fieldsToFetch.add(PHONE_NUMBER_STRING);
            Map<String, Object> userData = this.formWidgetDataFetcher.getDataForFields(fieldsToFetch, leadPageDataSourceResponse, reviewUserDataSourceResponse, decrypter, locationRequestHandler);

            // Customize the template based on content scenario and user data
            customizeTemplateWithUserData(listWidgetData, userData, applicationDataResponse);

            // Log BQ events
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), "V4"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V4"));
            throw new PinakaException("Error while building widget Group Data for LV4 Landing Page for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        // Cast ListWidgetData to GroupedFormWidgetData since they share the same interface
        return listWidgetData;
    }

    private String deriveTemplate(ApplicationDataResponse applicationDataResponse, LeadPageDataSourceResponse leadPageDataSourceResponse) {
        try {
            // Check if user has a name from profile
            boolean hasName = false;
            if (leadPageDataSourceResponse != null && leadPageDataSourceResponse.getProfile() != null) {
                String firstName = leadPageDataSourceResponse.getProfile().getFirstName();
                hasName = (firstName != null && !firstName.trim().isEmpty());
            }

            // Determine scenario based on name and offer availability
            if (hasName) {
                return LANDING_PAGE_WITH_NAME;
            } else {
                return LANDING_PAGE_WITHOUT_NAME;
            }
        } catch (Exception e) {
            log.error("Error deriving content scenario for user: {}", applicationDataResponse.getSmUserId(), e);
            // Return default scenario on error
            return LANDING_PAGE_WITHOUT_NAME;
        }
    }

    /**
     * Customize template with actual user data and scenario-specific content
     * This method updates the JSON structure directly with prefilled data
     */
    private void customizeTemplateWithUserData(ListWidgetData<PrimitiveCard> listWidgetData, Map<String, Object> userData, ApplicationDataResponse applicationDataResponse) {
        try {
            // Update user name in the template if available
            String firstName = (String) userData.get(FIRST_NAME_STRING);
            if (firstName != null && !firstName.trim().isEmpty()) {
                updateUserNameInTemplate(listWidgetData, firstName);
            }

            // Update offer amount if available
            updateOfferAmountInTemplate(listWidgetData, applicationDataResponse);

            log.info("Successfully customized template for user: {}", applicationDataResponse.getSmUserId());
        } catch (Exception e) {
            log.error("Error customizing template for user: {}", applicationDataResponse.getSmUserId(), e);
            // Don't throw exception, just log and continue with default template
        }
    }

    /**
     * Update user name in the template structure
     * For now, just log the user name - template modification will be implemented later
     */
    private void updateUserNameInTemplate(ListWidgetData<PrimitiveCard> listWidgetData, String firstName) {
        String formattedName = formatUserName(firstName);
        log.info("User name to display in template: {}", formattedName);
        if (listWidgetData.getRenderableComponents() != null && !listWidgetData.getRenderableComponents().isEmpty() && listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue() != null && listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle().getValue() != null) {
            listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle().getValue().setText("Congrats " + firstName);
        }
    }

    private void updateOfferAmountInTemplate(ListWidgetData<PrimitiveCard> listWidgetData, ApplicationDataResponse applicationDataResponse) {
        try {
            // Get PA offer from application data
            PaOffer paOffer = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get(PA_OFFER), PaOffer.class);
            boolean appendUpto = true;
            long amount;
            if (paOffer == null || paOffer.getAmount() == null) {
                log.debug("PA offer is null or amount is not set, using minimum offer amount for userId {}", applicationDataResponse.getSmUserId());
                amount = MAX_OFFER_AMOUNT;
            } else if (paOffer.getAmount() < MIN_OFFER_AMOUNT) {
                log.debug("PA offer amount {} is less than minimum threshold, setting to minimum offer amount for userId {}", paOffer.getAmount(), applicationDataResponse.getSmUserId());
                amount = MIN_OFFER_AMOUNT;
            } else {
                appendUpto = false;
                amount = paOffer.getAmount();
            }
            String formattedAmount = "₹" + formatNumber(amount) + "*";
            log.info("PA offer amount to display in template: {}", formattedAmount);

            if (listWidgetData.getRenderableComponents() != null &&
                    listWidgetData.getRenderableComponents().size() > AMOUNT_ARRAY_INDEX &&
                    listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue() != null &&
                    listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue() != null) {
                listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue().setText(formattedAmount);
            }
            if (listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue() != null &&
                    listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription() != null &&
                    listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription().getValue() != null) {
                String subtitleText = listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription().getValue().getText();
                if (!appendUpto && subtitleText != null) {
                    subtitleText = subtitleText.replace(" upto", "");
                }
                listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription().getValue().setText(subtitleText);
            }

        } catch (Exception e) {
            log.error("Error processing offer amount for template", e);
        }
    }

    /**
     * Format user name for display in the template
     * Adds line break for better visual presentation
     */
    private String formatUserName(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return "Valued Customer";
        }

        String[] nameParts = userName.trim().split("\\s+");
        if (nameParts.length >= 2) {
            // If we have at least 2 parts, put first name on first line, rest on second line
            String firstName = nameParts[0];
            StringBuilder restOfName = new StringBuilder();
            for (int i = 1; i < nameParts.length; i++) {
                if (i > 1) restOfName.append(" ");
                restOfName.append(nameParts[i]);
            }
            return firstName + "\n " + restOfName;
        } else {
            // Single name, just return as is
            return userName.trim();
        }
    }

    /**
     * Get form configuration for template substitution
     * Uses the same FormConfig as LV3 for consistency
     */
    private String getFormJson(String template, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMap(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }

    /**
     * Banner transformer for Lead V4 Landing Page
     */
    public static class LeadV4LandingPageBannerTransformer implements BannerWidgetTransformer {

        private static final String LANDING_PAGE_BANNER;

        static {
            LANDING_PAGE_BANNER = TransformerUtils.readFileasString("template/lead/V4/LandingPageBanner.json");
        }

        private final DynamicBucket dynamicBucket;

        public LeadV4LandingPageBannerTransformer(DynamicBucket dynamicBucket) {
            this.dynamicBucket = dynamicBucket;
        }

        @Override
        public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
            // Use carousel template for banner (lender logos)
/*
                BannerWidgetData bannerWidgetData = ObjectMapperUtil.get()
                        .readValue(LANDING_PAGE_CAROUSEL_TEMPLATE, BannerWidgetData.class);
*/

            // The carousel template already contains lender logos
            // No additional customization needed for now

            return new BannerWidgetData();
        }
    }
}
