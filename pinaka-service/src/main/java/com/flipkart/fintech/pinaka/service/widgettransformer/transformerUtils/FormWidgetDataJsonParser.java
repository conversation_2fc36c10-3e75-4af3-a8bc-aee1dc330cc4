package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichButtonValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupDataValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@CustomLog
public class FormWidgetDataJsonParser {

    public Map<String, FormFieldValue> getFormFieldValueMapToPrefill(List<FormGroupValue> formGroups) {
        Map<String, FormFieldValue> fieldFormFieldValueMap = new HashMap<>();
        for (FormGroupValue formGroupValue : Objects.requireNonNull(formGroups)) {
            if (formGroupValue.getFormGroupHeader() != null) {
                RenderableComponent<RichButtonValue> next = formGroupValue.getFormGroupHeader().getNext();
                if (next != null && next.getAction() != null) {
                    Map<String, Object> params = next.getAction().getParams();
                    Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
                    List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get("slots");
                    for (Map<String, Object> slot : slots) {
                        // Ignoring the banner
                        if(((String)slot.get("elementId")).contains("BANNER")){
                            continue;
                        }
                        Map<String, Object> widget = (Map<String, Object>) slot.get("widget");
                        Map<String, Object> data = (Map<String, Object>) widget.get("data");
                        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().convertValue(data, GenericFormWidgetData.class);
                        if (genericFormWidgetData.getRenderableComponents() != null && !genericFormWidgetData.getRenderableComponents().isEmpty()) {
                            for (RenderableComponent<FormFieldValue> renderableComponent : genericFormWidgetData.getRenderableComponents()) {
                                FormFieldValue value = renderableComponent.getValue();
                                fieldFormFieldValueMap.put(value.getName(), value);
                            }
                        }
                    }
                }
            }
        }
        return fieldFormFieldValueMap;
    }

    public void updateFormFieldValueMapToPrefill(List<FormGroupValue> formGroups, Map<String, FormFieldValue> formFieldValueMap, Map<String, SubmitButtonValue> submitButtonValueMap) {
        for (FormGroupValue formGroupValue : Objects.requireNonNull(formGroups)) {
            if (formGroupValue.getFormGroupHeader() != null) {
                RenderableComponent<RichButtonValue> next = formGroupValue.getFormGroupHeader().getNext();
                if (next != null && next.getAction() != null) {
                    Map<String, Object> params = next.getAction().getParams();
                    Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
                    List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get("slots");
                    for (Map<String, Object> slot : slots) {
                        // Ignoring the banner
                        if(((String)slot.get("elementId")).contains("BANNER")) {
                            continue;
                        }
                        Map<String, Object> widget = (Map<String, Object>) slot.get("widget");
                        Map<String, Object> data = (Map<String, Object>) widget.get("data");
                        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().convertValue(data, GenericFormWidgetData.class);
                        if (genericFormWidgetData.getRenderableComponents() != null && !genericFormWidgetData.getRenderableComponents().isEmpty()) {
                            for (RenderableComponent<FormFieldValue> renderableComponent : genericFormWidgetData.getRenderableComponents()) {
                                FormFieldValue value = renderableComponent.getValue();
                                renderableComponent.setValue(formFieldValueMap.get(value.getName()));
                            }
                            genericFormWidgetData.setSubmitButton(submitButtonValueMap.get(genericFormWidgetData.getFormId()));
                        }
                        Map<String, Object> updatedData = ObjectMapperUtil.get().convertValue(genericFormWidgetData, Map.class);
                        widget.put("data", updatedData);
                        slot.put("widget", widget);
                    }
                    pageResponse.put("slots", slots);
                    params.put("pageResponse", pageResponse);
                    next.getAction().setParams(params);
                }
            }
        }
    }

    public void updateFormFieldValueMapToPrefill(Map<String, FormFieldValue> formFieldValueMap, List<RenderableComponent<FormFieldValue>> renderableComponents) {
        if (renderableComponents != null && !renderableComponents.isEmpty()) {
            for (RenderableComponent<FormFieldValue> renderableComponent : renderableComponents) {
                FormFieldValue value = renderableComponent.getValue();
                renderableComponent.setValue(formFieldValueMap.get(value.getName()));
            }
        }
    }

    public Map<String, SubmitButtonValue> getFormFieldSubmitButtons(List<FormGroupValue> formGroups) {
        Map<String, SubmitButtonValue> submitButtonValueMap = new HashMap<>();
        for (FormGroupValue formGroupValue : Objects.requireNonNull(formGroups)) {
            if (formGroupValue.getFormGroupHeader() != null) {
                RenderableComponent<RichButtonValue> next = formGroupValue.getFormGroupHeader().getNext();
                if (next != null && next.getAction() != null) {
                    Map<String, Object> params = next.getAction().getParams();
                    Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
                    List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get("slots");
                    for (Map<String, Object> slot : slots) {
                        // Ignoring the banner
                        if(((String)slot.get("elementId")).contains("BANNER")){
                            continue;
                        }
                        Map<String, Object> widget = (Map<String, Object>) slot.get("widget");
                        Map<String, Object> data = (Map<String, Object>) widget.get("data");
                        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().convertValue(data, GenericFormWidgetData.class);
                        submitButtonValueMap.put(genericFormWidgetData.getFormId(), genericFormWidgetData.getSubmitButton());
                    }
                }
            }
        }
        return submitButtonValueMap;
    }

    public Map<String, FormGroupDataValue> getGroupFieldValueMapToPrefill(List<FormGroupValue> formGroups) {
        Map<String, FormGroupDataValue> formgroupFieldValueMap = new HashMap<>();
        for (FormGroupValue formGroupValue : Objects.requireNonNull(formGroups)) {
            if (formGroupValue.getFormGroupData() != null) {
                for (FormGroupDataValue formGroupDatum : formGroupValue.getFormGroupData()) {
                    if (formGroupDatum.getData() != null) {
                        String fieldName = formGroupDatum.getData().getName();
                        formgroupFieldValueMap.put(fieldName, formGroupDatum);
                    }
                }
            }
        }
        return formgroupFieldValueMap;
    }

    public Map<String, FormFieldValue> getFormFieldValueMapToPrefillForRenderableComponents(List<RenderableComponent<FormFieldValue>> renderableComponents) {
        Map<String, FormFieldValue> fieldFormFieldValueMap = new HashMap<>();
        if (renderableComponents != null && !renderableComponents.isEmpty()) {
            for (RenderableComponent<FormFieldValue> renderableComponent : renderableComponents) {
                FormFieldValue value = renderableComponent.getValue();
                fieldFormFieldValueMap.put(value.getName(), value);
            }
        }
        return fieldFormFieldValueMap;
    }
}
