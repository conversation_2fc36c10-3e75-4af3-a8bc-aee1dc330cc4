package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.response.OtpVerificationPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.OtpCustomFormFieldData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;


import lombok.CustomLog;

@CustomLog
public class OtpVerificationFormTransformer {

    private OtpVerificationPageDataSourceResponse otpVerificationPageDataSourceResponse;
    private static String genericFormjson;

    static {
        genericFormjson = TransformerUtils.readFileasString("template/idfc/OTPVerification.json");
    }

    public GenericFormWidgetData buildWidgetData(OtpVerificationPageDataSourceResponse pageDataSourceReponse)
            throws PinakaClientException {
        try {
            GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get().readValue(genericFormjson, GenericFormWidgetData.class);
            CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) genericFormWidgetDataCopy.getRenderableComponents()
                    .get(0).getValue();
            OtpCustomFormFieldData otpCustomFormFieldData  = (OtpCustomFormFieldData) customFormFieldValue.getCustomFieldData();
            otpVerificationPageDataSourceResponse = pageDataSourceReponse;
            updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());
            updateResendAction(otpCustomFormFieldData.getResendText().getAction());
            return genericFormWidgetDataCopy;
        } catch (Exception e) {
            log.error("Basic Details build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

    private void updateResendAction(Action action) {
        action.setParams(otpVerificationPageDataSourceResponse.getResendOtpActionParams());
        action.setEncryption(otpVerificationPageDataSourceResponse.getEncryptionData());
    }

    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(otpVerificationPageDataSourceResponse.getQueryParams());
        action.setEncryption(otpVerificationPageDataSourceResponse.getEncryptionData());
    }

}