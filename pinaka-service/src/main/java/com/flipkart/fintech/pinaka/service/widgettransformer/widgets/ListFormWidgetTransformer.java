package com.flipkart.fintech.pinaka.service.widgettransformer.widgets;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;

public interface ListFormWidgetTransformer {
    CardSummaryListWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException;
}
