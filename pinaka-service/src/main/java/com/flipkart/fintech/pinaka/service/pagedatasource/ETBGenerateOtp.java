package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.ETBGenerateOtpResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.PhoneNumberValidator;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.google.i18n.phonenumbers.NumberParseException;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.CustomLog;
import org.apache.http.NameValuePair;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

@CustomLog
public class ETBGenerateOtp implements PageDataSource<ETBGenerateOtpResponse>{

    @Inject
    private static ConfigUtils configUtils;

    @Override
    public ETBGenerateOtpResponse getData(ApplicationDataResponse applicationDataResponse) {
        ETBGenerateOtpResponse etbGenerateOtpResponse = new ETBGenerateOtpResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        etbGenerateOtpResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        etbGenerateOtpResponse.setEncryptionData(encryption.orElse(null));
        etbGenerateOtpResponse.setPhoneNumberList(getPhoneNumberList(applicationDataResponse));
        return etbGenerateOtpResponse;
    }

    private List<String> getPhoneNumberList(ApplicationDataResponse applicationDataResponse) {
        List<String>phoneNumberList = new ArrayList<>();
        if(applicationDataResponse.getApplicationData().containsKey("pollOffer")){
            if(((LinkedHashMap)applicationDataResponse.getApplicationData().get("pollOffer")).containsKey("phoneNumbers")){
               LinkedHashMap<String,Object> phoneNumbers =  ((LinkedHashMap)(((LinkedHashMap<?, ?>) applicationDataResponse.getApplicationData().get("pollOffer")).get("phoneNumbers")));
                for (int i = 1; i <= 3; i++) {
                    String key = "phoneNumber" + i;
                    if (phoneNumbers.containsKey(key)) {
                        String phoneNumber = (String) phoneNumbers.get(key);
                        try {
                            if (PhoneNumberValidator.validPhoneNumber(phoneNumber)) {
                                phoneNumberList.add(phoneNumber);
                            }
                        } catch (NumberParseException e) {
                            log.info("Invalid phone number for etb flow:{}",e.getMessage());
                        }
                    }
                }
            }
        }
        return phoneNumberList;
    }

}
