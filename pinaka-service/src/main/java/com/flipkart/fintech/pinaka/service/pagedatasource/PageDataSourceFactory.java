package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.service.response.BasicDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.widgettransformer.ApplicationStatusWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.BasicDetailsFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.landingPages.OfferDetailsLpTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage2FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LandingPageTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.AnnouncementCardWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ApprovedOfferWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;

import javax.inject.Inject;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 04/01/24
 */
@CustomLog
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class PageDataSourceFactory {

    private final OfferDetailsLpTransformer offerDetailsLpTransformer;

    public BannerWidgetTransformer getBannerWidgetTransformer(ApplicationDataResponse applicationDataResponse, String interactionKey, DynamicBucket dynamicBucket) {
        log.info(applicationDataResponse.getApplicationData().toString());
        log.info(interactionKey);
        if ("BASIC_DETAILS".equals(interactionKey)) {
            return new BasicDetailsFormTransformer(new BasicDetailsPageDataSourceResponse(), new HashMap<>(), dynamicBucket);
        }
        if ("NAME_PAGE".equals(interactionKey)) {
            return new NamePageFormTransformer(new LeadPageDataSourceResponse(), new HashMap<>(), null, dynamicBucket);
        }
        if ("LEAD_V4_LANDING_PAGE".equalsIgnoreCase(interactionKey)) {
            return new LV4LandingPageTransformer.LeadV4LandingPageBannerTransformer(dynamicBucket);
        } else if ("LEAD_V3_NAME_PAGE".equalsIgnoreCase(interactionKey) || "LEAD_V4_NAME_PAGE".equalsIgnoreCase(interactionKey)) {
            return new LV3NamePageFormTransformer.LV3NamePageBannerFormTransformer(dynamicBucket);
        } else if ("LEAD_V3_PAGE_1".equalsIgnoreCase(interactionKey) || "LEAD_V4_PAGE_1".equalsIgnoreCase(interactionKey)) {
            return new LV3ReviewPage1FormTransformer.LV3ReviewPageBannerFormTransformer();
        } else if ("LEAD_V3_PAGE_2".equalsIgnoreCase(interactionKey) || "LEAD_V4_PAGE_2".equalsIgnoreCase(interactionKey)) {
            return new LV3ReviewPage2FormTransformer.WorkDetailsBannerFormTransformer();
        }
        return new ApplicationStatusWidgetTransformer(new ApplicationStatusPageDataSource().getData(applicationDataResponse));
    }

    public AnnouncementCardWidgetTransformer getAnnouncementCardWidgetTransformer(ApplicationDataResponse applicationDataResponse, SlotInfo slotInfo) {
        return offerDetailsLpTransformer;
    }

    public ApprovedOfferWidgetTransformer getApprovedOfferWidgetTransformer(ApplicationDataResponse applicationDataResponse, SlotInfo slotInfo) {
        return offerDetailsLpTransformer;
    }
}
