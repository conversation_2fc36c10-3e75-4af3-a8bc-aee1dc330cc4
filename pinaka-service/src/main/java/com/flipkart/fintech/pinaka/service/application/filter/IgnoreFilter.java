package com.flipkart.fintech.pinaka.service.application.filter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 20/11/17.
 */
@Inherited
@Target({ElementType.TYPE})
@Retention(value = RetentionPolicy.RUNTIME)
public @interface IgnoreFilter {
    String value() default FilterConstants.ALL;
}
