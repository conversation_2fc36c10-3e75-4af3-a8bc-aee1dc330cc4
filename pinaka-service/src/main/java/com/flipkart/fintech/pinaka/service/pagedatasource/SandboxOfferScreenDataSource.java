package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.service.response.SandboxOfferScreenResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class SandboxOfferScreenDataSource implements PageDataSource<SandboxOfferScreenResponse> {

  @Inject
  private static ConfigUtils configUtils;

  @Override
  public SandboxOfferScreenResponse getData(ApplicationDataResponse applicationDataResponse) {
    GetOfferResponse sandboxOfferResponse = ObjectMapperUtil.get()
        .convertValue(applicationDataResponse.getApplicationData().get("getOffer"),
            GetOfferResponse.class);
    SandboxOfferScreenResponse offerScreenPageDataSourceResponse = new SandboxOfferScreenResponse();
    offerScreenPageDataSourceResponse.setLender(Lender.valueOf((String)applicationDataResponse.getApplicationData().get("financial_provider")));
    offerScreenPageDataSourceResponse.setGetOfferResponse(sandboxOfferResponse);
    List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
    offerScreenPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
//    Optional<EncryptionData> encryption = configUtils.getEncryptionData();
//    offerScreenPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
    return offerScreenPageDataSourceResponse;
  }
}