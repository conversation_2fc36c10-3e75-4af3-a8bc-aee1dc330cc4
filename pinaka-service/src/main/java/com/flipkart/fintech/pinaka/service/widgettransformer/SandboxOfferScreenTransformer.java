package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.Offer;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.OfferEntity;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.service.response.SandboxOfferScreenResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.OfferScreenUtils;
import com.flipkart.fintech.profile.common.DynamicConfigHelper;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.request.fintech.calm.Tenure;
import com.flipkart.rome.datatypes.request.fintech.calm.enums.Charge;
import com.flipkart.rome.datatypes.request.fintech.calm.enums.TenureUnit;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.calm.Charges;
import com.flipkart.rome.datatypes.response.fintech.calm.RangeFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.calm.enums.RateType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.AmountRangeOffer;
import com.flipkart.rome.datatypes.response.fintech.supermoney.InterestData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.RepaymentDetails;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.CalculatorType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.BankOfferFormFieldV2Data;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.guidedNav.RichText;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import org.apache.maven.shared.utils.StringUtils;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

public class SandboxOfferScreenTransformer {

  private final String formJsonString;
  private final DynamicBucket dynamicBucket;

  @Inject
  public SandboxOfferScreenTransformer(@Named("sandboxOfferScreen") String formJsonString, DynamicBucket dynamicBucket) {
    this.formJsonString = formJsonString;
    this.dynamicBucket = dynamicBucket;
  }

  public GenericFormWidgetData buildWidgetData(SandboxOfferScreenResponse pageDataSourceResponse) throws JsonProcessingException {
    GenericFormWidgetData genericFormWidgetData = getGenericFormWidgetData();
    GetOfferResponse offerResponse = pageDataSourceResponse.getGetOfferResponse();
    CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) genericFormWidgetData.getRenderableComponents().get(0).getValue();
    BankOfferFormFieldV2Data bankOfferFormFieldData  = (BankOfferFormFieldV2Data) customFormFieldValue.getCustomFieldData();
    updateAmountSliderDetails(bankOfferFormFieldData, offerResponse);
    updateAmountRangeOffer(bankOfferFormFieldData, offerResponse, pageDataSourceResponse.getLender());
    updateAccountAggregatorButton(bankOfferFormFieldData, pageDataSourceResponse);
    updateSubmitButton(genericFormWidgetData.getSubmitButton(), pageDataSourceResponse);
    return genericFormWidgetData;
  }

  private void updateAccountAggregatorButton(BankOfferFormFieldV2Data bankOfferFormFieldData, SandboxOfferScreenResponse pageDataSourceResponse) {
    if(pageDataSourceResponse.getGetOfferResponse().getJourneyState().getAdditionalDataModesAvailable() != null && pageDataSourceResponse.getGetOfferResponse().getJourneyState().getAdditionalDataModesAvailable().contains("AA")) {
      Action action = bankOfferFormFieldData.getAccountAggregatorAction().getAction();
      Map<String, Object> queryParams = pageDataSourceResponse.getQueryParams();
      Map<String, Object> accountAggregatorForm = getAccountAggregatorForm();
      queryParams.put("formData", accountAggregatorForm);
      action.setParams(queryParams);
    }
    else {
      bankOfferFormFieldData.setAccountAggregatorAction(null);
    }

  }

  private Map<String, Object> getAccountAggregatorForm() {
    HashMap<String, Object> accountAggregatorParams = new HashMap<>();
    accountAggregatorParams.put("accountAggregator", true);
    return accountAggregatorParams;
  }

  private void updateAmountRangeOffer(BankOfferFormFieldV2Data bankOfferFormFieldData, GetOfferResponse offerResponse, Lender lender) {
    List<AmountRangeOffer> amountRangeOffers = offerResponse
            .getGeneratedOffer()
            .getOffer()
            .getOfferTable()
            .stream()
            .map(offerEntity ->  getAmountRangeOffer(offerEntity, offerResponse.getGeneratedOffer().getOffer(), lender))
            .collect(Collectors.toList());

    bankOfferFormFieldData.setAmountRangeOffers(amountRangeOffers);
  }


  private RichText getNameField(Charge charge) {
    RichText name = new RichText();
    name.setText(StringUtils.capitalise(charge.toString().toLowerCase().replace("_", " ")));
    return name;
  }

  private AmountRangeOffer getAmountRangeOffer(OfferEntity offerEntity, Offer offer, Lender lender) {
    AmountRangeOffer amountRangeOffer = new AmountRangeOffer();
    amountRangeOffer.setMin(offerEntity.getSanctionedAmount().getMin().intValue());
    amountRangeOffer.setMax(offerEntity.getSanctionedAmount().getMax().intValue());
    InterestData interestData = new InterestData();
    interestData.setValue(offerEntity.getRoi());
    amountRangeOffer.setInterest(interestData);
    setTenure(offerEntity, amountRangeOffer);
    setStartAndEndDate(offerEntity, amountRangeOffer, offer);
    List<Charges> charges = new ArrayList<>();
    amountRangeOffer.setCharges(charges);
    CalculatorType calculatorType = CalculatorType.DEDUCTED_CHARGES;
    if(offerEntity.getPf() != null){
      setLoanCharge(offerEntity, amountRangeOffer, Charge.PROCESSING_FEE, calculatorType);
    }
    if(offerEntity.getStampDuty() != null){
      setLoanCharge(offerEntity, amountRangeOffer, Charge.STAMP_DUTY, calculatorType);
    }
    return amountRangeOffer;
  }

  private void setLoanCharge(OfferEntity offerEntity, AmountRangeOffer amountRangeOffer, Charge charge, CalculatorType calculatorType) {
    Charges loanCharge = new Charges();
    loanCharge.setChargeType(charge);
    loanCharge.setName(getNameField(charge));
    loanCharge.setCalculatorType(calculatorType);
    if(charge.equals(Charge.PROCESSING_FEE) && Objects.nonNull(offerEntity.getPf())){
      loanCharge.setType(RateType.valueOf(offerEntity.getPf().getType().name()));
      loanCharge.setValue(offerEntity.getPf().getValue());
      loanCharge.setGst(offerEntity.getPf().getGst());
    } else if(charge.equals(Charge.STAMP_DUTY) && Objects.nonNull(offerEntity.getStampDuty())){
      loanCharge.setType(RateType.valueOf(offerEntity.getStampDuty().getType().name()));
      loanCharge.setValue(offerEntity.getStampDuty().getValue());
      loanCharge.setGst(offerEntity.getStampDuty().getGst());
    }
    amountRangeOffer.getCharges().add(loanCharge);
  }

  private void setStartAndEndDate(OfferEntity offerEntity, AmountRangeOffer amountRangeOffer, Offer offer) {
    LocalDate scheduleStartDate = getScheduledStartDate(offer);
    LocalDate scheduleEndDate = scheduleStartDate.plusMonths(offerEntity.getTenure().getValue());
    amountRangeOffer.getRepaymentDetails().setStartDate(scheduleStartDate.toString());
    amountRangeOffer.getRepaymentDetails().setEndDate(scheduleEndDate.toString());
  }

  private LocalDate getScheduledStartDate(Offer offer){
    if(offer.getEmiFirstDate()!= null){
      return offer.getEmiFirstDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    return OfferScreenUtils.getScheduleStartDate().plusDays(4);
  }

  private void setTenure(OfferEntity offerEntity, AmountRangeOffer amountRangeOffer) {
    Tenure tenure = new Tenure();
    tenure.setUnit(TenureUnit.valueOf(offerEntity.getTenure().getUnit().name()));
    tenure.setValue(offerEntity.getTenure().getValue());
    RepaymentDetails repaymentDetails = new RepaymentDetails();
    repaymentDetails.setTenure(tenure);
    amountRangeOffer.setRepaymentDetails(repaymentDetails);
  }

  private void updateAmountSliderDetails(BankOfferFormFieldV2Data bankOfferFormFieldData, GetOfferResponse offerResponse) {
    RangeFormFieldValue amountSliderValue = bankOfferFormFieldData.getAmountSlider().getValue();
    amountSliderValue.setDefaultValue(getMaxEligibleAmount(offerResponse));
    amountSliderValue.setMaxValue(getMaxEligibleAmount(offerResponse));
    amountSliderValue.setMinValue(getMinEligibleAmount(offerResponse));
    amountSliderValue.getStepperValue().setStepSize(offerResponse.getGeneratedOffer().getOffer().getStepper().intValue());
  }

  private long getMinEligibleAmount(GetOfferResponse offerResponse) {
    List<OfferEntity> offerTable = offerResponse.getGeneratedOffer()
            .getOffer().getOfferTable();
    return offerTable
            .stream()
            .map(offerEntity -> offerEntity.getSanctionedAmount().getMin().longValue())
            .min(Long::compareTo)
            .get();
  }

  private long getMaxEligibleAmount(GetOfferResponse offerResponse) {
    return offerResponse.getGeneratedOffer().getOffer().getMaxSanctionedAmount().longValue();
  }

  private GenericFormWidgetData getGenericFormWidgetData() throws JsonProcessingException {
    return ObjectMapperUtil.get().readValue(formJsonString, GenericFormWidgetData.class);
  }

  private void updateSubmitButton(SubmitButtonValue submitButton, SandboxOfferScreenResponse pageDataSourceResponse) {
    Action action = submitButton.getButton().getAction();
    action.setParams(pageDataSourceResponse.getQueryParams());
    action.setEncryption(pageDataSourceResponse.getEncryptionData());
    Lender lender = pageDataSourceResponse.getLender();
    if(lender!=null)
    {
      String lenderImageUrl = getLenderImage(lender);

      if (lenderImageUrl != null && !lenderImageUrl.isEmpty()) {
        submitButton.getLenderLogo().getValue().setDynamicImageUrl(lenderImageUrl);
      }
    }
  }

  private String getLenderImage(Lender lender) {

    switch (lender){
      case KISSHT:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.kissht.logo", null);
      case MONEYVIEW:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.moneyview.logo", null);
      case MONEYVIEWV2:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.moneyview.logo", null);
//      case CITI:
//        return DynamicConfigHelper.getString(dynamicBucket, "idfc.lender.image.url", "202403");
//      case INDIA_BULLS:
//        return DynamicConfigHelper.getString(dynamicBucket, "idfc.lender.image.url", "202403");
      case AXIS:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.axis.logo", null);
      case IDFC:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.idfc.logo", null);
      case FIBE:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.fibe.logo", null);
      case FIBEV2:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.fibe.logo", null);
      case MONEYVIEWOPENMKT:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.moneyview.logo", null);
      case OMNI:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.omni.logo", null);
      case OMNIV2:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.omni.logo", null);
      case DMI:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.dmi.logo", null);
      case SMARTCOIN:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.smartcoin.logo", null);
      case FINNABLE:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.finnable.logo", null);
      case RING:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.kissht.logo", null);
      case PFL:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.pfl.logo", null);
      case ABFL:
        return DynamicConfigHelper.getString(dynamicBucket, "lender.abfl.logo", null);
      default:
        break;
    }
    return "";
  }
}
