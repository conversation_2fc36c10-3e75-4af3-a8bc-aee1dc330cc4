package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.response.WorkDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.calm.AutoSuggestFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.insurtech.PriceTextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import entitymanager.client.shade.org.apache.commons.text.StringSubstitutor;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pinaka.service.constants.PageConstant.PL_ADDITIONAL_DETAILS_FORM.industryTypeOptions;
import static com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType.PLAIN_TEXT;

@CustomLog
public class WorkDetailsFormTransformer{
    private static String salariedFormJson;
    private static String selfEmployedFormJson;
    private static String selfEmployed = "SelfEmployed";
    private static String salaried = "Salaried";

    private final WorkDetailsPageDataSourceResponse workDetailsPageDataSourceResponse;

    private final DynamicBucket dynamicBucket;

    //todo:move file name to constants
    static {
        salariedFormJson = TransformerUtils.readFileasString("template/sandbox/SalariedDetails.json");
        selfEmployedFormJson = TransformerUtils.readFileasString("template/sandbox/SelfEmployedDetails.json");
    }

    public WorkDetailsFormTransformer(WorkDetailsPageDataSourceResponse workDetailsPageDataSourceResponse, DynamicBucket dynamicBucket) {
        this.workDetailsPageDataSourceResponse = workDetailsPageDataSourceResponse;
        this.dynamicBucket = dynamicBucket;
    }

    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        try {
            Map<String, String> basicDetails = (Map<String, String>) applicationDataResponse.getApplicationData().get("basicDetails");
            String employmentType = basicDetails.get("employmentType");
            String monthlyIncome = "";
            if (Objects.nonNull(basicDetails)){
                monthlyIncome = basicDetails.getOrDefault("income", "");
            }
            GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get()
                    .readValue(getLenderForm(applicationDataResponse.getMerchantId(), employmentType), GenericFormWidgetData.class);
            PriceTextBoxFormFieldValueV0 PriceTextBoxFormFieldValueV0 = getPriceTextBoxFormFieldValueV0(genericFormWidgetDataCopy);
            updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());
            if (Objects.nonNull(PriceTextBoxFormFieldValueV0)) {
                updateIncome(PriceTextBoxFormFieldValueV0, monthlyIncome);
            }
            if (selfEmployed.equals(employmentType)) {
                populateDropdown(genericFormWidgetDataCopy);
            }
            return genericFormWidgetDataCopy;
        } catch (Exception e) {
            log.error("Work Details build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

    private void populateDropdown(GenericFormWidgetData genericFormWidgetDataCopy) {
        AutoSuggestFormFieldValue industryField = (AutoSuggestFormFieldValue) genericFormWidgetDataCopy.
                getRenderableComponents().get(0).getValue();
        if (!Objects.isNull(industryField)) {
            industryField.setData(getDropdownData());
        }
    }

    private PriceTextBoxFormFieldValueV0 getPriceTextBoxFormFieldValueV0(GenericFormWidgetData genericFormWidgetDataCopy) {
        List<RenderableComponent<FormFieldValue>> renderableComponents = genericFormWidgetDataCopy.getRenderableComponents();
        Optional<RenderableComponent<FormFieldValue>> formFieldValueRenderableComponent = renderableComponents.stream().
                filter(incomeField -> (incomeField.getValue().getName()).equals("income")).
                findAny();
        if (formFieldValueRenderableComponent.isPresent()) {
            return (PriceTextBoxFormFieldValueV0) formFieldValueRenderableComponent.get().getValue();
        }
        return null;
    }

    public List<DropdownValue> getDropdownData() {
        return industryTypeOptions.stream().map(input -> {
            DropdownValue dropdownValue = new DropdownValue(input.getId(), input.getTitle());
            dropdownValue.setType(PLAIN_TEXT);
            return dropdownValue;
        }).collect(Collectors.toList());
    }

    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(workDetailsPageDataSourceResponse.getQueryParams());
        action.setEncryption(workDetailsPageDataSourceResponse.getEncryptionData());
    }

    private String getLenderForm(String merchantId, String employmentType) {
        FormConfig formConfig = new FormConfig(null, null,
                PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(
                merchantId, dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(getFormJson(employmentType));
    }

    private String getFormJson(String employmentType) {
        if (salaried.equals(employmentType)) {
            return salariedFormJson;
        }
        return selfEmployedFormJson;
    }

    private void updateIncome(PriceTextBoxFormFieldValueV0 PriceTextBoxFormFieldValueV0, String monthlyIncome) {
        if (StringUtils.isNotEmpty(monthlyIncome)){      
            PriceTextBoxFormFieldValueV0.setValue(monthlyIncome);
            PriceTextBoxFormFieldValueV0.setDisabled(true);
        }
    }

}
