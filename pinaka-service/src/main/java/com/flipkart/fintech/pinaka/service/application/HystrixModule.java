package com.flipkart.fintech.pinaka.service.application;

import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import com.google.inject.AbstractModule;
import com.netflix.config.ConfigurationManager;
import org.apache.commons.configuration.AbstractConfiguration;
import com.flipkart.fintech.logger.core.flogger.FintechFlogger;
import com.flipkart.fintech.logger.core.FintechLogger;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @since 08/03/21.
 */
public class HystrixModule extends AbstractModule {

    private String hystrixConfigFilePath;

    private static final FintechFlogger logger = FintechLogger.flogger(HystrixModule.class);

    public HystrixModule(String hystrixConfig) {
        this.hystrixConfigFilePath = hystrixConfig;
    }

    @Override
    protected void configure() {
        Properties properties = new Properties();
        try (FileInputStream fileInputStream = new FileInputStream(hystrixConfigFilePath)){
            properties.load(fileInputStream);
            AbstractConfiguration configInstance = ConfigurationManager.getConfigInstance();
            for (Map.Entry<Object, Object> entry : properties.entrySet()) {
                configInstance.setProperty(entry.getKey().toString(), entry.getValue());
            }
        }
        catch (IOException e) {
            logger.error("Unable to reading config file: {}", hystrixConfigFilePath, e);
            throw new PinakaWebAppException("Unable to read hystrix config file");
        }
    }
}
