package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.OtpVerificationPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import javax.inject.Inject;
import org.apache.http.NameValuePair;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Optional;

public class OtpVerificationPageDataSource implements PageDataSource{
    @Inject
    private static ConfigUtils configUtils;
    @Override
    public OtpVerificationPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        OtpVerificationPageDataSourceResponse otpVerificationPageDataSourceResponse = new OtpVerificationPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        otpVerificationPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        otpVerificationPageDataSourceResponse.setResendOtpformData(new QueryParamUtils().getResendOtpFormData());
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        otpVerificationPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return otpVerificationPageDataSourceResponse;
    }
}