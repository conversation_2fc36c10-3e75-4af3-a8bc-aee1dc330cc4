package com.flipkart.fintech.pinaka.service.application;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class OnboardingConfiguration {
    private boolean genericFlowEnabled;
    // this flow would control raise of change entity event in whitelisting flow
    private boolean whitelistChangeEntityEnabled;
    private boolean upgradeJourneyLimitReductionAllowed = false;
    private List<String> genericFlowAccountIds = Lists.newArrayList();
    Map<ProductType, ProductOnboardingConfiguration> productOnboardingConfig;
    private Long plusWhitelistId;
}
