package com.flipkart.fintech.pinaka.service.ruleengine.lendingorchestrator.pagestate;

import static com.flipkart.fintech.pinaka.api.request.v6.ApplicationStatus.APPLICATION_COMPLETED;
import static com.flipkart.fintech.pinaka.api.request.v6.ApplicationStatus.SUCCESS;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_OD_LANDING_PAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_PL_MAINTENANCE;
import static com.flipkart.fintech.pinaka.service.ruleengine.lendingorchestrator.pagestate.PageStateRuleEngineStates.*;
import static com.supermoney.ams.bridge.utils.PinakaConstants.PLConstants.FINANCIAL_PROVIDER;

import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.offer.orchestrator.model.PreApprovedOfferDetails;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.ruleengine.RuleEngine;
import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class PageStateRuleEngine extends
    RuleEngine<PageStateRuleEngineInput, PageStateRuleEngineStates, PageActionResponse> {

  private final DynamicBucket dynamicBucket;

  private static final Set<String> DISBURSED_LOAN_STATUS = getDisbursedLoanStatus();

  public static final Long FIFTEEN_DAYS_IN_SECONDS = 15 * 24 * 60 * 60L;

  @Inject
  public PageStateRuleEngine(DynamicBucket dynamicBucket) {
    this.dynamicBucket = dynamicBucket;
  }

  private static Set<String> getDisbursedLoanStatus() {
    Set<String> disbursedLoanStatus = new HashSet<>();
    disbursedLoanStatus.add(SUCCESS.name());
    disbursedLoanStatus.add(APPLICATION_COMPLETED.name());
    return disbursedLoanStatus;
  }

  @Override
  protected String getRuleEngineName() {
    return "PAGE_STATE";
  }

  @Override
  public PageStateRuleEngineStates  getStateByRuleEngine(PageStateRuleEngineInput input) {
    ApplicationDataResponse plApplicationDataResponse = input.getPlApplicationDataResponse();
    ApplicationDataResponse leadApplicationDataResponse = input.getLeadApplicationDataResponse();
    LendingPageRequest lendingPageRequest = input.getLendingPageRequest();
    PreApprovedOfferDetails preApprovedOfferDetails = input.getPreApprovedOfferDetails();

    if (Objects.isNull(plApplicationDataResponse)) {
      return Objects.isNull(leadApplicationDataResponse) ? CREATE_LEAD : RESUME_LEAD;
    }

    if (Objects.nonNull(lendingPageRequest)
        && Objects.nonNull(lendingPageRequest.getApplicationId())
        && lendingPageRequest.getApplicationId()
        .equals(plApplicationDataResponse.getApplicationId())) {
      return RESUME_LENDER_APPLICATION;
    }

    if (Objects.nonNull(lendingPageRequest)
        && Objects.nonNull(lendingPageRequest.getApplicationId())
        && lendingPageRequest.getApplicationId()
        .equals(leadApplicationDataResponse.getApplicationId())) {
      return RESUME_LEAD;
    }

    if(dynamicBucket.getBoolean(ENABLE_OD_LANDING_PAGE) && Lender.RING.name().equals(plApplicationDataResponse.getApplicationData().get(FINANCIAL_PROVIDER))){
      if(plApplicationDataResponse.getApplicationState().equals("OFFER_DETAILS")){
        if(plApplicationDataResponse.getApplicationData().containsKey("getOffer")){
          GetOfferResponse getOfferResponse = ObjectMapperUtil.get().convertValue(plApplicationDataResponse.getApplicationData().get("getOffer"),
                  GetOfferResponse.class);
          Long ts = getOfferResponse.getJourneyState().getTs();
          Long currentTs = DateUtils.getCurrentDateInSeconds();
          if(currentTs - ts > FIFTEEN_DAYS_IN_SECONDS){
            return OFFER_DETAILS_LANDING_PAGE;
          }
        }
      }
    }

    if (!isEligibleForPreApprovedOffer(plApplicationDataResponse, preApprovedOfferDetails
    )) {
      return RESUME_LENDER_APPLICATION;
    }


    return REPEAT_LOAN_LANDING_PAGE;
  }

  private static boolean isEligibleForPreApprovedOffer(
      ApplicationDataResponse plApplicationDataResponse,
      PreApprovedOfferDetails preApprovedOfferDetails) {
    return DISBURSED_LOAN_STATUS.contains(plApplicationDataResponse.getApplicationState())
        && Objects.nonNull(preApprovedOfferDetails);
  }
}
