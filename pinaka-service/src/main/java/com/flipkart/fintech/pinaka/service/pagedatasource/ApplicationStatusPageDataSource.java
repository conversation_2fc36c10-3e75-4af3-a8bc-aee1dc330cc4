package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanUtilityResponse;
import com.flipkart.fintech.pinaka.service.response.ApplicationStatusPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class ApplicationStatusPageDataSource implements PageDataSource<ApplicationStatusPageDataSourceResponse>{

    @Inject
    private static ConfigUtils configUtils;
    @Override
    public ApplicationStatusPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        AutoDisbursalResponse autoDisbursalResponse = ObjectMapperUtil.get().convertValue(
                applicationDataResponse.getApplicationData().get("authAutoDisbursal"), AutoDisbursalResponse.class);
        LoanUtilityResponse loanUtilityResponse = null;
        if (applicationDataResponse.getApplicationData().containsKey("getPaymentDetails")) {
            loanUtilityResponse = ObjectMapperUtil.get().convertValue(
                    applicationDataResponse.getApplicationData().get("getPaymentDetails"), LoanUtilityResponse.class);
        }
        ApplicationStatusPageDataSourceResponse applicationStatusPageDataSourceResponse  = new ApplicationStatusPageDataSourceResponse();
        applicationStatusPageDataSourceResponse.setLoanUtilityResponse(loanUtilityResponse);
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        applicationStatusPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        applicationStatusPageDataSourceResponse.getQueryParams().put("lastPage","/ams/1/repayment-modes");
        applicationStatusPageDataSourceResponse.getQueryParams().put("taskKey","repaymentSchedule");
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        applicationStatusPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        applicationStatusPageDataSourceResponse.setAutoDisbursalResponse(autoDisbursalResponse);
        return applicationStatusPageDataSourceResponse;
    }
}
