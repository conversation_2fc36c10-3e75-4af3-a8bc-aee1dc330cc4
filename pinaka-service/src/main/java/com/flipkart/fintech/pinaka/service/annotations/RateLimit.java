package com.flipkart.fintech.pinaka.service.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
//this annotation ensures that the method(s) with the specified limiterkey is(are) not executed more than x no of times per second.
public @interface RateLimit {
    String limiterKey();
}