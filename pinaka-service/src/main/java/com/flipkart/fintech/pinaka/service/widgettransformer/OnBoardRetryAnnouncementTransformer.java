package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;

public class OnBoardRetryAnnouncementTransformer {
    private static String onboardRetryAnnouncementjson;

    static {
        onboardRetryAnnouncementjson = TransformerUtils.readFileasString("template/idfc/OnBoardRetryAnnouncement.json");

    }

    public AnnouncementV2WidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse)
            throws JsonProcessingException {
        AnnouncementV2WidgetData announcementV2WidgetData = ObjectMapperUtil.get().readValue(onboardRetryAnnouncementjson, AnnouncementV2WidgetData.class);
        announcementV2WidgetData.getData().getValue().getRichButton().getAction().setParams(WidgetTransformerUtils.getButtonParams(applicationDataResponse));
        return announcementV2WidgetData;
    }
}
