package com.flipkart.fintech.pinaka.service.ruleengine.landingPageOrchestrator;

import com.flipkart.fintech.pinaka.service.ruleengine.models.Filter;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import com.ezylang.evalex.Expression;
import com.ezylang.evalex.config.ExpressionConfiguration;
import com.ezylang.evalex.data.EvaluationValue;
import lombok.CustomLog;


@CustomLog
public class PlLandingPageExpressionEvaluator implements ExpressionEvaluator{


    @Override
    public Boolean evaluate(List<Filter> rules, HashMap<String, Object> ruleEngineVariables) {
        if(Objects.isNull(rules)) return false;
        ExpressionConfiguration configuration = ExpressionConfiguration.defaultConfiguration();
        for (Filter rule : rules) {
            EvaluationValue output;
            try{
                output = new Expression(rule.getExpression(), configuration)
                        .with("application", ruleEngineVariables)
                        .evaluate();
            } catch (Exception e){
                log.error(String.format("ExpressionEvaluatorImplCohort:: failed to evaluate rule %s due to %s", rule, e));
                return false;
            }
            // todo add support for multiple rules
            if (output.getBooleanValue()) return true;
        }
        return false;
    }
}
