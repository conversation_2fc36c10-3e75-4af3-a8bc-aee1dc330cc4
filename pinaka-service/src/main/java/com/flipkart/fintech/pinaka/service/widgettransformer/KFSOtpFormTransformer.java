package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.KFSOtpPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.OtpCustomFormFieldData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

public class KFSOtpFormTransformer {
    private static String genericFormjson;
    private KFSOtpPageDataSourceResponse kfsOtpPageDataSourceResponse;

    static {
        genericFormjson = TransformerUtils.readFileasString("template/idfc/KFSOtp.json");

    }
    public GenericFormWidgetData buildWidgetData(KFSOtpPageDataSourceResponse pageDataSourceReponse)
            throws JsonProcessingException {
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(genericFormjson, GenericFormWidgetData.class);
        CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) genericFormWidgetData.getRenderableComponents()
                .get(0).getValue();
        OtpCustomFormFieldData otpCustomFormFieldData  = (OtpCustomFormFieldData) customFormFieldValue.getCustomFieldData();
        kfsOtpPageDataSourceResponse = pageDataSourceReponse;
        updateSubmitButton(genericFormWidgetData.getSubmitButton());
        updateResendAction(otpCustomFormFieldData.getResendText().getAction());
        return genericFormWidgetData;
    }

    private void updateResendAction(Action action) {
        action.setParams(kfsOtpPageDataSourceResponse.getResendOtpActionParams());
        action.setEncryption(kfsOtpPageDataSourceResponse.getEncryptionData());
    }
    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(kfsOtpPageDataSourceResponse.getQueryParams());
        action.setEncryption(kfsOtpPageDataSourceResponse.getEncryptionData());
    }
}
