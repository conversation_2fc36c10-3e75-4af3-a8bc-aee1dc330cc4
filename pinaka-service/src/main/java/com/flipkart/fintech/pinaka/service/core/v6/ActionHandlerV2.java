package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.ActionHandlerV2Impl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;

@ImplementedBy(ActionHandlerV2Impl.class)
public interface ActionHandlerV2 {
    PageActionResponse submit(FormSubmitRequestV2 submitRequest, String requestId, String userAgent) throws PinakaException;
}
