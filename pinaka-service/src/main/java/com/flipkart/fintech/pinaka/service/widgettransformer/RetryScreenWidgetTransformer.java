package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import lombok.CustomLog;

import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils.getAnnouncementV2WidgetData;


@CustomLog
public class RetryScreenWidgetTransformer {

    private static final String retryScreenJson;

    static {
        retryScreenJson = TransformerUtils.readFileasString("template/sandbox/RetryScreen.json");
    }

    public AnnouncementV2WidgetData buildRetryScreen(ApplicationDataResponse applicationDataResponse)  throws PinakaClientException {
        AnnouncementV2WidgetData announcementV2WidgetData = getGenericFormWidgetData(applicationDataResponse);
        return announcementV2WidgetData;
    }

    private AnnouncementV2WidgetData getGenericFormWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        return getAnnouncementV2WidgetData(applicationDataResponse, retryScreenJson);
    }
}


