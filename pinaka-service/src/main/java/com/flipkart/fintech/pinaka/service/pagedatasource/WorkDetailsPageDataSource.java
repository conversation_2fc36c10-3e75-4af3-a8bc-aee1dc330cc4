package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.WorkDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;

import javax.inject.Inject;
import java.util.List;
import java.util.Optional;

public class WorkDetailsPageDataSource implements PageDataSource<WorkDetailsPageDataSourceResponse> {
    @Inject
    private static ConfigUtils configUtils;

    @Override
    public WorkDetailsPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        WorkDetailsPageDataSourceResponse WorkDetailsPageDataSourceResponse = new WorkDetailsPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        WorkDetailsPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        WorkDetailsPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return WorkDetailsPageDataSourceResponse;
    }
}
