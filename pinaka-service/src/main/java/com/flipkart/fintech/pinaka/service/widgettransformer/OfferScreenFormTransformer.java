package com.flipkart.fintech.pinaka.service.widgettransformer;


import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.BANK_OFFER_STEP_SIZE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.response.InitialOffer;
import com.flipkart.fintech.pinaka.service.response.OfferScreenPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.OfferScreenUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.calm.RangeFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.enums.FormFieldType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.InterestData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.StepperValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.StepperRangeUnits;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.BankOfferFormFieldData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.Getter;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;


public class OfferScreenFormTransformer {

    @Getter
    class Range {
        private int lowerBound;
        private int upperBound;
        private int minTenureForRange;
        private int maxTenureForRange;

        public Range(int lowerBound, int upperBound, int minTenureForRange, int maxTenureForRange) {
            this.lowerBound = lowerBound;
            this.upperBound = upperBound;
            this.minTenureForRange = minTenureForRange;
            this.maxTenureForRange = maxTenureForRange;
        }

        public boolean isWithinRange(int amount) {
            return amount <= upperBound;
        }

        public String getRangeKey(String upperBoundStr) {
            return lowerBound + "-" + upperBoundStr;
        }
    }
    private static String genericFormJson;
    private OfferScreenPageDataSourceResponse offerScreenPageDataSourceResponse;

    static {
        genericFormJson = TransformerUtils.readFileasString("template/idfc/OfferScreen.json");
    }

    public GenericFormWidgetData buildWidgetData(OfferScreenPageDataSourceResponse pageDataSourceReponse)
            throws ParseException, JsonProcessingException {
        GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get().readValue(genericFormJson, GenericFormWidgetData.class);
        offerScreenPageDataSourceResponse = pageDataSourceReponse;
        updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());
        return getData(genericFormWidgetDataCopy);
    }

    private GenericFormWidgetData getData(GenericFormWidgetData genericFormWidgetDataCopy)
            throws ParseException {
        InitialOffer offer = offerScreenPageDataSourceResponse.getInitialOfferGenerationStatusResponse().getOffers().get(0);
        CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) genericFormWidgetDataCopy.getRenderableComponents().get(0).getValue();
        BankOfferFormFieldData bankOfferFormFieldData  = (BankOfferFormFieldData) customFormFieldValue.getCustomFieldData();
        bankOfferFormFieldData.setInterest(new InterestData(Double.parseDouble(offer.getRoi())));
        bankOfferFormFieldData.getTenureSlider().getValue().setMinValue(Long.parseLong(offer.getMinTenure()));
        bankOfferFormFieldData.getTenureSlider().getValue().setMaxValue(Long.parseLong(offer.getMaxTenure()));
        bankOfferFormFieldData.getTenureSlider().getValue().setDefaultValue(Long.parseLong(offer.getMinTenure()));
        bankOfferFormFieldData.getRangeMap().getValue().setRangeMap(generateMap(offer.getMaxEligibleAmount(),offer.getMaxTenure()));
        bankOfferFormFieldData.getAmountSlider().getValue().setMaxValue(Long.parseLong(offer.getMaxEligibleAmount()));
        bankOfferFormFieldData.getAmountSlider().getValue().setMinValue(Long.parseLong(offer.getMinEligibleAmount()));
        bankOfferFormFieldData.getAmountSlider().getValue().setDefaultValue((Long.parseLong(offer.getMaxEligibleAmount())+Long.parseLong(offer.getMinEligibleAmount()))/2);
        bankOfferFormFieldData.getAmountSlider().getValue().getStepperValue().setStepSize(BANK_OFFER_STEP_SIZE);
        ((CustomFormFieldValue) genericFormWidgetDataCopy.getRenderableComponents().get(0).getValue()).setCustomFieldData(OfferScreenUtils.setEmiSchedules(bankOfferFormFieldData,offer.getProcessingFee()));
        return genericFormWidgetDataCopy;
    }


    public Map<String, RangeFormFieldValue> generateMap(String maxAmt, String maxTenure) {
        Map<String, RangeFormFieldValue> stringRangeFormFieldValueMap = new HashMap<>();

        int maxAmount = Integer.parseInt(maxAmt);
        int maxTenureValue = Integer.parseInt(maxTenure);

        Range[] ranges = {
                new Range(0, 50000, 12, Math.min(24, maxTenureValue)),
                new Range(50000, 100000, 12, Math.min(36, maxTenureValue)),
                new Range(100000, 200000, 12, Math.min(48, maxTenureValue)),
                new Range(200000, maxAmount, 12, Math.min(60, maxTenureValue))
        };

        for (Range range : ranges) {
            if (range.isWithinRange(maxAmount)) {
                String rangeKey = range.getRangeKey(maxAmt);
                RangeFormFieldValue rangeFormFieldValue = createRangeFormFieldValue(range.getMinTenureForRange(), range.getMaxTenureForRange());
                stringRangeFormFieldValueMap.put(rangeKey, rangeFormFieldValue);
                return stringRangeFormFieldValueMap;
            }
            String predefinedRangeKey = range.getRangeKey(String.valueOf(range.getUpperBound()));
            RangeFormFieldValue predefinedRangeFormFieldValue = createRangeFormFieldValue(range.getMinTenureForRange(), range.getMaxTenureForRange());
            stringRangeFormFieldValueMap.put(predefinedRangeKey, predefinedRangeFormFieldValue);
        }

        return stringRangeFormFieldValueMap;
    }

    private RangeFormFieldValue createRangeFormFieldValue(int defaultValue, int maxValue) {
        RangeFormFieldValue rangeFormFieldValue = new RangeFormFieldValue();
        rangeFormFieldValue.setDefaultValue(defaultValue);
        rangeFormFieldValue.setDisabled(false);
        rangeFormFieldValue.setMaxValue(maxValue);
        rangeFormFieldValue.setMinValue(defaultValue);
        rangeFormFieldValue.setLabel("Select EMI plan");
        rangeFormFieldValue.setMandatory(true);
        rangeFormFieldValue.setStepperValue(createStepperValue());
        rangeFormFieldValue.setName("amountSlider");
        rangeFormFieldValue.setFormFieldType(FormFieldType.RANGE);
        rangeFormFieldValue.setType("RangeFormFieldValue");
        return rangeFormFieldValue;
    }

    private StepperValue createStepperValue() {
        StepperValue stepperValue = new StepperValue();
        stepperValue.setStepperUnit(StepperRangeUnits.MONTH);
        stepperValue.setStepSize(3);
        stepperValue.setType("StepperValue");
        return stepperValue;
    }


    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(offerScreenPageDataSourceResponse.getQueryParams());
        action.setEncryption(offerScreenPageDataSourceResponse.getEncryptionData());
    }

}
