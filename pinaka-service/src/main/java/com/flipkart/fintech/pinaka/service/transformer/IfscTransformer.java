package com.flipkart.fintech.pinaka.service.transformer;

import com.flipkart.fintech.pandora.api.model.pl.request.IfscRequest;
import com.flipkart.fintech.pandora.api.model.pl.response.IfscAvailableModes;
import com.flipkart.fintech.pandora.api.model.pl.response.IfscBankDetails;
import com.flipkart.fintech.pandora.api.model.pl.response.IfscResponse;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.BankBranch;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;
import com.flipkart.rome.datatypes.response.fintech.calm.MandateMode;
import com.flipkart.rome.datatypes.response.fintech.calm.enums.BankingMode;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class IfscTransformer {
  public static IfscRequest transform(IfscSearchRequest request) {
    IfscRequest ifscRequest = new IfscRequest();
    ifscRequest.setIfscCode(request.getIfscCode());
    return ifscRequest;
  }

  public static IfscSearchResponse transform(IfscResponse ifscDetails) {
    BankBranch bankBranch = getBankBranch(ifscDetails.getBankDetails());
    List<MandateMode> mandateModes = getMandateModes(ifscDetails.getAvailableModes());
    IfscSearchResponse response = new IfscSearchResponse();
    response.setBank(bankBranch);
    response.setAvailableModes(mandateModes);
    return response;
  }

  private static List<MandateMode> getMandateModes(IfscAvailableModes availableModes) {
    List<MandateMode> modes = new ArrayList<>();
    String debitCard = availableModes.getDebitCard();
    if (StringUtils.isNotBlank(debitCard)) {
      MandateMode mandateMode = getDebitCardMode(debitCard);
      modes.add(mandateMode);
    }
    String netBanking = availableModes.getNetBanking();
    if (StringUtils.isNotBlank(netBanking)) {
      MandateMode mandateMode = getNetBankingMode(netBanking);
      modes.add(mandateMode);
    }
    return modes;
  }

  private static MandateMode getNetBankingMode(String netBanking) {
    MandateMode mandateMode = new MandateMode();
    mandateMode.setBankingMode(BankingMode.NET_BANKING);
    mandateMode.setName("Net Banking");
    mandateMode.setCode(netBanking);
    return mandateMode;
  }

  private static MandateMode getDebitCardMode(String debitCard) {
    MandateMode mandateMode = new MandateMode();
    mandateMode.setBankingMode(BankingMode.DEBIT_CARD);
    mandateMode.setName("Debit Card");
    mandateMode.setCode(debitCard);
    return mandateMode;
  }

  private static BankBranch getBankBranch(IfscBankDetails bankDetails) {
    BankBranch bankBranch = new BankBranch();
    bankBranch.setBranch(bankDetails.getBranch());
    bankBranch.setCity(bankDetails.getCity());
    bankBranch.setName(bankDetails.getName());
    return bankBranch;
  }
}
