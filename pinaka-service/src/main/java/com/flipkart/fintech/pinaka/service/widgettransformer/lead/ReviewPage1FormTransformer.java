package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.common.decrypter.DecrypterImpl;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DateFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.PincodeCustomFormFieldDataV0;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.*;

import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.joda.time.DateTime;

import java.text.ParseException;
import java.util.*;


@CustomLog
public class ReviewPage1FormTransformer {

    private static final String initalUserDataFormJson;
    public static final int DATE_OF_BIRTH_FORM_FIELD_INDEX = 1;

    private final ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    private final Map<String, FormFields> formFieldsMap;
    private final DynamicBucket dynamicBucket;
    private final Decrypter decrypter;


    public ReviewPage1FormTransformer(ReviewUserDataSourceResponse reviewUserDataSourceResponse, @Named("dataPageScreen") Map<String, FormFields> formFieldsMap, DynamicBucket dynamicBucket) {
        this.reviewUserDataSourceResponse = reviewUserDataSourceResponse;
        this.formFieldsMap = formFieldsMap;
        this.dynamicBucket = dynamicBucket;
        this.decrypter = new DecrypterImpl();
    }

    public enum FormFields {
        PAN_NUMBER {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                String panNumber = null;
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();

                if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getPan() != null) {
                    panNumber = decrypter.decryptString(reviewUserDataSourceResponse.getProfile().getPan());
                    log.info("Prefilling AddressLine1 from profile for SM_USER_ID {}", reviewUserDataSourceResponse.getProfile().getSmUserId());
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), PAN_PREFILL_FROM_PROFILE)).mark();
                } else if (userDataResponse != null) {
                    panNumber = userDataResponse.getPanNumber();
                }
                if (panNumber != null && !panNumber.isEmpty()) {
                    formFieldValue.setValue(panNumber);
                }
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), panNumber));
            }
        }, DOB {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
                DateFormFieldValue dateFormFieldValue = (DateFormFieldValue) formFieldValue;
                dateFormFieldValue.setMinValue(String.valueOf(DateTime.now().minusYears(60)));
                dateFormFieldValue.setMaxValue(String.valueOf(DateTime.now().minusYears(21)));
                String dob = null;
                if (profileDetailedResponse != null && profileDetailedResponse.getDob() != null) {
                    String dobString = decrypter.decryptString(profileDetailedResponse.getDob());
                    try {
                        dob = DateUtils.convertDateFormat(dobString, DateUtils.getSimpleDateFormat9, DateUtils.getSimpleDateFormat9);
                        PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), DOB_PREFILL_FROM_PROFILE)).mark();
                    } catch (ParseException e) {
                        log.error("Date Format from Profile is Wrong {}", dob);
                    }
                } else if (userDataResponse.getDateOfBirth() != null) {
                    String experianDob = userDataResponse.getDateOfBirth();
                    if (experianDob != null) {
                        try {
                            dob = DateUtils.convertDateFormat(experianDob, DateUtils.experianDateFormat, DateUtils.getSimpleDateFormat9);
                        } catch (ParseException e) {
                            log.error("Invalid Dob Format in Experian for smUserId {}", reviewUserDataSourceResponse.getProfile().getSmUserId());
                        }
                    }
                }
                if (dob != null && !dob.isEmpty()) {
                    formFieldValue.setValue(dob);
                }
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), dob));
            }
        },

        EMAIL {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
                String email = null;
                if (profileDetailedResponse != null && profileDetailedResponse.getEmail() != null) {
                    email = decrypter.decryptString(profileDetailedResponse.getEmail());
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), EMAIL_PREFILL_FROM_PROFILE)).mark();
                } else if (userDataResponse != null) {
                    email = userDataResponse.getEmail();
                }
                if (email != null && !email.isEmpty()) {
                    formFieldValue.setValue(email);
                }
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), email));
            }
        }, HOUSE_NUMBER {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
                String addressLine1 = null;
                if (profileDetailedResponse != null && profileDetailedResponse.getAddressLine1() != null && profileDetailedResponse.getAddressLine2() != null) {
                    addressLine1 = decrypter.decryptString(profileDetailedResponse.getAddressLine1());
                    log.info("Prefilling AddressLine1 from Profile for SM_USER_ID {}", profileDetailedResponse.getSmUserId());
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), ADDRESS_LINE_1_PREFILL_FROM_PROFILE)).mark();

                } else if (userDataResponse != null) {
                    addressLine1 = userDataResponse.getAddressDetailResponse().getAddressLine1();
                }
                if (addressLine1 != null && !addressLine1.isEmpty()) {
                    formFieldValue.setValue(addressLine1);
                }
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), addressLine1));
            }
        }, AREA {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
                String addressLine2 = null;
                if (profileDetailedResponse != null && profileDetailedResponse.getAddressLine2() != null && profileDetailedResponse.getAddressLine1() != null) {
                    addressLine2 = decrypter.decryptString(profileDetailedResponse.getAddressLine2());
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), ADDRESS_LINE_2_PREFILL_FROM_PROFILE)).mark();
                } else if (userDataResponse != null) {
                    addressLine2 = userDataResponse.getAddressDetailResponse().getAddressLine2();
                }
                if (addressLine2 != null && !addressLine2.isEmpty()) {
                    formFieldValue.setValue(addressLine2);
                }
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), addressLine2));
            }
        }, PINCODE {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
                String pincode = null;
                if (profileDetailedResponse != null && profileDetailedResponse.getUserEnteredPincode() != null) {
                    pincode = String.valueOf(profileDetailedResponse.getUserEnteredPincode());
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), PINCODE_PREFILL_FROM_PROFILE)).mark();
                } else if (userDataResponse != null) {
                    pincode = userDataResponse.getAddressDetailResponse().getPincode();
                }
                if (pincode != null && !pincode.isEmpty() && !pincode.equals("0")) {
                    CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) formFieldValue;
                    PincodeCustomFormFieldDataV0 pincodeCustomFormFieldData = (PincodeCustomFormFieldDataV0) customFormFieldValue.getCustomFieldData();
                    pincodeCustomFormFieldData.getPincode().getValue().setValue(pincode);
                    pincodeCustomFormFieldData.getPincode().getValue().setTracking(WidgetTransformerUtils.getTrackingMetadata("pincode", pincode));
                }
            }
        }, GENDER {
            @Override
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                DropdownFormFieldValue genderDropDownFormFieldValue = (DropdownFormFieldValue) formFieldValue;
                DropdownValue selectedOption = new DropdownValue();
                selectedOption.setType(DropdownType.PLAIN_TEXT);
                if (profileDetailedResponse != null && profileDetailedResponse.getGender() != null) {
                    String gender = profileDetailedResponse.getGender();
                    selectedOption.setId(gender);
                    switch (gender) {
                        case "M":
                            selectedOption.setTitle("Male");
                            break;
                        case "F":
                            selectedOption.setTitle("Female");
                            break;
                        case "O":
                            selectedOption.setTitle("Others");
                            break;
                        default:
                            selectedOption.setTitle("Male");
                            break;
                    }
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), GENDER_PREFILL_FROM_PROFILE)).mark();

                } else if (Objects.nonNull(userDataResponse) && Objects.nonNull(userDataResponse.getGender())) {

                    switch (userDataResponse.getGender()) {
                        case "1":
                            selectedOption.setId("M");
                            selectedOption.setTitle("Male");
                            break;
                        case "2":
                            selectedOption.setId("F");
                            selectedOption.setTitle("Female");
                            break;
                        case "3":
                            selectedOption.setId("O");
                            selectedOption.setTitle("Others");
                            break;
                        default:
                            selectedOption.setId("M");
                            selectedOption.setTitle("Male");
                            break;
                    }
                }
                genderDropDownFormFieldValue.setSelectedOption(selectedOption);
                genderDropDownFormFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(genderDropDownFormFieldValue.getName(), selectedOption.getId()));
            }
        }, EMPLOYMENT_TYPE {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter) {
                ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
                MultiDropdownFormFieldValue employmentTypeDropDownValue = (MultiDropdownFormFieldValue) formFieldValue;

                List<MultiDropdownValue> options = employmentTypeDropDownValue.getOptions();
                if (options != null && !options.isEmpty()) {
                    options.forEach(option -> {
                        if (option.getId().equals("Salaried")) {
                            option.setSubtitle("You get a monthly salary");
                        }
                        if (option.getId().equals("SelfEmployed")) {
                            option.setSubtitle("You have a business or shop");
                        }
                    });
                }

                String selectionOption = "";
                if (profileDetailedResponse != null && profileDetailedResponse.getEmploymentType() != null) {
                    String employmentType = String.valueOf(profileDetailedResponse.getEmploymentType());
                    if (employmentType.equals("SelfEmployed")) {
                        selectionOption = "SelfEmployed";
                    } else if(employmentType.equals("Salaried")) {
                        selectionOption = "Salaried";
                    }
                    employmentTypeDropDownValue.setValue(selectionOption);
                    employmentTypeDropDownValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(employmentTypeDropDownValue.getName(), selectionOption));
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), EMPLOYMENT_TYPE_PREFILL_FROM_PROFILE)).mark();
                }
            }
        };

        public abstract void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter);

    }

    static {
        initalUserDataFormJson = TransformerUtils.readFileasString("template/lead/ReviewPage1Screen.json");
    }

    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        try {
            GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get().readValue(initalUserDataFormJson, GenericFormWidgetData.class);
            updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());


            if (Objects.nonNull(reviewUserDataSourceResponse)) {
                prefillFirstInitialUserDataForm(reviewUserDataSourceResponse, genericFormWidgetDataCopy);
            }
            updateDOBField(genericFormWidgetDataCopy);
            return genericFormWidgetDataCopy;
        } catch (Exception e) {
            log.error("First Initial User Data build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

    private void prefillFirstInitialUserDataForm(ReviewUserDataSourceResponse reviewUserDataSourceResponse, GenericFormWidgetData genericFormWidgetData) {

        List<RenderableComponent<FormFieldValue>> renderableComponents = Objects.requireNonNull(genericFormWidgetData.getRenderableComponents());

        for (RenderableComponent<FormFieldValue> renderableComponent : renderableComponents) {
            if (formFieldsMap.containsKey(renderableComponent.getValue().getName())) {
                try {
                    formFieldsMap.get(renderableComponent.getValue().getName()).prefill(reviewUserDataSourceResponse, renderableComponent.getValue(), decrypter);
                } catch (Exception e) {
                    log.warn("Prefill form data in review page 1 got exception while prefilling for sm_user_id " + reviewUserDataSourceResponse.getProfile().getSmUserId() + " for form key " + renderableComponent.getValue().getName(), e);
                }

            }
        }
    }

    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(reviewUserDataSourceResponse.getQueryParams());
        action.setEncryption(reviewUserDataSourceResponse.getEncryptionData());
    }

    private void updateDOBField(GenericFormWidgetData genericFormWidgetDataCopy) {
        List<RenderableComponent<FormFieldValue>> formFieldValue = Objects.requireNonNull(genericFormWidgetDataCopy.getRenderableComponents());
        DateFormFieldValue dateFormFieldValue = Objects.requireNonNull((DateFormFieldValue) formFieldValue.get(DATE_OF_BIRTH_FORM_FIELD_INDEX).getValue());
        dateFormFieldValue.setMinValue(String.valueOf(DateTime.now().minusYears(60)));
        dateFormFieldValue.setMaxValue(String.valueOf(DateTime.now().minusYears(18)));
    }

}
