package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.GroupedFormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupDataValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.apache.commons.text.StringSubstitutor;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.*;

@CustomLog
public class LV3ReviewPage1FormTransformer implements GroupedFormWidgetTransformer {

    private static final String ALL_FILLED_REVIEW_SCREEN_TEMPLATE;
    private static final String PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE;
    private static final String NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE;

    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final FormWidgetDataJsonParser formWidgetDataJsonParser;
    private final LocationRequestHandler locationRequestHandler;
    private final BqIngestionHelper bqIngestionHelper;

    static {
        ALL_FILLED_REVIEW_SCREEN_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/AllFilledReviewScreen.json");
        PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/Personal-Details-Prefilled-Without-Work-Address-Details.json");
        NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/Without-Personal-Without-Address.json");
    }

    static public class LV3ReviewPageBannerFormTransformer implements BannerWidgetTransformer {

        private static final String NAME_PAGE_BANNER;

        static {
            NAME_PAGE_BANNER = TransformerUtils.readFileasString("template/lead/V3/ReviewScreenBanner.json");
        }

        @Override
        public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
            BannerWidgetData bannerWidgetData;
            try {
                bannerWidgetData = ObjectMapperUtil.get().readValue(NAME_PAGE_BANNER, BannerWidgetData.class);
            } catch (Exception e) {
                throw new PinakaException("Error while building banner widget data for LV3 Review Page 1 for userId: "+ applicationDataResponse.getSmUserId(), e);
            }
            return bannerWidgetData;
        }
    }

    public LV3ReviewPage1FormTransformer(Decrypter decrypter, DynamicBucket dynamicBucket,
                                         FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
                                         FormWidgetDataFetcher formWidgetDataFetcher,
                                         FormWidgetDataJsonParser formWidgetDataJsonParser, LocationRequestHandler locationRequestHandler, BqIngestionHelper bqIngestionHelper) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.formWidgetDataJsonParser = formWidgetDataJsonParser;
        this.locationRequestHandler = locationRequestHandler;
        this.bqIngestionHelper = bqIngestionHelper;
    }

    @Override
    public GroupedFormWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        GroupedFormWidgetData groupedFormWidgetData = null;
        String pageState = "ALL_FILLED";
        try {
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();
            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
            groupedFormWidgetData = ObjectMapperUtil.get().readValue(getFormJson(ALL_FILLED_REVIEW_SCREEN_TEMPLATE, applicationDataResponse), GroupedFormWidgetData.class);
            Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
            Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
            Map<String, SubmitButtonValue> formFieldSubmitButtons = this.formWidgetDataJsonParser
                    .getFormFieldSubmitButtons(groupedFormWidgetData.getFormGroups());
            Map<String, Object> userData = this.formWidgetDataFetcher
                    .getDataForFields(formFieldValueMapToPrefill.keySet(), leadPageDataSourceResponse, reviewUserDataSourceResponse,
                            decrypter, locationRequestHandler);

            boolean shouldReRead = false;
            if (personalDetailsNotFound(userData)) {
                groupedFormWidgetData = ObjectMapperUtil.get()
                        .readValue(getFormJson(NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE, applicationDataResponse), GroupedFormWidgetData.class);
                pageState = "NOTHING_PREFILLED";
                shouldReRead = true;
            } else if (addressNotFound(userData) || workDetailsNotFound(userData)) {
                groupedFormWidgetData = ObjectMapperUtil.get()
                        .readValue(getFormJson(PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE, applicationDataResponse), GroupedFormWidgetData.class);
                pageState = "PERSONAL_DETAILS_PREFILLED";
                shouldReRead = true;
            }
            if (shouldReRead) {
                formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
                groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
                formFieldSubmitButtons = this.formWidgetDataJsonParser.getFormFieldSubmitButtons(groupedFormWidgetData.getFormGroups());
            }
            updateGroupedWidgetSubmitButton(groupedFormWidgetData.getSubmitButton(), reviewUserDataSourceResponse);
            updateSubmitButtons(formFieldSubmitButtons, reviewUserDataSourceResponse);
            formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
            formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
            this.formWidgetDataJsonParser.updateFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups(), formFieldValueMapToPrefill, formFieldSubmitButtons);
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), "V3"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V3"));
            throw new PinakaException("Error while building widget Group Data for LV3 Review Page 1, userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return groupedFormWidgetData;
    }



    boolean addressNotFound(Map<String, Object> userData) {
        String addressLineOne = (String) userData.getOrDefault(HOUSE_NUMBER_STRING, "");
        PincodeDetailsResponse pinCode = (PincodeDetailsResponse) userData.getOrDefault(PINCODE_DETAILS_STRING, null);
        List<CAISHolderAddressDetails> addressOptions = (List<CAISHolderAddressDetails>) userData.getOrDefault(ADDRESSES_STRING, Collections.emptyList());
        boolean defaultAddressPresent = StringUtils.isNotBlank(addressLineOne) && pinCode != null && StringUtils.isNotBlank(pinCode.getPincode());
        boolean atleastOneValidAddressFound = atLeastOneValidAddressPresent(addressOptions);
        return !(defaultAddressPresent || atleastOneValidAddressFound);
    }

    boolean atLeastOneValidAddressPresent(List<CAISHolderAddressDetails> addressOptions) {
        if (addressOptions == null || addressOptions.isEmpty()) {
            return false;
        }
        for (CAISHolderAddressDetails address : addressOptions) {
            String firstLineOfAddress = address.getFirstLineOfAddress();
            String pincode = address.getZipPostalCodeOfAddress();
            if (StringUtils.isNotBlank(firstLineOfAddress) && StringUtils.isNotBlank(pincode)) {
                return true;
            }
        }
        return false;
    }

    boolean workDetailsNotFound(Map<String, Object> userData) {
        String companyName = (String) userData.getOrDefault(COMPANY_NAME_STRING, "");
        String income = (String) userData.getOrDefault(MONTHLY_INCOME_STRING, "");
        String email = (String) userData.getOrDefault(EMAIL_STRING, "");
        String incomeSource = (String) userData.getOrDefault(INCOME_SOURCE_STRING, "");
        String employmentType = (String) userData.getOrDefault(EMPLOYMENT_TYPE_STRING, "");
        return StringUtils.isEmpty(companyName) || StringUtils.isEmpty(income) || StringUtils.isEmpty(email) || StringUtils.isEmpty(incomeSource) || StringUtils.isEmpty(employmentType);
    }

    boolean personalDetailsNotFound(Map<String, Object> userData) {
        return !isPanNumberPresent(userData) || !isDateOfBirthPresent(userData) || !isGenderStringPresent(userData);
    }

    boolean isDateOfBirthPresent(Map<String, Object> userData) {
        return userData.containsKey("dob") && Objects.nonNull(userData.get("dob")) && StringUtils.isNotBlank(userData.get("dob").toString());
    }

    boolean isPanNumberPresent(Map<String, Object> userData) {
        return userData.containsKey("panNumber") && Objects.nonNull(userData.get("panNumber")) && StringUtils.isNotBlank(userData.get("panNumber").toString());
    }

    boolean isGenderStringPresent(Map<String, Object> userData) {
        boolean isFirstStringPresent = userData.containsKey(GENDER_STRING)
                && Objects.nonNull(userData.get(GENDER_STRING)) &&
                StringUtils.isNotBlank(userData.get(GENDER_STRING).toString());
        boolean isSecondStringPresent = userData.containsKey(GENDER_STRING_2)
                && Objects.nonNull(userData.get(GENDER_STRING_2)) &&
                StringUtils.isNotBlank(userData.get(GENDER_STRING_2).toString());
        return isFirstStringPresent || isSecondStringPresent;
    }

    private String getFormJson(String template, ApplicationDataResponse applicationDataResponse) {
        FormConfig formConfig = new FormConfig("18", "90", PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }

}
