package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.GroupedFormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupDataValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import lombok.CustomLog;
import org.apache.commons.text.StringSubstitutor;

import java.util.*;

import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3NamePageFormTransformer.LV3NamePageBannerFormTransformer.PL_DYNAMIC_IMAGE_BANNER_URL;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.*;

@CustomLog
public class LV3NamePageFormTransformer implements GroupedFormWidgetTransformer {

    private static String NAME_PAGE_FORM_TEMPLATE;
    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    private final FormWidgetDataJsonParser formWidgetDataJsonParser;
    private final LocationRequestHandler locationRequestHandler;
    private final BqIngestionHelper bqIngestionHelper;

    private final FormConfig formConfig = new FormConfig("18", "90",
            PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);

    static {
        NAME_PAGE_FORM_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/NamePage.json");
    }

    static public class LV3NamePageBannerFormTransformer implements BannerWidgetTransformer {

        private final DynamicBucket dynamicBucket;
        private static final String NAME_PAGE_BANNER;
        public static final String PL_DYNAMIC_IMAGE_BANNER_URL = "dynamicImageBannerUrl";

        public LV3NamePageBannerFormTransformer(DynamicBucket dynamicBucket) {
            this.dynamicBucket = dynamicBucket;
        }

        static {
            NAME_PAGE_BANNER = TransformerUtils.readFileasString("template/lead/V3/NamePage-banner.json");
        }

        @Override
        public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
            BannerWidgetData bannerWidgetData;
            try {
                bannerWidgetData = ObjectMapperUtil.get().readValue(NAME_PAGE_BANNER, BannerWidgetData.class);
            } catch (Exception e) {
                throw new PinakaException("Error while building banner widget data for LV3 Name Page, for userId: "+ applicationDataResponse.getSmUserId(), e);
            }
            List<RenderableComponent<ImageValue>> renderableComponents = Objects.requireNonNull(bannerWidgetData.getRenderableComponents());
            Objects.requireNonNull(renderableComponents.get(0).getValue()).setDynamicImageUrl(dynamicBucket.getString(PL_DYNAMIC_IMAGE_BANNER_URL));
            return bannerWidgetData;
        }
    }

    public LV3NamePageFormTransformer(Decrypter decrypter, DynamicBucket dynamicBucket,
                                      FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
                                      FormWidgetDataFetcher formWidgetDataFetcher,
                                      FormWidgetDataJsonParser formWidgetDataJsonParser,
                                      LocationRequestHandler locationRequestHandler, BqIngestionHelper bqIngestionHelper) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.formWidgetDataJsonParser = formWidgetDataJsonParser;
        this.locationRequestHandler = locationRequestHandler;
        this.bqIngestionHelper = bqIngestionHelper;
    }

    @Override
    public GroupedFormWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        GroupedFormWidgetData groupedFormWidgetData;
        String pageState = "NAME_PAGE";
        try {
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();

            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
            String bannerURL = dynamicBucket.getString(PL_DYNAMIC_IMAGE_BANNER_URL);
            NAME_PAGE_FORM_TEMPLATE = NAME_PAGE_FORM_TEMPLATE.replace("${BANNER_URL}", bannerURL);
            groupedFormWidgetData = ObjectMapperUtil.get().readValue(getFormJson(NAME_PAGE_FORM_TEMPLATE, applicationDataResponse), GroupedFormWidgetData.class);

            Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
            Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
            Map<String, Object> userData = this.formWidgetDataFetcher
                    .getDataForFields(formFieldValueMapToPrefill.keySet(), leadPageDataSourceResponse,
                            reviewUserDataSourceResponse, decrypter, locationRequestHandler);
            Map<String, SubmitButtonValue> formFieldSubmitButtons = formWidgetDataJsonParser
                    .getFormFieldSubmitButtons(groupedFormWidgetData.getFormGroups());

            updateGroupedWidgetSubmitButton(groupedFormWidgetData.getSubmitButton(), reviewUserDataSourceResponse);
            updateSubmitButtons(formFieldSubmitButtons, reviewUserDataSourceResponse);
            this.formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
            this.formWidgetDataJsonParser.updateFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups(), formFieldValueMapToPrefill, formFieldSubmitButtons);
            this.formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), "V3"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V3"));
            throw new PinakaException("Error while building widget Group Data for LV3 Name Page for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return groupedFormWidgetData;
    }

    private String getFormJson(String template, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMap(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }

}
