package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.service.client.sandbox.v1.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse;
import com.flipkart.fintech.pinaka.service.response.ApplicationStatusSandboxPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import javax.inject.Inject;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 26/12/23
 */
public class ApplicationStatusSandboxPageDataSource implements PageDataSource<ApplicationStatusSandboxPageDataSourceResponse> {

    @Inject
    private static ConfigUtils configUtils;

    @Override
    public ApplicationStatusSandboxPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        GetApplicationStatusResponse getApplicationStatusResponse = ObjectMapperUtil.get().convertValue
                (applicationDataResponse.getApplicationData().get("getLenderStatus"), GetApplicationStatusResponse.class);
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        String loanId = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("createApplicationLender"),
                CreateApplicationResponse.class).getLenderApplicationId();

        ApplicationStatusSandboxPageDataSourceResponse dataSourceResponse = new ApplicationStatusSandboxPageDataSourceResponse();
        dataSourceResponse.setEncryptionData(encryption.orElse(null));
        dataSourceResponse.setApplicationStatusResponse(getApplicationStatusResponse);
        dataSourceResponse.setLoanId(loanId);

        return dataSourceResponse;
    }
}
