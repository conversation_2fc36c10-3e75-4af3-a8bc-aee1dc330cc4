package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.ETBVerifyOTPResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;

import javax.inject.Inject;
import java.util.List;
import java.util.Optional;

public class ETBVerifyOtp implements PageDataSource<ETBVerifyOTPResponse>{
    @Inject
    private static ConfigUtils configUtils;
    @Override
    public ETBVerifyOTPResponse getData(ApplicationDataResponse applicationDataResponse) {
        ETBVerifyOTPResponse etbVerifyOTPResponse = new ETBVerifyOTPResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        etbVerifyOTPResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        etbVerifyOTPResponse.setResendOtpformData(new QueryParamUtils().getResendOtpFormData());
        etbVerifyOTPResponse.setEncryptionData(encryption.orElse(null));
        return etbVerifyOTPResponse;
    }
}
