package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.flipkart.fintech.pinaka.service.application.Constants;
import com.flipkart.fintech.pinaka.service.application.filter.FilterPriority;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Priority;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by sujeetkumar.r on 08/01/18.
 */
@Priority(FilterPriority.AUTHORIZATION_FILTER)
public class AuthorizationFilter implements ContainerRequestFilter{

    private static final Map<String, String> merchantIdToKeyMapping = new HashMap<>();

    static {
        merchantIdToKeyMapping.put("mp_flipkart", "mp_flipkart");
        merchantIdToKeyMapping.put("test_merchant", "test_merchant");
    }

    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {
        String authorizationHeaderValue = containerRequestContext.getHeaderString(Constants.X_AUTHORIZATION);
        if(StringUtils.isEmpty(authorizationHeaderValue)) {
            Exception cause = new IllegalAccessException("No authorization information found.");
            throw new WebApplicationException(cause, Response.Status.FORBIDDEN);
        }
        String merchantId = containerRequestContext.getHeaderString(Constants.X_MERCHANT_ID);
        StringBuilder keyReverse = new StringBuilder(merchantIdToKeyMapping.get(merchantId));
        String input = merchantId+":"+ keyReverse.reverse();
        String base64encoded = Base64.getEncoder().encodeToString(input.getBytes());
        if(!base64encoded.equals(authorizationHeaderValue))
        {
            Exception cause = new IllegalAccessException("Invalid authorization token");
            throw new WebApplicationException(cause, Response.Status.UNAUTHORIZED);
        }
    }
}
