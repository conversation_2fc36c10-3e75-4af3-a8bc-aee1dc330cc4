package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TokenUtils;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@CustomLog
public class WidgetTransformerUtils {

    public static Map<String, Object> getButtonParams(ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> params = new HashMap<>();
        params.put("applicationId", applicationDataResponse.getApplicationId());
        params.put("userId", applicationDataResponse.getExternalUserId());
        if ((!Objects.isNull(applicationDataResponse.getPendingTask())) && applicationDataResponse.getPendingTask().size() != 0) {
            PendingTask pendingTask = applicationDataResponse.getPendingTask().get(0);
            params.put("taskId", pendingTask.getTaskId());
        }
        return params;
    }

    public static AnnouncementV2WidgetData getAnnouncementV2WidgetData(ApplicationDataResponse applicationDataResponse, String formJsonString) throws PinakaClientException {
        try {
            AnnouncementV2WidgetData announcementV2WidgetData = ObjectMapperUtil.get().readValue(formJsonString, AnnouncementV2WidgetData.class);
            announcementV2WidgetData.getData().getValue().getRichButton().getAction().setParams(WidgetTransformerUtils.getButtonParams(applicationDataResponse));
            return announcementV2WidgetData;
        } catch (Exception e) {
            log.error("AnnouncementV2WidgetData build failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

    public static Map<String, Object> getTrackingMetadata(String fieldName, String prefilledValue){
        return new HashMap<String, Object>(){{
            put("sendImpression", true);
            put("fieldName",  fieldName);
            put("prefill", true);
            put("prefilledValue", prefilledValue);
            put("sendEdits", true);
            put("sendInlineError", true);
        }};
    }

    public static Map<String, String> getTrackingMetadataForGroupFields(String fieldName, String prefilledValue){
        return new HashMap<String, String>(){{
            put("sendImpression", "true");
            put("fieldName",  fieldName);
            put("prefill", "true");
            put("prefilledValue", prefilledValue);
            put("sendEdits", "true");
            put("sendInlineError", "true");
        }};
    }

    public static String getToken(String applicationId) throws JsonProcessingException {
        Token token = Token.builder()
                .applicationId(applicationId)
                .operation(null)
                .build();
        return TokenUtils.getEncryptedToken(token);
    }
}
