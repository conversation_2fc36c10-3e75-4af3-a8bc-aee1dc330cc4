package com.flipkart.fintech.pinaka.service.widgettransformer;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.OfferWaitScreenDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.LoadingWidgetDataV0;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;


public class OfferWaitScreenLoadingWidgetTransformer {

    public LoadingWidgetDataV0 buildWidgetData(OfferWaitScreenDataSourceResponse offerWaitScreenDataSourceResponse)
            throws JsonProcessingException {
        String loadingWidgetJson = TransformerUtils.readFileasString("template/idfc/OfferWaitScreen.json");
        LoadingWidgetDataV0 loadingWidgetData =  ObjectMapperUtil.get().readValue(loadingWidgetJson, LoadingWidgetDataV0.class);
        loadingWidgetData.getPollingContext().getAction().setParams(offerWaitScreenDataSourceResponse.getQueryParams());
        return loadingWidgetData;
    }

}
