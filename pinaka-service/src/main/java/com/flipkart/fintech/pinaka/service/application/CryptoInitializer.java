package com.flipkart.fintech.pinaka.service.application;

import com.flipkart.fintech.pinaka.service.utils.CryptoUtils;
import io.dropwizard.lifecycle.Managed;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 24/04/18.
 */
public class CryptoInitializer implements Managed {

    private CryptoConfiguration cryptoConfig;

    public CryptoInitializer(CryptoConfiguration cryptoConfig){
        this.cryptoConfig = cryptoConfig;
    }

    @Override
    public void start() throws Exception {
        CryptoUtils.initCrypto(cryptoConfig.getAlgo(), cryptoConfig.getSecretKey());
    }

    @Override
    public void stop() throws Exception {

    }
}
