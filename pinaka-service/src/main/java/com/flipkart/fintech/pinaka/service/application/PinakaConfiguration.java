package com.flipkart.fintech.pinaka.service.application;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.affordability.collections.client.CollectionsClientConfiguration;
import com.flipkart.affordability.config.*;
import com.flipkart.affordability.khaata.client.KhaataClientConfig;
import com.flipkart.affordability.onboarding.client.OnboardingClientConfig;
import com.flipkart.affordability.underwriting.client.UnderwritingConfig;
import com.flipkart.fintech.ardour.client.ArdourClientConfig;
import com.flipkart.fintech.cryptex.config.CryptexBundleConfiguration;
import com.flipkart.fintech.kafka.config.KafkaConsumerConfiguration;
import com.flipkart.fintech.logger.application.FintechLogConfig;
import com.flipkart.fintech.outbound.OutboundBundleConfiguration;
import com.flipkart.fintech.pandora.client.PandoraClientConfiguration;
import com.flipkart.fintech.pandoralite.client.PandoraLiteClientConfiguration;
import com.flipkart.fintech.pinaka.client.PinakaCalvinClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.VaradhiClentConfig;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClientConfig;
import com.flipkart.fintech.pinaka.models.DatabaseConfig;
import com.flipkart.fintech.pinaka.service.application.hawkeye.HawkeyeAsyncConfig;
import com.flipkart.fintech.pinaka.service.configuration.*;
import com.flipkart.fintech.profile.pubsub.PublisherConfig;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.config.ProfileServiceDatabaseConfig;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.redis.configuration.RedisConfiguration;
import com.flipkart.fintech.rotator.RotationConfiguration;
import com.flipkart.fintech.security.gibraltar.config.GibraltarClientConfig;
import com.flipkart.fintech.stratum.api.config.StratumD42Configuration;
import com.flipkart.fintech.user.data.config.SmUserServiceClientConfig;
import com.flipkart.fintech.userservice.client.UslClientConfig;
import com.flipkart.fintech.viesti.config.ViestiConsumerConfiguration;
import com.flipkart.fintech.winterfell.client.WinterfellClientConfig;
import com.flipkart.flux.client.config.FluxClientConfiguration;
import com.flipkart.mysql.lock.MysqlLockConfiguration;
import com.flipkart.restbus.turbo.config.TurboConfig;
import com.flipkart.sa.model.SourceAttributionConfiguration;
import com.flipkart.sensitive.configuration.SensitiveAnnotationConfiguration;
import com.flipkart.sm.pages.config.PageServiceConfig;
import com.sumo.crisys.client.CrisysClientConfig;
import com.supermoney.google.clients.bigtable.client.config.BigtableConfig;
import com.supermoney.ams.bridge.models.MerchantJourneyConfig;
import io.dropwizard.Configuration;
import io.federecio.dropwizard.swagger.SwaggerBundleConfiguration;
import lombok.Data;
import lombok.Getter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by sujeetkumar.r on 09/08/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PinakaConfiguration extends Configuration {

    @NotNull
    @Valid
    @JsonProperty(value = "swagger")
    private final SwaggerBundleConfiguration swaggerBundleConfiguration = new SwaggerBundleConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "pinakaClientConfig")
    private final PinakaClientConfig pinakaClientConfig = new PinakaClientConfig();

    @JsonProperty("eventPublishEnvironment")
    private String eventPublishEnvironment;

    @JsonProperty("publisherConfig")
    private PublisherConfig publisherConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "pinakaCalvinClientConfig")
    private final PinakaCalvinClientConfig pinakaCalvinClientConfig = new PinakaCalvinClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "ardourClientConfig")
    private final ArdourClientConfig ardourClientConfig = new ArdourClientConfig();


    @NotNull
    @Valid
    @JsonProperty(value = "gcpPinakaClientConfig")
    private final PinakaClientConfig gcpPinakaClientConfig = new PinakaClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "userServiceClientConfig")
    private final UserServiceClientConfig userServiceClientConfig = new UserServiceClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "oAuthServiceClientConfig")
    private final OAuthServiceClientConfig oAuthServiceClientConfig = new OAuthServiceClientConfig();


    @NotNull
    @Valid
    @JsonProperty(value = "loginServiceClientConfig")
    private final LoginServiceClientConfig loginServiceClientConfig = new LoginServiceClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "profileServiceConfig")
    private ProfileServiceConfig profileServiceConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "profileClientConfiguration")
    private ProfileClientConfiguration profileClientConfiguration;

    @NotNull
    @Valid
    @JsonProperty(value = "fluxAsyncClientConfig")
    private FluxClientConfig fluxAsyncClientConfig = new FluxClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "connektClientConfig")
    private ConnektClientConfig connektClientConfig = new ConnektClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "robinhoodAsyncClientConfig")
    private RobinhoodAsyncClientConfig robinhoodAsyncClientConfig = new RobinhoodAsyncClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "pandoraClientConfig")
    private PandoraClientConfiguration pandoraClientConfiguration = new PandoraClientConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "pandoraLiteClientConfig")
    private PandoraLiteClientConfiguration pandoraLiteClientConfiguration = new PandoraLiteClientConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "fluxConfiguration")
    private FluxClientConfiguration fluxClientConfig = new FluxClientConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "onboardingClientConfig")
    private OnboardingClientConfig onboardingClientConfig = new OnboardingClientConfig();

    @NotNull
    @JsonProperty(value = "cryptoConfig")
    private CryptoConfiguration cryptoConfiguration = new CryptoConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "underwritingConfig")
    private UnderwritingConfig underwritingConfig = new UnderwritingConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "lenderConfiguration")
    private LenderConfiguration lenderConfiguration = new LenderConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "tijoriConfig")
    private TijoriConfiguration tijoriConfiguration = new TijoriConfiguration();

    @NotNull
    @Valid
    @JsonProperty("external_client_config")
    private ExternalClientConfig externalClientConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "contextWiseWhitelistConfig")
    private ContextWiseWhitelistConfiguration contextWiseWhitelistConfiguration = new ContextWiseWhitelistConfiguration();

    @JsonProperty
    private LenderRankingConfiguration lenderRankingConfiguration;

    @JsonProperty(value = "cryptexConfiguration")
    private CryptexBundleConfiguration cryptexConfiguration;

    @NotNull
    @Valid
    @JsonProperty(value = "varadhiClientConfig")
    private VaradhiClentConfig varadhiClentConfig;

    public OutboundBundleConfiguration getOutboundBundleConfiguration() {
        return OutboundBundleConfiguration;
    }

    public void setOutboundBundleConfiguration(OutboundBundleConfiguration OutboundBundleConfiguration) {
        this.OutboundBundleConfiguration = OutboundBundleConfiguration;
    }

    @JsonProperty(value = "OutboundBundleConfiguration")
    private OutboundBundleConfiguration OutboundBundleConfiguration;

    @JsonProperty
    private CollectionsClientConfiguration collectionsClientConfiguration;

    @NotNull
    @Valid
    @JsonProperty(value = "abConfiguration")
    private final ABConfiguration abConfiguration = new ABConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "uiConfiguration")
    private final UiConfiguration uiConfiguration = new UiConfiguration();

    @Valid
    private final DataPointConfiguration dataPointConfiguration = new DataPointConfiguration();

    @NotNull
    @Getter
    private List<RateLimitConfig> rateLimitingConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "onboardingConfig")
    private final OnboardingConfiguration onboardingConfiguration = new OnboardingConfiguration();

    @NotNull
    private final AlfredClientConfig alfredClientConfig = new AlfredClientConfig();

    //TODO: DexterClientConfig to be fetch in DexterClientImpl
//    @NotNull
//    @Valid
//    @JsonProperty(value = "dexterClientConfig")
//    private final DexterClientConfig dexterClientConfig;

    @NotNull
    private final FkPayClientConfig fkPayClientConfig = new FkPayClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "neoCrmClientConfig")
    private NeoCrmClientConfig neoCrmClientConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "databaseConfig")
    private DatabaseConfig databaseConfig = new DatabaseConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "databaseSlaveConfig")
    private DatabaseSlaveConfig databaseSlaveConfig = new DatabaseSlaveConfig();


    @NotNull
    @Valid
    @JsonProperty(value = "profileDatabaseConfig")
    private ProfileServiceDatabaseConfig profileServiceDatabaseConfig = new ProfileServiceDatabaseConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "pageServiceConfig")
    private PageServiceConfig pageServiceConfig = new PageServiceConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "mysqlLockConfig")
    private final MysqlLockConfiguration mysqlLockConfiguration = new MysqlLockConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "redisConfig")
    private final RedisConfiguration redisConfiguration = new RedisConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "sourceAttributionConfig")
    private final SourceAttributionConfiguration sourceAttributionConfiguration = new SourceAttributionConfiguration();

    @JsonProperty("rotation")
    public RotationConfiguration rotationConfiguration;


    @NotNull
    @Valid
    @JsonProperty(value = "winterfellClientConfig")
    private final WinterfellClientConfig winterfellClientConfig = new WinterfellClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "stratumD42Configuration")
    private final StratumD42Configuration stratumD42Configuration = new StratumD42Configuration();

    @NotNull
    private final LockinClientConfig lockinClientConfig = new LockinClientConfig();


    @NotNull
    @Valid
    @JsonProperty(value = "pandoraAsyncClientConfig")
    private final PandoraAsyncClientConfiguration pandoraAsyncClientConfiguration = new PandoraAsyncClientConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "skylerClientConfig")
    private final SkylerClientConfig skylerClientConfig = new SkylerClientConfig();


    @NotNull
    @Valid
    @JsonProperty(value = "khaataClientConfig")
    private final KhaataClientConfig khaataClientConfig = new KhaataClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "coinManagerClientConfig")
    private final CoinManagerClientConfig coinManagerClientConfig = new CoinManagerClientConfig();

    @NotNull
    @Valid
    @JsonProperty("kafkaConsumerConfiguration")
    private final KafkaConsumerConfiguration kafkaConsumerConfiguration = new KafkaConsumerConfiguration();

    @NotNull
    @Valid
    @JsonProperty("hawkeyeAsyncConfig")
    private final HawkeyeAsyncConfig hawkeyeAsyncConfig = new HawkeyeAsyncConfig();

    @NotNull
    @Valid
    @JsonProperty("encryptionKeys")
    private HashMap<String, String> encryptionKeys = new HashMap<>();

    public HashMap<String, String> getEncryptionKeys() {
        return encryptionKeys;
    }

    public SensitiveAnnotationConfiguration getSensitiveAnnotationConfiguration() {
        return new SensitiveAnnotationConfiguration(getEncryptionKeys());
    }

    private final SecurityKeyConfiguration securityKeyConfiguration = new SecurityKeyConfiguration();

    @NotNull
    @Valid
    @JsonProperty("tijoriAsyncClientConfig")
    private final TijoriAsyncClientConfig tijoriAsyncClientConfig = new TijoriAsyncClientConfig();

    @NotNull
    @Valid
    private final PinakaAsyncClientConfig pinakaAsyncClientConfig = new PinakaAsyncClientConfig();


    @NotNull
    private final FintechUserServiceClientConfig fintechUserServiceClientConfig = new FintechUserServiceClientConfig();

    @NotNull
    @Valid
    private final UslClientConfig uslClientConfig = new UslClientConfig();


    @NotNull
    @Valid
    @JsonProperty(value = "hystrixModuleConfiguration")
    private final HystrixModuleConfiguration hystrixModuleConfiguration = new HystrixModuleConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "multiTenantOAuthClientConfig")
    private final MultiTenantOAuthClientConfig multiTenantOAuthClientConfig = new MultiTenantOAuthClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "multiTenantConnektClientConfig")
    private final MultiTenantConnektClientConfig multiTenantConnektClientConfig = new MultiTenantConnektClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "turboConfig")
    private TurboConfig turboConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "coreLogisticsClientConfig")
    private final CoreLogisticsClientConfig coreLogisticsClientConfig = new CoreLogisticsClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "gibraltarClientConfig")
    private GibraltarClientConfig gibraltarClientConfig = new GibraltarClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "criClientConfig")
    private CriClientConfig criClientConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "esClientConfig")
    private EsClientConfig esClientConfig = new EsClientConfig();

    @NotNull
    @Valid
    @JsonProperty(value = "consentConfig")
    private ConsentConfig consentConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "viestiConfig")
    private ViestiConsumerConfiguration viestiConfig = new ViestiConsumerConfiguration();

    @NotNull
    @Valid
    @JsonProperty(value = "fintechLogConfig")
    private FintechLogConfig fintechLogConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "smUserServiceClientConfig")
    private SmUserServiceClientConfig smUserServiceClientConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "merchantJourneyUrlConfig")
    private Map<String, MerchantJourneyConfig> merchantJourneyUrlConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "crisysClientConfig")
    private CrisysClientConfig crisysClientConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "offerServiceConfig")
    private OfferServiceConfig offerServiceConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "bigtableConfig")
    private BigtableConfig bigtableConfig;

    @NotNull
    @Valid
    @JsonProperty(value = "pubsubconfigCs")
    private com.supermoney.commons.pubsub.client.config.PublisherConfig publisherConfigforCs;

    @NotNull
    @Valid
    @JsonProperty(value = "dexterClientConfig")
    private DexterClientConfig dexterClientConfig;
}
