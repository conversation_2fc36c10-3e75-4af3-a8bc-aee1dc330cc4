package com.flipkart.fintech.pinaka.service.application;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.DefaultValue;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ES_CLIENT_TIMEOUT_MS_DEFAULT;

@Data
public class EsClientConfig {
    @NotEmpty private String hostName;
    @NotNull private Integer port;
    @DefaultValue(ES_CLIENT_TIMEOUT_MS_DEFAULT) private Integer connectionTimeout;
}
