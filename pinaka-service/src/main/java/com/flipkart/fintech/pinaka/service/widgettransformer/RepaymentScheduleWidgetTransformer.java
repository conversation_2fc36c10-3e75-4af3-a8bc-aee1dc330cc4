package com.flipkart.fintech.pinaka.service.widgettransformer;


import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.response.RepaymentScheduleResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.fintech.supermoney.TextDataValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.RowCellType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.table.RowCellValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.TableWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import lombok.CustomLog;

@CustomLog
public class RepaymentScheduleWidgetTransformer {

    private static String tableWidgetJson;

    static {
        tableWidgetJson = TransformerUtils.readFileasString("template/idfc/RepaymentSchedule.json");
    }
    public TableWidgetData buildtableWidgetData(RepaymentScheduleResponse response)
            throws PinakaClientException {
        try {
            TableWidgetData tableWidgetData = ObjectMapperUtil.get().readValue(tableWidgetJson, TableWidgetData.class);
            getData(tableWidgetData,response);
            return tableWidgetData;
        }
        catch (Exception e) {
            log.error("TableWidgetData build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }

    }

    private void getData(TableWidgetData tableWidgetData, RepaymentScheduleResponse response) {
        AutoDisbursalResponse autoDisbursalResponse = response.getAutoDisbursalResponse();
        List<RepaySchedule>repayScheduleList =  autoDisbursalResponse.getResourceData().get(0).getRepaySchedule();
        Collections.sort(repayScheduleList, Comparator.nullsFirst(Comparator.comparing(inst -> {
            if (inst.getInstallmentNumber() == null) {
                return null;
            }
            try {
                return Integer.parseInt(inst.getInstallmentNumber());
            } catch (NumberFormatException e) {
                return null;
            }
        })));
        for(RepaySchedule repaySchedule:repayScheduleList)
        {   List<RowCellValue>rowCellValueList = new ArrayList<>();
            RowCellValue monthCellValue = getRowCellValue(repaySchedule.getInstallmentNumber());
            RowCellValue installmentCellValue = getRowCellValue(repaySchedule.getInstallment());
            RowCellValue interestCellValue = getRowCellValue(repaySchedule.getInterestComponent());
            RowCellValue outstandinPrincipalCellValue = getRowCellValue(repaySchedule.getOutstandingPrincipal());
            RowCellValue principalCellValue = getRowCellValue(repaySchedule.getPrincipalComponent());
            rowCellValueList.add(monthCellValue);
            rowCellValueList.add(installmentCellValue);
            rowCellValueList.add(principalCellValue);
            rowCellValueList.add(interestCellValue);
            rowCellValueList.add(outstandinPrincipalCellValue);
            tableWidgetData.getTableData().getRows().add(rowCellValueList);
        }
    }
    private RowCellValue getRowCellValue(String text)
    {
        RowCellValue rowCellValue = new RowCellValue<>();
        TextDataValue textDataValue = new TextDataValue();
        textDataValue.setText(text);
        rowCellValue.setValue(textDataValue);
        rowCellValue.setCellType(RowCellType.TEXT);
        return rowCellValue;
    }
}
