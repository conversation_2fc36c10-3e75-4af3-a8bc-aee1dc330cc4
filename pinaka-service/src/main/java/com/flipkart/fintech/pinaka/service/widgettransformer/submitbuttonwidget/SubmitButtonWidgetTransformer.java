package com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;

public interface SubmitButtonWidgetTransformer {

  SubmitButtonWidgetData buildSubmitButtonWidgetData(
      ApplicationDataResponse applicationDataResponse)
      throws PinakaClientException, JsonProcessingException, PinakaException;
}