package com.flipkart.fintech.pinaka.service.application;

import com.flipkart.fintech.cryptex.annotation.EncryptedConfig;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 16/09/20.
 */
@Data
public class FkPayClientConfig {
    private String url;
    @EncryptedConfig(key = "fkPayClientId")
    private String clientId;
    @EncryptedConfig(key = "fkPayClientSecret")
    private String clientSecret;
    @EncryptedConfig(key = "fkPayEncryptionkey")
    private String encryptionKey;
}
