package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.service.application.Constants;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Created by sujee<PERSON><PERSON>.r on 25/08/18.
 */
@CustomLog
@Priority(5000)
public class DeviceDetailsFilter implements ContainerRequestFilter {

    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {
        try {
            if (containerRequestContext.getHeaders().get("Host").get(0).startsWith("10.47")) {
                List<String> keys = Arrays.asList("Host", "x_sideline_exchange_name", "x_main_exchange_name", "x_restbus_http_uri", "X-Forwarded-For", "fk-client-ip");
                StringBuilder debugLog = new StringBuilder("Request received via the Chennai frontend for URL : " + containerRequestContext.getUriInfo().getRequestUri() + " containing the following headers: \t");
                for (String key : keys) {
                    if (containerRequestContext.getHeaders().containsKey(key)) {
                        debugLog.append(key).append(" : ").append(containerRequestContext.getHeaders().get(key)).append("\t");
                    }
                }
                log.info(debugLog.toString());
            }
        } catch (Exception ignored) {}
        try {
            String deviceDetails = containerRequestContext.getHeaderString(Constants.X_DEVICE_DETAILS);

            if (Objects.isNull(RequestContextThreadLocal.REQUEST_CONTEXT.get())) {
                log.info("RequestContextThreadLocal's RequestContext is null");
                RequestContextThreadLocal.setRequestContext(new RequestContext(null, null, null, null, false));
            }

            RequestContextThreadLocal.REQUEST_CONTEXT.get()
                    .setHeader(Constants.X_DEVICE_DETAILS, deviceDetails);
            log.info("setting device details in filter {}", RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(Constants.X_DEVICE_DETAILS));
            if (StringUtils.isNotBlank(deviceDetails)) {
                if (MDC.get(Constants.X_DEVICE_DETAILS) != null) {
                    log.info("Prev Device Details in MDC {}", MDC.get(Constants.X_DEVICE_DETAILS));
                }
                MDC.put(Constants.X_DEVICE_DETAILS, deviceDetails);

                log.info("New Device Details value from MDC {}", MDC.get(Constants.X_DEVICE_DETAILS));
            }
        } catch (Exception e) {
            log.error("exception in device details filter {}", e, e);
        }
    }
}
