package com.flipkart.fintech.pinaka.service.application;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 30/08/17.
 */
public class Constants {
    private Constants() {

    }

    public static final String X_CLIENT_ID = "X-Client-Id";
    public static final String HEADER_EVENT_NAME = "X_EVENT_NAME";
    public static final String X_MERCHANT_ID = "X-Merchant-Id";
    public static final String X_AUTHORIZATION = "X-Authorization";
    public static final String X_DEVICE_DETAILS = "X-Device-Details";
    public static final String X_USER_AGENT = "X-User-Agent";
    public static final String APPLICATION_JSON = "application/json";
    public static final String PINCODE_INDEX = "pincode_master_index";
    public static final String EMPLOYER_INDEX = "employer_master_index_v0";
    public static final String ID = "_id";
    public static final String INDIA= "INDIA";
    public static final String QUEUE = "queue";
    public static final String POST = "POST";
    public static final String DECISION_APPROVED = "approved";
    public static final String DECISION_SOFT_APPROVED = "soft_approved";
    public static final String DECISION_DECLINED = "declined";
    public static final String FREEFIELD6 = "payment_modes_12m__credit_card_num_ratio";
    public static final String FREEFIELD7 = "payment_modes__cod_num_ratio";
    public static final String FREEFIELD8 = "order_window_lt__rto_count_num_ratio";
    public static final String FREEFIELD9 = "payment_modes_12m__wallet";
    public static final String FREEFIELD10 = "payment_modes_lt__credit_card__count_unique_num";
    public static final String FREEFIELD11 = "payment_modes_12m__credit_card_emi_num_ratio";
    public static final String FREEFIELD12 = "order_window_24m__order_count_num_ratio";
    public static final String FREEFIELD13 = "visit_count__web_num_decile";
    public static final String FREEFIELD14 = "anlt_bu__life_style__order_count_num_norm_ratio_decile";
    public static final String FREEFIELD15 = "city__metro__order_value_decile";
    public static final String FREEFIELD16 = "score";
    public static final String BNPL_CREMO_SCORE = "bnpl_cremo_score";
    public static final String HOME_LATITUDE = "cbc_home_latitude";
    public static final String HOME_LONGITUDE = "cbc_home_longitude";

    public static final List<String> JRM_V1_FEATURES = Collections.unmodifiableList(Arrays.asList("payment_modes_12m__credit_card_num_ratio",
            "payment_modes__cod_num_ratio",
            "order_window_lt__rto_count_num_ratio",
            "payment_modes_12m__wallet",
            "payment_modes_lt__credit_card__count_unique_num",
            "payment_modes_12m__credit_card_emi_num_ratio",
            "order_window_24m__order_count_num_ratio",
            "visit_count__web_num_decile",
            "anlt_bu__life_style__order_count_num_norm_ratio_decile",
            "city__metro__order_value_decile",
            "score",
            "bnpl_cremo_score"));

    public static final List<String> JRM_V2_FEATURES = Collections.unmodifiableList(Arrays.asList(
            "purchase_v231", "purchase_v337", "location_adddress_v105", "engagement_v145", "profile_v1",
            "purchase_v109", "payments_v176", "payments_v45", "payments_v178", "purchase_v313",
            "payments_v62", "payments_v182", "location_adddress_v74", "payments_v54", "engagement_v206",
            "payments_v61", "location_adddress_v56", "purchase_v22", "post_purchase_v7", "purchase_v50",
            "payments_v137", "payments_v77", "purchase_v4", "purchase_v199", "purchase_v149",
            "payments_v191", "purchase_v284", "purchase_v20", "purchase_v348", "engagement_v197",
            "purchase_v179", "engagement_v150", "payments_v180", "post_purchase_v9", "purchase_v245",
            "location_adddress_v182", "engagement_v131", "purchase_v49", "engagement_v59", "location_adddress_v24",
            "purchase_v256", "purchase_v286", "purchase_v23", "purchase_v135", "purchase_v230",
            "purchase_v195", "engagement_v148", "payments_v80", "payments_v20", "purchase_v223",
            "purchase_v237", "purchase_v24", "location_adddress_v75", "engagement_v51", "purchase_v371",
            "payments_v123", "purchase_v306", "purchase_v366", "location_adddress_v161", "engagement_v14",
            "score_v2", "bnpl_cremo_score_v2"));

    public static final String BIGFOOT_INGESTION_CALLBACK_URL = "%s" + "/pinaka/1/ingest/callback/%s/%s";
    public static final String FK_MERCHANT_KEY = "1";
    public static final String START_ACTION = "start";
    public static final String STOP_ACTION = "stop";
    public static final String NA = "NA";
    public static final String PINAKA_CLIENT_ID = "pinaka";
    public static final String ARDOUR_CLIENT_ID = "ardour";
    public static final String PAGE_ID = "pageId";
    public static final String CARD_CONSOLE_PAGE_IDS = "cardConsolePageIds";
    public static final String CARD_CONSOLE_HOME_PAGE_ID = "cardConsoleHomePageId";


    public static final String DEFAULT = "default";
    public static final String ENCRYPTION_KEY_REF = "kycKey";
    public static final String CBC_PII_DATA_KEY_REF = "cbcKey";
    public static final String       SMS_INSIGHTS_FORM_KEY            = "WAIT_FOR_SMS_UPLOAD";
    public static final String CBC_LIVE_COHORTS_KEY = "cbc.live_cohorts";
    public static final String CARD_CONSOLE = "CARD_CONSOLE";
    public static final String CBC_VIEW_DETAILS = "cbcViewCardDetailsBannerUrl";
    public static final String CBC_APPLY_NOW = "cbcApplyNowBannerUrl";
    public static final String CBC_CONTINUE_APPLICATION = "cbcContinueApplicationBannerUrl";
    public static final String CBC_COMING_SOON = "cbcComingSoonBannerUrl";
    public static final String RATE_LIMITING_MESSAGE = "Request is Rate Limited";
    public static final String RATE_LIMIT_KEY_MISSING = "Rate limit is missing";
    public static final String RATE_LIMITING_METRIC = "RateLimit.%s";
    public static final Long SUPER_COIN_TIME_RANGE = 8035200000L;
    public static final Long SUPER_COIN_MILI_SECONDS = 86400000L;
    public static final String CBC = "CBC";
    public static final String CBC_SC_ELITE = "CBC_SC_ELITE";
    public static final String CBC_SC_LITE = "CBC_SC_LITE";
    public static final String CBC_CODE = "530";
    public static final String CBC_SC_ELITE_CODE  = "534";
    public static final String CBC_SC_LITE_CODE = "535";
    public static final long KYC_VALID_TILL_SECONDS = 7890000; //3 months
    public static final long KYC_VALID_TILL_DAYS = 90; //3 months

    public static final String ONBOARDING_CONSENTS = "ONBOARDING_CONSENTS";
    public static final String CONSENT_SERVICE_FLAG= "consentServiceFlag";
    public static final String SAVE_LIMIT_PATH = "/pinaka/4/limit/save";
    public static final String JOURNEY_CONTEXT_PARAM = "journey_context";
    public static final String PRODUCT_PARAM = "product";
    public static final String UPDATE_LIMIT_STATE_PATH = "/pinaka/4/limit/update/state";
    public static final String X_EXTERNAL_ID = "X-External-Id";
    public static final String REVALIDATION_CREDIT_PROFILE = "REVALIDATION_CREDIT_PROFILE";
    public static final String REVALIDATION_UNDERWRITING_RULE_CONFIG = "revalidation.underwriting.ruleConfig";
    public static final String INITIATE_REVALIDATION_APPLICATION_PATH = "/pinaka/4/fintech/%s/revalidation/initiate";
    public static final String UPDATE_PERSONAL_LOAN_BORROWER_PATH = "/pinaka/2/borrower/update";
    public static final String SHOPSY_MERCHANT = "SHOPSY";

    public static final String PROFILE_BASIC_DETAILS_MATCH = "PROFILE_BASIC_DETAILS_MATCH";

    public static final String PROFILE_BASIC_DETAILS_MISMATCH = "PROFILE_BASIC_DETAILS_MISMATCH";
    public static final String USER_DISCARD = "userDiscard";
}
