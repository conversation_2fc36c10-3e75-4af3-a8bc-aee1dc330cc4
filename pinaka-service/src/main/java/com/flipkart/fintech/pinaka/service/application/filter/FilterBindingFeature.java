package com.flipkart.fintech.pinaka.service.application.filter;

import com.flipkart.fintech.pinaka.service.application.filter.request.AuthorizationFilter;
import com.flipkart.fintech.pinaka.service.application.filter.request.ClientFilter;
import com.flipkart.fintech.pinaka.service.application.filter.request.MerchantFilter;

import javax.ws.rs.container.DynamicFeature;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.FeatureContext;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 30/08/17.
 */
public class FilterBindingFeature implements DynamicFeature {
    @Override
    public void configure(ResourceInfo resourceInfo, FeatureContext featureContext) {
        IgnoreFilter ignoreFilter = resourceInfo.getResourceClass().getAnnotation(IgnoreFilter.class);
        if(null == ignoreFilter || ignoreFilter.value().equals(FilterConstants.ALL) || ignoreAllFilter(resourceInfo)){
            return;
        }
        if(isMerchantFilterRequired(ignoreFilter)) featureContext.register(MerchantFilter.class);
        if(isClientFilterRequired(ignoreFilter)) featureContext.register(ClientFilter.class);
        if(isAuthFilterRequired(ignoreFilter)) featureContext.register(AuthorizationFilter.class);
    }

    private boolean isMerchantFilterRequired(IgnoreFilter ignoreFilter){
        boolean flag = true;
        if (ignoreFilter.value().equals(FilterConstants.MERCHANT)) {
            flag = false;
        }
        return flag;
    }

    private boolean isClientFilterRequired(IgnoreFilter ignoreFilter){
        boolean flag = true;
        if (ignoreFilter.value().equals(FilterConstants.CLIENT)) {
            flag = false;
        }
        return flag;
    }

    private boolean isAuthFilterRequired(IgnoreFilter ignoreFilter){
        boolean flag = true;
        if (ignoreFilter.value().equals(FilterConstants.AUTHORIZATION)) {
            flag = false;
        }
        return flag;
    }

    private boolean ignoreAllFilter(ResourceInfo resourceInfo){

        return resourceInfo.getResourceMethod().getName().equals("handlePennyDropCompletion");
    }
}
