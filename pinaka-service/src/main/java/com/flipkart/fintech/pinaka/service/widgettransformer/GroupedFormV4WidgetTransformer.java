package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3ReviewPage2FormTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.google.inject.Inject;
import lombok.CustomLog;
import lombok.NonNull;

@CustomLog
public class GroupedFormV4WidgetTransformer {

    private final LV3NamePageFormTransformer lv3NamePageFormTransformer;
    private final LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer;
    private final LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer;

    @Inject
    public GroupedFormV4WidgetTransformer(LV3NamePageFormTransformer lv3NamePageFormTransformer,
                                          LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer,
                                          LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer) {
        this.lv3NamePageFormTransformer = lv3NamePageFormTransformer;
        this.lv3ReviewPage1FormTransformer = lv3ReviewPage1FormTransformer;
        this.lv3ReviewPage2FormTransformer = lv3ReviewPage2FormTransformer;
    }

    public GenericFormWidgetData buildWidgetData(@NonNull String formType, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        switch (formType) {
            case "LEAD_V3_PAGE_1_FORM":
                return lv3NamePageFormTransformer.buildWidgetGroupData(applicationDataResponse);
            case "LEAD_V3_PAGE_2_FORM":
                return lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse);
            case "LEAD_V3_PAGE_3_FORM":
                return lv3ReviewPage2FormTransformer.buildWidgetData(applicationDataResponse);
        }
        throw new PinakaException("Unknown formType : " + formType);
    }

}
