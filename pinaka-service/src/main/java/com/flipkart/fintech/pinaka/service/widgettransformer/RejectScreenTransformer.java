package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import lombok.CustomLog;

import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils.getAnnouncementV2WidgetData;

@CustomLog
public class RejectScreenTransformer {
    private static String formJsonString;

    static {
        formJsonString = TransformerUtils.readFileasString("template/sandbox/RejectScreen.json");
    }

    public AnnouncementV2WidgetData buildRejectScreen(ApplicationDataResponse applicationDataResponse)  throws PinakaClientException{
        AnnouncementV2WidgetData announcementV2WidgetData = getGenericFormWidgetData(applicationDataResponse);
        return announcementV2WidgetData;
    }

    private AnnouncementV2WidgetData getGenericFormWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        return getAnnouncementV2WidgetData(applicationDataResponse, formJsonString);
    }


}
