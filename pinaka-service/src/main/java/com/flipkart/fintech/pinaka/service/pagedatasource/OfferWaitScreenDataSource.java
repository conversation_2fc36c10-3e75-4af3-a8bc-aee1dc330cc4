package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.OfferWaitScreenDataSourceResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class OfferWaitScreenDataSource implements PageDataSource<OfferWaitScreenDataSourceResponse>{

    @Override
    public OfferWaitScreenDataSourceResponse getData(ApplicationDataResponse applicationData) {
        Map<String,Object> queryparams = new HashMap<>();
        queryparams.put("applicationId",applicationData.getApplicationId());
        queryparams.put("applicationState",applicationData.getApplicationState());
        return new OfferWaitScreenDataSourceResponse(queryparams);

    }
}
