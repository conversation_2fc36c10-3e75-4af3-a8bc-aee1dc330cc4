package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.HeaderConstants;
import io.dropwizard.util.Sets;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.MultivaluedMap;
import java.util.Objects;
import java.util.Set;

@CustomLog
public class MerchantIdFilter implements ContainerRequestFilter {
    private final Set<String> defaultMerchants = Sets.of(MerchantUser.MerchantKeys.FLIPKART_MERCHANT_KEY,
            MerchantUser.MerchantKeys.SHOPSY_MERCHANT_KEY, MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY);

    public void filter(ContainerRequestContext containerRequestContext) {
        if(Objects.isNull(RequestContextThreadLocal.REQUEST_CONTEXT.get())) {
            RequestContextThreadLocal.setRequestContext(new RequestContext(false));
        }

        RequestContext requestContext = RequestContextThreadLocal.REQUEST_CONTEXT.get();

        MultivaluedMap<String, String> headers = containerRequestContext.getHeaders();
        String merchantId = headers.getFirst(HeaderConstants.X_MERCHANT_ID);
        if (StringUtils.isBlank(merchantId) || !defaultMerchants.contains(merchantId)) {
            log.info("Merchant id header is not coming for the api: {}", containerRequestContext.getUriInfo().getPath());
            markMissingMerchantIdMetric(containerRequestContext.getUriInfo().getPath());
        }

        requestContext.setHeader(HeaderConstants.X_MERCHANT_ID, merchantId);
    }

    private void markMissingMerchantIdMetric(String api) {
        PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), "missing.merchantid")).mark();
    }
}
