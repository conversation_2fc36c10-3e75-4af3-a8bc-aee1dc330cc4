package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.flipkart.fintech.pinaka.service.application.Constants;
import com.flipkart.fintech.pinaka.service.application.filter.FilterPriority;

import javax.annotation.Priority;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by sujee<PERSON><PERSON>.r on 30/08/17.
 */
@Priority(FilterPriority.MERCHANT_FILTER)
public class MerchantFilter implements ContainerRequestFilter {

    private static final List<String> whitelistedMerchantIds = new ArrayList<>();

    static {
        whitelistedMerchantIds.add("mp_flipkart");
        whitelistedMerchantIds.add("test_merchant");
    }

    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {

        if (containerRequestContext.getUriInfo().getPath().contains("swagger")) {
            return;
        }

        String merchantId = containerRequestContext.getHeaderString(Constants.X_MERCHANT_ID);

        if(null == merchantId || merchantId.isEmpty() || !whitelistedMerchantIds.contains(merchantId)){
            throw new WebApplicationException("No merchant identifier found",
                    new IllegalAccessException("No merchant identifier found"),
                    Response.Status.FORBIDDEN);
        }
    }
}
