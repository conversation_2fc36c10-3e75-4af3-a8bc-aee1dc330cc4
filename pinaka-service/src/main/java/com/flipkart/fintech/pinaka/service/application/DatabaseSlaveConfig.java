package com.flipkart.fintech.pinaka.service.application;

import com.flipkart.fintech.cryptex.annotation.EncryptedConfig;
import io.dropwizard.db.DataSourceFactory;
import lombok.Data;

@Data
public class DatabaseSlaveConfig extends DataSourceFactory {
    @EncryptedConfig(key = "databaseSlaveConfigUrl")
    private String slaveEncryptedUrl;
    @EncryptedConfig(key = "databaseSlaveConfigUser")
    private String slaveEncryptedUser;
    @EncryptedConfig(key = "databaseSlaveConfigPassword")
    private String slaveEncryptedPassword;

    public void setSlaveEncryptedUrl(String slaveEncryptedUrl) {
        super.setUrl(slaveEncryptedUrl);
        this.slaveEncryptedUrl = slaveEncryptedUrl;
    }
}
