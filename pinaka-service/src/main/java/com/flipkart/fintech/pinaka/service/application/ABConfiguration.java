package com.flipkart.fintech.pinaka.service.application;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ABConfiguration {
    @NotNull
    private String  endpoint;
    @NotNull
    private String  layerList;
    @NotNull
    private String  clientId;

    private List<String> testAccounts;

    @NotNull
    private boolean unlayeredAbEnabled;

    @NotNull
    private String clientSecretKey;
}

