package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

@CustomLog
public class BankLogoTransformer {
    private static String textWidgetJson;
    static {
        //todo :
        textWidgetJson = TransformerUtils.readFileasString("template/idfc/BankLogo.json");
    }
    public RichMessageWidgetData buildtextWidgetData()
            throws PinakaClientException {
        try {
            RichMessageWidgetData richMessageWidgetData = ObjectMapperUtil.get()
                    .readValue(textWidgetJson, RichMessageWidgetData.class);
            return richMessageWidgetData;
        }
        catch (Exception e) {
            log.error("RichMessageWidgetData build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }

    }
}
