package com.flipkart.fintech.pinaka.service.widgettransformer;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.LEAD;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.ams.ApplicationTypeUtils;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.DecrypterImpl;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.response.BasicDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DateFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.*;
import javax.annotation.Nullable;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.joda.time.DateTime;

@CustomLog
public class BasicDetailsFormTransformer implements BannerWidgetTransformer {

  private static final String genericFormjsonSandbox;
  private static final String personalLoanBannerjson;
  private static final Map<String, FormConfig> lenderFormConfig = new HashMap<>();
  public static final int DATE_OF_BIRTH_FORM_FIELD_INDEX = 2;
  public static final String dateRegexPattern = "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/([0-9]{4})$";
  private final BasicDetailsPageDataSourceResponse basicDetailsPageDataSourceResponse;
  private final DecrypterImpl decrypter = new DecrypterImpl();
  private final Map<String, FormFields> formFieldsMap;
  private final DynamicBucket dynamicBucket;

  public enum FormFields {
    FIRST_NAME {
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if(profile.getFirstName()!=null) {
          String firstName = decrypter.decryptString(profile.getFirstName());
          if (firstName != null) {
            formFieldValue.setValue(firstName);
          }
        }
      }
    },
    LAST_NAME{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if (profile.getLastName()!=null) {
          String lastName = decrypter.decryptString(profile.getLastName());
          if (lastName != null) {
            formFieldValue.setValue(lastName);
          }
        }
      }
    },
    DOB{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        DateFormFieldValue dateFormFieldValue = (DateFormFieldValue) formFieldValue;
        dateFormFieldValue.setMinValue(String.valueOf(DateTime.now().minusYears(60)));
        dateFormFieldValue.setMaxValue(String.valueOf(DateTime.now().minusYears(21)));
        if(profile.getDob()!=null) {
          String dob = decrypter.decryptString(profile.getDob());
          if (dob != null) {
            Pattern pattern = Pattern.compile(dateRegexPattern);
            Matcher matcher = pattern.matcher(dob);
            if(matcher.matches()) {
              formFieldValue.setValue(dob);
            }
          }
        }
        formFieldValue = dateFormFieldValue;
      }
    },
    PAN{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if(profile.getPan()!=null) {
          String pan = decrypter.decryptString(profile.getPan());
          if (pan != null) {
            formFieldValue.setValue(pan);
          }
        }
      }
    },
    EMPLOYMENT_TYPE{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if(profile.getEmploymentType()!=null) {
          formFieldValue.setValue(profile.getEmploymentType().toString());
        }
      }
    },
    GENDER{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if(profile.getGender()!=null) {
          DropdownValue genderDropDownValue = new DropdownValue();
          genderDropDownValue.setId(profile.getGender());
          genderDropDownValue.setType(DropdownType.PLAIN_TEXT);

          ((DropdownFormFieldValue) formFieldValue).setSelectedOption(genderDropDownValue);
        }
      }
    },
    PINCODE{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if(profile.getUserEnteredPincode()!=null) {
          formFieldValue.setValue(String.valueOf(profile.getUserEnteredPincode()));
        }
      }
    },
    EMAIL{
      public void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter) {
        if(profile.getEmail()!=null) {
          String email = decrypter.decryptString(profile.getEmail());
          if (email != null) {
            formFieldValue.setValue(email);
          }
        }
      }
    };

    public abstract void prefill(ProfileDetailedResponse profile, FormFieldValue formFieldValue, DecrypterImpl decrypter);
  }


  //todo:move file name to constants
  static {
    genericFormjsonSandbox = TransformerUtils.readFileasString(
        "template/sandbox/BasicDetailsSandbox.json");
    personalLoanBannerjson = TransformerUtils.readFileasString(
        "template/sandbox/PersonalLoanBanner.json");
    lenderFormConfig.put("MONEYVIEW", new FormConfig("21", "57",
        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap));
    lenderFormConfig.put("MONEYVIEWOPENMKT", new FormConfig("21", "57",
        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap));
    lenderFormConfig.put("FIBE", new FormConfig("19", "55",
        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap));
    lenderFormConfig.put("OMNI", new FormConfig("21", "57",
        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap));
    lenderFormConfig.put("IDFC", new FormConfig("21", "60",
        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantIDFCConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap));
  }

  private static final BannerWidgetData bannerWidgetData;

  static {
    try {
      bannerWidgetData = ObjectMapperUtil.get()
          .readValue(personalLoanBannerjson, BannerWidgetData.class);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  public BasicDetailsFormTransformer(
          BasicDetailsPageDataSourceResponse basicDetailsPageDataSourceResponse,
          @Named("panPageScreen") Map<String, FormFields> formFieldsMap, DynamicBucket dynamicBucket) {
    this.basicDetailsPageDataSourceResponse = basicDetailsPageDataSourceResponse;
    this.formFieldsMap=formFieldsMap;
    this.dynamicBucket = dynamicBucket;
  }

  public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse)
      throws PinakaClientException {
    try {
      GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get()
          .readValue(getLenderForm(applicationDataResponse), GenericFormWidgetData.class);
      updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());

      ProfileDetailedResponse profile = basicDetailsPageDataSourceResponse.getProfile();
      if (Objects.nonNull(profile)) {
        prefillLeadFormData(profile, genericFormWidgetDataCopy);
      } else {
        updateDOBField(genericFormWidgetDataCopy, null);
      }

//            removePincodeField(genericFormWidgetDataCopy,applicationDataResponse);
      return genericFormWidgetDataCopy;
    } catch (Exception e) {
      log.error("Basic Details build Widget Data failed with error : {}", e.getMessage());
      throw new PinakaClientException(e);
    }
  }

  private void prefillLeadFormData(ProfileDetailedResponse profile,
      GenericFormWidgetData genericFormWidgetDataCopy) {
    List<RenderableComponent<FormFieldValue>> renderableComponents = Objects.requireNonNull(
            genericFormWidgetDataCopy.getRenderableComponents());

    for(RenderableComponent<FormFieldValue> renderableComponent: renderableComponents){
      if(formFieldsMap.containsKey(renderableComponent.getValue().getName())){
        formFieldsMap.get(renderableComponent.getValue().getName())
                .prefill(profile, renderableComponent.getValue(), decrypter);
      }
    }
  }

  @Override
  public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) {
    return bannerWidgetData;
  }

  private void updateDOBField(GenericFormWidgetData genericFormWidgetDataCopy,
      @Nullable String dob) {
    List<RenderableComponent<FormFieldValue>> formFieldValue = Objects.requireNonNull(
        genericFormWidgetDataCopy.getRenderableComponents());
    DateFormFieldValue dateFormFieldValue = Objects.requireNonNull(
        (DateFormFieldValue) formFieldValue.get(DATE_OF_BIRTH_FORM_FIELD_INDEX).getValue());
    dateFormFieldValue.setMinValue(String.valueOf(DateTime.now().minusYears(60)));
    dateFormFieldValue.setMaxValue(String.valueOf(DateTime.now().minusYears(21)));
    if (StringUtils.isNotBlank(dob)) {
      dateFormFieldValue.setValue(dob);
    }
  }

  private void removePincodeField(GenericFormWidgetData genericFormWidgetDataCopy,
      ApplicationDataResponse applicationDataResponse) {
    if (
        (!applicationDataResponse.getApplicationType().equals(LEAD.name()))
    ) {
      Objects.requireNonNull(genericFormWidgetDataCopy.getRenderableComponents()).remove(4);
    }
  }


  private void updateSubmitButton(SubmitButtonValue submitButton) {
    Action action = submitButton.getButton().getAction();
    action.setParams(basicDetailsPageDataSourceResponse.getQueryParams());
    action.setEncryption(basicDetailsPageDataSourceResponse.getEncryptionData());
  }

  private String getLenderForm(ApplicationDataResponse applicationDataResponse) {
    FormConfig defaultFormConfig = new FormConfig("21", "60",
        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
    FormConfig formConfig = Optional.ofNullable(
            lenderFormConfig.get(ApplicationTypeUtils.getLender(applicationDataResponse)))
        .orElse(defaultFormConfig);
    Map<String, Object> formConfigMap = formConfig.getFormConfigMap(
        applicationDataResponse.getMerchantId(), dynamicBucket);
    StringSubstitutor sub = new StringSubstitutor(formConfigMap);
    return sub.replace(genericFormjsonSandbox);
  }


}