package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.common.decrypter.DecrypterImpl;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.calm.AutoSuggestFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.insurtech.PriceTextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import entitymanager.client.shade.org.apache.commons.text.StringSubstitutor;
import lombok.CustomLog;

import java.util.*;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pinaka.service.constants.PageConstant.PL_ADDITIONAL_DETAILS_FORM.industryTypeOptions;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.*;
import static com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType.PLAIN_TEXT;

/**
 * <AUTHOR>
 * @date 12/01/25
 */
@CustomLog
public class ReviewPage2FormTransformer {
    private static final String salariedFormJson;
    private static final String selfEmployedFormJson;
    private static String salaried = "Salaried";
    public static final String selfEmployed = "SelfEmployed";
    private final Map<String, FormField> formFieldMap;

    private final ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    private final DynamicBucket dynamicBucket;
    private final Decrypter decrypter;

    //todo:move file name to constants
    static {
        salariedFormJson = TransformerUtils.readFileasString("template/lead/ReviewPage2SalariedScreen.json");
        selfEmployedFormJson = TransformerUtils.readFileasString("template/lead/ReviewPage2SelfEmplScreen.json");
    }


    public ReviewPage2FormTransformer(ReviewUserDataSourceResponse reviewUserDataSourceResponse, @Named("page2Screen") Map<String, FormField> formFieldMap, DynamicBucket dynamicBucket) {
        this.reviewUserDataSourceResponse = reviewUserDataSourceResponse;
        this.formFieldMap = formFieldMap;
        this.dynamicBucket = dynamicBucket;
        this.decrypter = new DecrypterImpl();
    }

    public enum FormField {

        COMPANY_NAME {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType) {
                if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getCompanyName() != null) {
                    String companyName = reviewUserDataSourceResponse.getProfile().getCompanyName();
                    if(salaried.equals(employmentType)){
                        AutoSuggestFormFieldValue getAutoFilledCompanyName = (AutoSuggestFormFieldValue) formFieldValue;
                        DropdownValue value = new DropdownValue();
                        if(reviewUserDataSourceResponse.getProfile().getOrganizationId() != null){
                            value.setId(reviewUserDataSourceResponse.getProfile().getOrganizationId());
                            value.setTitle(companyName);
                            value.setType(PLAIN_TEXT);
                            getAutoFilledCompanyName.setValue(value);
                        }
                    }else {
                        formFieldValue.setValue(companyName);
                    }
                    formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), companyName));

                }
            }
        }, MONTHLY_INCOME {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType) {
                if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getMonthlyIncome() != null) {
                    String monthlyIncome = String.valueOf(reviewUserDataSourceResponse.getProfile().getMonthlyIncome());
                    PriceTextBoxFormFieldValueV0 priceTextBoxFormFieldValueV0 = (PriceTextBoxFormFieldValueV0) formFieldValue;
                    priceTextBoxFormFieldValueV0.setValue(monthlyIncome);
                    priceTextBoxFormFieldValueV0.setTracking(WidgetTransformerUtils.getTrackingMetadata(priceTextBoxFormFieldValueV0.getName(), monthlyIncome));
                    log.info("Prefilling MonthlyIncome from Profile for SM_USER_ID {}", reviewUserDataSourceResponse.getProfile().getSmUserId());
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), MONTHLY_INCOME_PREFILL_FROM_PROFILE)).mark();
                }
            }
        },  BONUS_OR_OTHER_INCOME {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType) {
                PriceTextBoxFormFieldValueV0 priceTextBoxFormFieldValueV0 = (PriceTextBoxFormFieldValueV0) formFieldValue;
                String bonusIncome = null;
                if(Objects.nonNull(reviewUserDataSourceResponse.getProfile().getBonusIncome())){
                    bonusIncome = String.valueOf(reviewUserDataSourceResponse.getProfile().getBonusIncome());
                }
                priceTextBoxFormFieldValueV0.setValue(bonusIncome);
                priceTextBoxFormFieldValueV0.setTracking(WidgetTransformerUtils.getTrackingMetadata(priceTextBoxFormFieldValueV0.getName(), bonusIncome));
                PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), BONUS_INCOME_PREFILL_FROM_PROFILE)).mark();
            }
        }, ANNUAL_TURNOVER {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType) {
                if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getAnnualTurnOver() != null) {
                    String annualTurnOver = String.valueOf(reviewUserDataSourceResponse.getProfile().getAnnualTurnOver());
                    PriceTextBoxFormFieldValueV0 priceTextBoxFormFieldValueV0 = (PriceTextBoxFormFieldValueV0) formFieldValue;
                    priceTextBoxFormFieldValueV0.setValue(annualTurnOver);
                    priceTextBoxFormFieldValueV0.setTracking(WidgetTransformerUtils.getTrackingMetadata(priceTextBoxFormFieldValueV0.getName(), annualTurnOver));
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), ANNUAL_TURNOVER_PREFILL_FROM_PROFILE)).mark();
                }
            }
        }, INCOME_SOURCE {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType) {
                if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getIncomeSource() != null) {
                    MultiDropdownFormFieldValue incomeSourceDropDownValue = (MultiDropdownFormFieldValue) formFieldValue;
                    String incomeSource = String.valueOf(reviewUserDataSourceResponse.getProfile().getIncomeSource());
                    incomeSourceDropDownValue.setValue(incomeSource);
                    incomeSourceDropDownValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(incomeSourceDropDownValue.getName(), incomeSource));
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), INCOME_SOURCE_PREFILL_FROM_PROFILE)).mark();
                }
            }
        }, INDUSTRY_TYPE {
            public void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType) {
                if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getIndustryType() != null) {
                    String industryType = reviewUserDataSourceResponse.getProfile().getIndustryType();
                    AutoSuggestFormFieldValue getAutoFilledIndustryType = (AutoSuggestFormFieldValue) formFieldValue;
                    DropdownValue value = new DropdownValue();
                    if(reviewUserDataSourceResponse.getProfile().getIndustryId() != null){
                        value.setTitle(industryType);
                        value.setId(reviewUserDataSourceResponse.getProfile().getIndustryId());
                        value.setType(PLAIN_TEXT);
                        getAutoFilledIndustryType.setValue(value);
                    }
                    formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), industryType));
                }
            }
        };

        public abstract void prefill(ReviewUserDataSourceResponse reviewUserDataSourceResponse, FormFieldValue formFieldValue, Decrypter decrypter, String employmentType);
    }

    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        try {
            Map<String, String> reviewPage1 = (Map<String, String>) applicationDataResponse.getApplicationData().get("reviewPage1");
            String employmentType = reviewPage1.get("employmentType");
            GenericFormWidgetData genericFormWidgetDataCopy = ObjectMapperUtil.get().readValue(getLenderForm(applicationDataResponse.getMerchantId(), employmentType), GenericFormWidgetData.class);
            PriceTextBoxFormFieldValueV0 PriceTextBoxFormFieldValueV0 = getPriceTextBoxFormFieldValueV0(genericFormWidgetDataCopy);
            updateSubmitButton(genericFormWidgetDataCopy.getSubmitButton());
            if (Objects.nonNull(reviewUserDataSourceResponse)) {
                prefillFirstInitialUserDataForm(reviewUserDataSourceResponse, genericFormWidgetDataCopy, employmentType);
            }
            if (selfEmployed.equals(employmentType)) {
                populateDropdown(genericFormWidgetDataCopy);
            }
            return genericFormWidgetDataCopy;
        } catch (Exception e) {
            log.error("Work Details build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

    private void prefillFirstInitialUserDataForm(ReviewUserDataSourceResponse reviewUserDataSourceResponse, GenericFormWidgetData genericFormWidgetData, String employmentType) {

        List<RenderableComponent<FormFieldValue>> renderableComponents = Objects.requireNonNull(genericFormWidgetData.getRenderableComponents());

        for (RenderableComponent<FormFieldValue> renderableComponent : renderableComponents) {
            if (formFieldMap.containsKey(renderableComponent.getValue().getName())) {
                try {
                    formFieldMap.get(renderableComponent.getValue().getName()).prefill(reviewUserDataSourceResponse, renderableComponent.getValue(), decrypter, employmentType);
                } catch (Exception e) {
                    log.warn("Prefill form data in review page 1 got exception while prefilling for sm_user_id " + reviewUserDataSourceResponse.getProfile().getSmUserId() + " for form key " + renderableComponent.getValue().getName(), e);
                }

            }
        }
    }

    private void populateDropdown(GenericFormWidgetData genericFormWidgetDataCopy) {
        AutoSuggestFormFieldValue industryField = (AutoSuggestFormFieldValue) genericFormWidgetDataCopy.getRenderableComponents().get(0).getValue();
        if (!Objects.isNull(industryField)) {
            industryField.setData(getDropdownData());
        }
    }

    private PriceTextBoxFormFieldValueV0 getPriceTextBoxFormFieldValueV0(GenericFormWidgetData genericFormWidgetDataCopy) {
        List<RenderableComponent<FormFieldValue>> renderableComponents = genericFormWidgetDataCopy.getRenderableComponents();
        Optional<RenderableComponent<FormFieldValue>> formFieldValueRenderableComponent = renderableComponents.stream().filter(incomeField -> (incomeField.getValue().getName()).equals("income")).findAny();
        if (formFieldValueRenderableComponent.isPresent()) {
            return (PriceTextBoxFormFieldValueV0) formFieldValueRenderableComponent.get().getValue();
        }
        return null;
    }

    public List<DropdownValue> getDropdownData() {
        return industryTypeOptions.stream().map(input -> {
            DropdownValue dropdownValue = new DropdownValue(input.getId(), input.getTitle());
            dropdownValue.setType(PLAIN_TEXT);
            return dropdownValue;
        }).collect(Collectors.toList());
    }


    private void updateSubmitButton(SubmitButtonValue submitButton) {
        Action action = submitButton.getButton().getAction();
        action.setParams(reviewUserDataSourceResponse.getQueryParams());
        action.setEncryption(reviewUserDataSourceResponse.getEncryptionData());
    }

    private String getLenderForm(String merchantId, String employmentType) {
        FormConfig formConfig = new FormConfig(null, null, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(merchantId, dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(getFormJson(employmentType));
    }

    private String getFormJson(String employmentType) {
        if (salaried.equals(employmentType)) {
            return salariedFormJson;
        }
        return selfEmployedFormJson;
    }

    //todo: update this logic
//    private void updateIncome(PriceTextBoxFormFieldValueV0 PriceTextBoxFormFieldValueV0) {
//        PriceTextBoxFormFieldValueV0.setValue("25,000"); //todo impute income from experian
//    }
}
