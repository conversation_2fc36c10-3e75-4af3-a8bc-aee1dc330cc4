package com.flipkart.fintech.lending.orchestrator.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ActiveLoanRuleVariable {
    private String lender;
    private String applicationState;
    private Long lastUpdatedAt;
    private String lenderState;
    private String lenderSubState;
    private Integer daysPastDisbursal;
    private Boolean isDisbursed;
}
