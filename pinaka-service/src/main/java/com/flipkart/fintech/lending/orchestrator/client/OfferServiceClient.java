package com.flipkart.fintech.lending.orchestrator.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.lending.orchestrator.service.OfferComparator;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.pinaka.api.model.LeadObject;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import java.util.Optional;
import javax.inject.Named;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.CustomLog;

@CustomLog
public class OfferServiceClient {

  private static final String CURRENT_OFFERS_PATH = "/offers/v1/current_offers";
  private static final String PRE_APPROVED_OFFERS_PATH = "/offers/v1/pre_approved_offers";
  private static final String X_USER_ID = "X-User-Id";
  private static final String X_MERCHANT_ID = "X-Merchant-Id";
  private static final String X_SM_USER_ID    = "X-SM-User-Id";

  private final WebTarget webTarget;

  @Inject
  public OfferServiceClient(@Named("offerServiceTarget") WebTarget webTarget) {
    this.webTarget = webTarget;
  }

  public Optional<LenderOfferEntity> getPreApprovedOffer(MerchantUser merchantUser) {
    Response response = webTarget
        .path(PRE_APPROVED_OFFERS_PATH)
        .request()
        .header(X_USER_ID, merchantUser.getMerchantUserId())
        .header(X_SM_USER_ID, merchantUser.getSmUserId())
        .header(X_MERCHANT_ID, merchantUser.getMerchantKey())
        .get();
    if (response.getStatusInfo().equals(Response.Status.OK)) {
      return Optional.of(response)
          .map(res -> res.readEntity(LenderOfferEntity.class));
    } else if (response.getStatusInfo().equals(Status.NO_CONTENT)) {
      return Optional.empty();
    } else {
      log.info("Got Error while calling offer service: {}", response.readEntity(String.class));
      PinakaMetricRegistry.getMetricRegistry().meter(
          MetricRegistry.name(OfferComparator.class, "ext_offer_service", "failed")).mark();
      throw new RuntimeException("Error while getting offer from external service");
    }
  }

  // add timer context inside try with resources.
  public LenderOfferEntity getOfferFromOfferService(LeadObject leadDetails,
      MerchantUser merchantUser)
      throws JsonProcessingException {
    ObjectMapper objectMapper = new ObjectMapper();
    String requestStr = objectMapper.writeValueAsString(leadDetails);
    Response response = webTarget
        .path(CURRENT_OFFERS_PATH)
        .request()
        .header(X_USER_ID, merchantUser.getMerchantUserId())
        .header(X_SM_USER_ID, merchantUser.getSmUserId())
        .header(X_MERCHANT_ID, merchantUser.getMerchantKey())
        .post(Entity.entity(requestStr, MediaType.APPLICATION_JSON));
    return Optional.of(response)
        .map(res -> res.readEntity(LenderOfferEntity.class))
        .orElseThrow(() -> {
          log.info("Got Error while calling offer service: {}", response.readEntity(String.class));
          PinakaMetricRegistry.getMetricRegistry().meter(
              MetricRegistry.name(OfferComparator.class, "ext_offer_service", "failed")).mark();
          return new RuntimeException("Error while getting offer from external service");
        });
  }
}
