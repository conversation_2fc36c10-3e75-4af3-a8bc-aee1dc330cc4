package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;

public interface LendingOrchestrator {

  PageActionResponse getHomePageV2(LendingPageRequest pageRequest, String merchantId, String userAgent, String requestId) throws PinakaException;

  PageActionResponse getMaintenancePage(String merchantId, String requestId);

  PageActionResponse getStatus(LendingPageRequest pageRequest, String merchantId, String requestId, String userAgent) throws PinakaException;

    PageActionResponse tryWithAnotherLender(String merchantId, String requestId,
        ResumePageRequest pageRequest,
        String userAgent) throws PinakaException;
}
