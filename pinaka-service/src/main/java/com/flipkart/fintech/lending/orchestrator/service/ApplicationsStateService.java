package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.lead.service.LeadService;
import com.flipkart.fintech.lending.orchestrator.model.ApplicationsStateDetails;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import javax.inject.Inject;

public class ApplicationsStateService {

  private final ApplicationService applicationService;
  private final LeadService leadService;

  @Inject
  public ApplicationsStateService(ApplicationService applicationService, LeadService leadService) {
    this.applicationService = applicationService;
    this.leadService = leadService;
  }

  public ApplicationsStateDetails getApplicationsStateDetails(MerchantUser merchantUser)
      throws PinakaException {
    return ApplicationsStateDetails.builder()
        .plApplicationDataResponse(
            applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.PERSONAL_LOAN)
                .orElse(null))
        .leadApplicationDataResponse(leadService.findActiveApplication(merchantUser).orElse(null))
        .build();
  }

}
