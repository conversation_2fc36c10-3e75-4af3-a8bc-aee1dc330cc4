package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.citadel.api.models.ActiveApplicationsResponse;
import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.ruleengine.models.PlLandingPageStates;

public interface LandingPageOrchestrator {
    PlLandingPageStates resolveLandingPage(LendingPageRequest landingPageRequest, String merchantId, String userAgent, String requestId,
                                           ActiveApplicationsResponse activeApplicationsResponse) throws PinakaException;
}
