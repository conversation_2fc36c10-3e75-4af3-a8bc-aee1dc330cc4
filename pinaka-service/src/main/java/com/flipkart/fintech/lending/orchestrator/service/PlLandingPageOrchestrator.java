package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.citadel.api.models.ActiveApplicationsResponse;
import com.flipkart.fintech.citadel.api.models.ApplicationData;
import com.flipkart.fintech.lending.orchestrator.model.ActiveLoanRuleVariable;
import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.pinaka.common.metricRegistry.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.LmsService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.ruleengine.landingPageOrchestrator.ExpressionEvaluator;
import com.flipkart.fintech.pinaka.service.ruleengine.models.Filter;
import com.flipkart.fintech.pinaka.service.ruleengine.models.PlLandingPageStates;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.*;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.LEAD;
import static com.flipkart.fintech.pinaka.api.enums.ProductType.PERSONAL_LOAN;


/**
 * <AUTHOR>
 * @date 14/10/24
 */
@CustomLog
public class PlLandingPageOrchestrator implements LandingPageOrchestrator{

    private final ExpressionEvaluator plLandingPageExpressionEvaluator;
    private final LinkedHashMap<PlLandingPageStates, List<Filter>> plLandingPageFilterRules;
    private final LmsService lmsService;

    @Inject
    public PlLandingPageOrchestrator(@Named("plLandingPageExpressionEvaluator") ExpressionEvaluator plLandingPageExpressionEvaluator,
                                     @Named("plLandingPageFilterRules") LinkedHashMap<PlLandingPageStates, List<Filter>> plLandingPageFilterRules, LmsService lmsService) {
        this.plLandingPageExpressionEvaluator = plLandingPageExpressionEvaluator;
        this.plLandingPageFilterRules = plLandingPageFilterRules;
        this.lmsService = lmsService;
    }

    @Override
    public PlLandingPageStates resolveLandingPage(LendingPageRequest landingPageRequest, String merchantId, String userAgent, String requestId,
                                                  ActiveApplicationsResponse activeApplicationsResponse) throws PinakaException {

        HashMap<String, Object> ruleEngineVariables = getRuleEngineVariables(activeApplicationsResponse, merchantId);

        for(Map.Entry<PlLandingPageStates, List<Filter>> pageStatesListEntry: plLandingPageFilterRules.entrySet()){
            List<Filter> rules = pageStatesListEntry.getValue();
            if(plLandingPageExpressionEvaluator.evaluate(rules, ruleEngineVariables)){
                return pageStatesListEntry.getKey();
            }
        }
        return PlLandingPageStates.RESUME_JOURNEY_LOAN_LP;
    }

    private HashMap<String, Object> getRuleEngineVariables(ActiveApplicationsResponse activeApplicationsResponse, String merchantId) throws PinakaException {

        HashMap<String, Object> ruleEngineVariables = new HashMap<>();
        ruleEngineVariables.put("merchantId", merchantId);
        List<ApplicationData> applicationDataList = activeApplicationsResponse.getApplications();
        ApplicationData leadData = applicationDataList.stream().filter(applicationData -> applicationData.getProductType().equals(LEAD.name())).findFirst().orElse(null);

        //todo: migrate workflows in such a way that only one active loan application can exist in citadel and revisit this logic
        List<ApplicationData>  loansData = applicationDataList.stream().filter(applicationData -> applicationData.getProductType().equals(PERSONAL_LOAN.name())).collect(Collectors.toList());


        if(Objects.nonNull(leadData)){
            ruleEngineVariables.put("leadState", leadData.getWorkflowState());
            ruleEngineVariables.put("leadLastUpdatedAt", leadData.getUpdatedAt());
        }

        if(!loansData.isEmpty()){
            /**
             *  assuming only one loan can exist which is not disbursed
             *  So ruleEngineVariables will contain two keys - ongoing_loan with value activeLoanVariable, and disbursed_loan with value list of activeLoanVariables
             */
            List<ActiveLoanRuleVariable> ongoingLoansRuleVariables = new ArrayList<>();
            List<ActiveLoanRuleVariable> disbursedLoanRuleVariables = new ArrayList<>();
            for(ApplicationData applicationData : loansData){
                ActiveLoanRuleVariable activeLoanRuleVariable = new ActiveLoanRuleVariable();
                activeLoanRuleVariable.setLender(applicationData.getLender());
                activeLoanRuleVariable.setLenderState(applicationData.getLenderState());
                activeLoanRuleVariable.setLenderSubState(applicationData.getLenderSubState());
                activeLoanRuleVariable.setApplicationState(applicationData.getWorkflowState());
                activeLoanRuleVariable.setLastUpdatedAt(applicationData.getUpdatedAt());
                int dpd = getDaysPastDisbursal(applicationData, activeApplicationsResponse.getSmUserId());
                activeLoanRuleVariable.setDaysPastDisbursal(dpd);
//                applicationData.put("loanLenderState", )
                if(dpd>0){
                    activeLoanRuleVariable.setIsDisbursed(true);
                    disbursedLoanRuleVariables.add(activeLoanRuleVariable);
                } else {
                    activeLoanRuleVariable.setIsDisbursed(false);
                    ongoingLoansRuleVariables.add(activeLoanRuleVariable);
                }
            }
            /**
             * there can be multiple disbursed loans, but only one ongoing loan application
             * in case of multiple disbursed loans, we want to only work with latest disbursed loan
             */
            if(ongoingLoansRuleVariables.size()>1){
                PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_MULTIPLE_ONGOING_APPLICATIONS").mark();
                // throw error
                throw new PinakaException("Multiple ongoing applications found for user: "+ activeApplicationsResponse.getSmUserId());
            }
            ActiveLoanRuleVariable latestDisbursedLoanVariable = null;
            if(!disbursedLoanRuleVariables.isEmpty()){
                latestDisbursedLoanVariable = disbursedLoanRuleVariables.stream().min(Comparator.comparingLong(ActiveLoanRuleVariable::getDaysPastDisbursal)).get();
            }
            ruleEngineVariables.put("ongoingLoanParams", ObjectMapperUtil.get().convertValue(ongoingLoansRuleVariables.stream().findFirst().orElse(null), HashMap.class));
            ruleEngineVariables.put("disbursedLoanParams", ObjectMapperUtil.get().convertValue(latestDisbursedLoanVariable, HashMap.class));
        }

        return ruleEngineVariables;
    }

    // this method should later get this data from lms
    private Integer getDaysPastDisbursal(ApplicationData applicationData, String smUserId){
        return lmsService.getDaysPastDisbursal(applicationData.getLspApplicationId());
    }


}
