package com.flipkart.fintech.lending.orchestrator.service;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.PERSONAL_LOAN;
import static com.flipkart.fintech.pinaka.service.application.Constants.USER_DISCARD;
import static com.flipkart.fintech.pinaka.service.arsenal.UserAgentHelper.ANDROID_PLATFORM_STRING;
import static com.flipkart.fintech.pinaka.service.arsenal.UserAgentHelper.IOS_PLATFORM_STRING;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.ConsentConstants.*;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.APPLY_NOW;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.flipkart.ams.ApplicationService;
import com.flipkart.ams.ApplicationServiceV2;
import com.flipkart.de.entity.client.request.pl.DeviceChannel;
import com.flipkart.de.entity.client.request.pl.PlDiscoveryAccountDO;
import com.flipkart.de.entity.client.request.pl.PlDiscoveryDeviceDO;
import com.flipkart.de.entity.client.request.pl.PlDiscoveryRequest;
import com.flipkart.de.entity.decision.BusinessUsecaseContext;
import com.flipkart.de.entity.decision.recommendation.business.PlAction;
import com.flipkart.de.entity.decision.response.DecisionResponseEntity;
import com.flipkart.de.entity.decision.usecase.PlDiscoveryDecisionEntity;
import com.flipkart.fintech.citadel.api.models.ActiveApplicationsResponse;
import com.flipkart.fintech.lead.model.LeadResponse;
import com.flipkart.fintech.lead.service.LeadService;
import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.library.FeatureReport;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.arsenal.LenderHelper;
import com.flipkart.fintech.pinaka.service.core.LmsService;
import com.flipkart.fintech.pinaka.service.core.page.InvalidMerchantBehaviour;
import com.flipkart.fintech.pinaka.service.core.page.NonWhitelistedUserBehavior;
import com.flipkart.fintech.pinaka.service.core.v6.LoanHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.external.CriClient;
import com.flipkart.fintech.pinaka.service.ruleengine.models.PlLandingPageStates;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantCheckUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.winterfell.api.request.ApplicationResponse;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.mobile.parser.reponseobject.UserAgentResponse;
import com.google.common.collect.ImmutableMap;
import com.supermoney.ams.bridge.AmsBridge;

import java.util.*;
import java.util.stream.Collectors;
import javax.inject.Inject;

import com.supermoney.ams.bridge.repairer.FormRepairerUtil;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

@CustomLog
public class PersonalLoanOrchestrator implements LendingOrchestrator {

  private final static String REPEAT_LOAN_LANDING_PAGE_BANNER = "REPEAT_LOAN_LANDING_PAGE_BANNER";

  private final LoanHandler loanHandler;
  private final ApplicationService applicationService;
  private final ApplicationServiceV2 applicationServiceV2;
  private final AmsBridge amsBridge;
  private final LeadService leadService;
  private final OfferService offerService;
  private final CriClient maverickClient;
  private final ExperianFeatureService experianFeatureService;
  private final DynamicBucket dynamicBucket;
  private final ReadRepairDataService readRepairDataService;
  private final PlLandingPageOrchestrator plLandingPageOrchestrator;
  private final LmsService lmsService;
  private final MetricRegistry metricRegistry;

  @Inject
  public PersonalLoanOrchestrator(
          LoanHandler loanHandler,
          ApplicationService applicationService,
          ApplicationServiceV2 applicationServiceV2,
          AmsBridge amsBridge,
          LeadService leadService,
          OfferService offerService,
          CriClient maverickClient,
          ExperianFeatureService experianFeatureService,
          DynamicBucket dynamicBucket,
          ReadRepairDataService readRepairDataService,
          PlLandingPageOrchestrator plLandingPageOrchestrator, LmsService lmsService) {
    this.loanHandler = loanHandler;
    this.applicationService = applicationService;
    this.applicationServiceV2 = applicationServiceV2;
    this.amsBridge = amsBridge;
    this.leadService = leadService;
    this.offerService = offerService;
    this.maverickClient = maverickClient;
    this.experianFeatureService = experianFeatureService;
    this.dynamicBucket = dynamicBucket;
    this.readRepairDataService = readRepairDataService;
    this.plLandingPageOrchestrator = plLandingPageOrchestrator;
    this.lmsService = lmsService;
    this.metricRegistry = PinakaMetricRegistry.getMetricRegistry();
  }

  @Override
  public PageActionResponse getHomePageV2(LendingPageRequest pageRequest, String merchantId, String userAgent, String requestId) throws PinakaException {
    MerchantUser merchantUser = MerchantUser.getMerchantUser(merchantId, pageRequest.getAccountId(), pageRequest.getSmUserId());

    if (REPEAT_LOAN_LANDING_PAGE_BANNER.equals(pageRequest.getSource())) {
      log.info("Showing second lead page for user: {}", merchantUser.getMerchantUserId());
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(PersonalLoanOrchestrator.class,
              "secondLeadPage")).mark();
      return getLeadPageActionResponse(requestId, userAgent, merchantUser);
    }

    ActiveApplicationsResponse activeApplicationsResponse = applicationService.findActiveApplicationsForProductTypeV2(merchantUser, null);

    PlLandingPageStates pageState = plLandingPageOrchestrator.resolveLandingPage(pageRequest, merchantId, userAgent, requestId, activeApplicationsResponse);
    switch (pageState){
      case REPEAT_LOAN_LP:
        return pageState.getPageActionResponse();
      default:
        Optional<ApplicationDataResponse> activeApplication = applicationService.getLatestActiveApplicationFromList(
                activeApplicationsResponse.getApplications().stream().filter(applicationData -> applicationData.getProductType().equals(PERSONAL_LOAN.name()))
                        .collect(Collectors.toList()), merchantUser, PERSONAL_LOAN);
        return getStatusV2(pageRequest, merchantUser, requestId, userAgent, activeApplication);
    }

  }

  public PageActionResponse getStatusV2(LendingPageRequest pageRequest, MerchantUser merchantUser,
                                        String requestId, String userAgent, Optional<ApplicationDataResponse> activeApplication) throws PinakaException {
    Timer.Context timer = null;
    try {
      if (activeApplication.isPresent()) {
        ApplicationDataResponse applicationDataResponse = activeApplication.get();
        timer = metricRegistry.timer(APPLY_NOW + applicationDataResponse.getApplicationState()).time();
        log.warn(
                "Cannot create another application as there is already an active application for MerchantUser {}, "
                        +
                        "resuming current application {}", merchantUser,
                applicationDataResponse.getApplicationId());
        return resumeLoanJourney(pageRequest, merchantUser.getMerchantKey(), requestId, applicationDataResponse);
      }

      return getLeadPageActionResponse(requestId, userAgent, merchantUser);
    } finally {
      if(timer != null) {
        timer.stop();
      }
    }
  }

  @Override
  public PageActionResponse getMaintenancePage(String merchantId, String requestId) {
    return PageActionResponse.builder()
        .action(Action.builder()
            .url("/ams/v1/maintenance")
            .actionType(ActionType.NAVIGATION)
            .build())
        .actionSuccess(true)
        .build();
  }

  @Override
  public PageActionResponse getStatus(LendingPageRequest pageRequest, String merchantId,
      String requestId, String userAgent) throws PinakaException {
    MerchantUser merchantUser = MerchantUserUtils
        .getMerchantUser(pageRequest.getAccountId(), pageRequest.getSmUserId());
    Optional<ApplicationDataResponse> activeApplication;
    if(StringUtils.isNotEmpty(pageRequest.getApplicationId())){
      log.info("resuming application found in request: {}", pageRequest.getApplicationId());
       activeApplication = Optional.ofNullable(applicationService.fetchActiveApplicationData
              (merchantUser, pageRequest.getApplicationId()));
    } else{
      activeApplication = applicationService
              .findLatestActiveApplicationV2(merchantUser, PERSONAL_LOAN);
    }
    if (activeApplication.isPresent()) {
      ApplicationDataResponse applicationDataResponse = activeApplication.get();
      log.warn(
          "Cannot create another application as there is already an active application for MerchantUser {}, "
              +
              "resuming current application {}", merchantUser,
          applicationDataResponse.getApplicationId());
      return resumeLoanJourney(pageRequest, merchantId, requestId, applicationDataResponse);
    }

    return getLeadPageActionResponse(requestId, userAgent, merchantUser);
  }

  @Override
  public PageActionResponse tryWithAnotherLender(String merchantId, String requestId, ResumePageRequest pageRequest,
      String userAgent) throws PinakaException {
    MerchantUser merchantUser = MerchantUser.getMerchantUser(merchantId, pageRequest.getAccountId(), pageRequest.getSmUserId());
    // try user_reject current application
    doUserDiscard(merchantUser, pageRequest.getApplicationId());
    Boolean isDiscardedSuccessfully = applicationService.discardApplication(merchantUser, pageRequest.getApplicationId());
    log.info("Retry with another lender, status being called: {}", pageRequest.getSmUserId());
    if (Boolean.TRUE.equals(isDiscardedSuccessfully)) {
      pageRequest.setApplicationId("");
    }
    return getStatus(LendingPageRequest.fromResumePageRequest(pageRequest), merchantId, requestId, userAgent);
  }

  private void doUserDiscard(MerchantUser merchantUser, String applicationId) throws PinakaException {
    ApplicationDataResponse response = applicationService.fetchApplicationData(merchantUser, applicationId);
			if (!amsBridge.isTerminalState(response)) {
					if(amsBridge.isUserDiscardAllowed(response)){
					FormSubmitRequest formSubmitRequest = FormRepairerUtil.getFormSubmitRequest(response);
					formSubmitRequest.setFormData(ImmutableMap.of(USER_DISCARD, true));
					ResumeApplicationRequest request = amsBridge.getResumeRequest(formSubmitRequest, response);
					applicationService.resumeApplication(merchantUser, applicationId, request);
					log.info("User discarded application :" + applicationId);
				} else {
					throw new PinakaException("Discard not allowed for this user :" + merchantUser.getMerchantUserId() +" state: "+response.getApplicationState());
				}
			}
	}

  private PageActionResponse getLeadPageActionResponse(String requestId, String userAgent,
      MerchantUser merchantUser)
      throws PinakaException {
    LeadResponse leadResponse;
    leadResponse = leadService.getCurrentLeadStatus(merchantUser, requestId);
    if (LeadDetails.LeadState.CREATE_PROFILE_END.equals(
        leadResponse.getLeadDetails().getStateOfLead())) {
      Map<String, Object> applicationData = leadResponse.getLeadDetails().getApplicationData();
      return getOfferAndInitiateLenderJourney(leadResponse.getLeadDetails(), merchantUser,
          requestId, applicationData, userAgent);
    }
    return leadResponse.getPageActionResponse();
  }

  private PageActionResponse getOfferAndInitiateLenderJourney(LeadDetails leadDetails,
      MerchantUser merchantUser, String requestId, Map<String, Object> applicationData,
      String userAgent) throws PinakaException {

    PlAction.Value tnsScore = checkTnsScore(merchantUser, userAgent, leadDetails.getLeadId());

    if (isExperianEnabled(merchantUser.getMerchantUserId())) {
      log.info("fetching experian details for user {}", merchantUser.getMerchantUserId());
      FeatureReport featureReport = experianFeatureService.getFeatureReport(
          merchantUser.getMerchantUserId(), merchantUser.getSmUserId(),
          leadDetails.getUserProfile().getProfileId(), leadDetails.getLeadId(),getConsentInJson(leadDetails), leadDetails.getMonthlyIncome());
      if (!Objects.isNull(featureReport)) {
        leadDetails.setExperianData(featureReport.getFeatureMap());
        leadDetails.setExperianReport(featureReport.getReport());
      }
    }

    CreateApplicationRequest lenderApplication = offerService.getLenderApplication(leadDetails, merchantUser, requestId, tnsScore, applicationData);
    if (lenderApplication == null) {
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(PersonalLoanOrchestrator.class,
          "nonWhitelistedBehaviour_lenderApplicationNull")).mark();
      return NonWhitelistedUserBehavior.getPageActionResponse(merchantUser, leadDetails.getLeadId());
    }

    ApplicationDataResponse applicationDataResponse = applicationServiceV2.createApplication(lenderApplication);
    return loanHandler.getPageActionResponse(applicationDataResponse, requestId, merchantUser);
  }

  // reuse loanhandler.resume
  PageActionResponse resumeLoanJourney(LendingPageRequest pageRequest, String merchantId,
      String requestId, ApplicationDataResponse applicationDataResponse) throws PinakaException {
    MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(pageRequest.getAccountId(),
        pageRequest.getSmUserId());
    String applicationId = applicationDataResponse.getApplicationId();
    if (amsBridge.checkReadRepair(applicationDataResponse)) {
      ResumeApplicationRequest resumeApplicationRequest = amsBridge.getRepairRequest(
          applicationDataResponse,pageRequest.getAdditionalParams());
      applicationService.resumeApplication(merchantUser, applicationId, resumeApplicationRequest);
      applicationDataResponse = applicationService.fetchApplicationData(merchantUser,
          applicationId);
    }

    if (!MerchantCheckUtils.isMerchantAllowedForJourney(merchantId,
        applicationDataResponse.getMerchantId())) {
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(PersonalLoanOrchestrator.class,
          "nonWhitelistedBehaviour_invalidMerchant")).mark();
      return InvalidMerchantBehaviour.getPageActionResponse(merchantId, applicationDataResponse.getMerchantId(), applicationDataResponse.getApplicationId());
    }

    // persisting readRepairData
    readRepairDataService.persistData(merchantUser,applicationDataResponse);
    return loanHandler.getPageActionResponse(applicationDataResponse, requestId, merchantUser);
  }

  private boolean isExperianEnabled(String accountId) {
    return (dynamicBucket.getBoolean("isExperianEnabled")
        || (!Objects.isNull(dynamicBucket.getStringArray("experianEnabledAccounts")) &&
        dynamicBucket.getStringArray("experianEnabledAccounts").contains(accountId)));
  }

  public PlAction.Value checkTnsScore(MerchantUser merchantUser, String userAgent, String leadId) {
    try (Timer.Context ignored = PinakaMetricRegistry.getMetricRegistry()
        .timer(MetricRegistry.name(PersonalLoanOrchestrator.class, "checkTnsScore")).time()) {
      if (isTNSScoreCheckNeeded(merchantUser)) {
        return PlAction.Value.NO_DECISION;
      }
      UserAgentResponse userAgentResponse = LenderHelper.GetUserAgentResponse(userAgent);
      if (Objects.isNull(userAgentResponse)) {
        log.info("Returning No_decision!!, no user agent");
        return PlAction.Value.NO_DECISION;
      }
      String clientRefId = String.valueOf(UUID.randomUUID());

      PlDiscoveryRequest plDiscoveryRequest = getTnsRequest(merchantUser.getMerchantUserId(),
          userAgentResponse, clientRefId);

      DecisionResponseEntity tnsDetails = maverickClient.getTNSDetails(plDiscoveryRequest);
      PlDiscoveryDecisionEntity decisionEntity = (PlDiscoveryDecisionEntity)
          tnsDetails.getDecisionMap().get(BusinessUsecaseContext.PL_DISCOVERY_SUPERMONEY);
      log.info("TNS score for user {} is {}",
          merchantUser.getMerchantUserId(),
          decisionEntity.getDecisionEntityMap().get(clientRefId).getRecommendation().getAction()
              .getValue());
      PlAction.Value response = decisionEntity.getDecisionEntityMap().get(clientRefId)
          .getRecommendation().getAction().getValue();

      if (Objects.isNull(response)) {
        return PlAction.Value.NO_DECISION;
      }
      return response;
    } catch (Exception e) {
      log.error("Exception in fetching TNS score for account ID: {}",
          merchantUser.getMerchantUserId());
      log.error("Returning No_decision!!");
    }
    return PlAction.Value.NO_DECISION;
  }

  private PlDiscoveryRequest getTnsRequest(String accountId, UserAgentResponse userAgentResponse,
      String clientRefId) {
    PlDiscoveryAccountDO plDiscoveryAccountDO = new PlDiscoveryAccountDO();
    plDiscoveryAccountDO.setAccountId(accountId);
    PlDiscoveryRequest plDiscoveryRequest = new PlDiscoveryRequest();
    plDiscoveryRequest.setClientReferenceId(clientRefId);
    plDiscoveryRequest.setAccountDetails(plDiscoveryAccountDO);

    PlDiscoveryDeviceDO plDiscoveryDeviceDO = new PlDiscoveryDeviceDO();
    plDiscoveryDeviceDO.setChannel(DeviceChannel.MSITE);
    Boolean idSupported = userAgentResponse.appDevice.idSupported;
    if (idSupported) {
      plDiscoveryDeviceDO.setDeviceId(userAgentResponse.appDevice.id);
      String osType = userAgentResponse.os.family;
      if (ANDROID_PLATFORM_STRING.equalsIgnoreCase(osType) || IOS_PLATFORM_STRING.equalsIgnoreCase(
          osType)) {
        plDiscoveryDeviceDO.setChannel(DeviceChannel.valueOf(osType.toUpperCase()));
      }
    }

    plDiscoveryRequest.setDeviceDetails(plDiscoveryDeviceDO);
    return plDiscoveryRequest;
  }


  private boolean isTNSScoreCheckNeeded(MerchantUser merchantUser) {
    return !MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY.equals(merchantUser.getMerchantKey());
  }

  private Consent getConsentInJson(LeadDetails leadDetails) {
    Map<String, Object> basicDetails = new HashMap<>();
    try {
      if(leadDetails.getApplicationData().containsKey("namePage")){
        basicDetails = (Map<String, Object>) leadDetails.getApplicationData().get("namePage");
      }
      else {
        basicDetails = (Map<String, Object>) leadDetails.getApplicationData().get("basicDetails");
      }
      Map<String, Object> consentData = (basicDetails == null) ? null : (Map<String, Object>) basicDetails.get("consentData");

      if (consentData == null) {
        return null;
      }

      return new Consent() {{
        setTs(Long.parseLong(consentData.get(CURRENT_TIMESTAMP).toString()));
        setIp((String) consentData.get(USER_IP));
        setPurpose(LENDING);
        setProvided(true);
        setDeviceId((String) consentData.get(DEVICE_ID));
        setDeviceInfo((String) consentData.get(DEVICE_INFO));
      }};
    } catch (Exception e) {
      log.info("getting exception in consent parsing leadDetails {}", leadDetails.toString(), e.getMessage());
    }
    return null;
  }

  public Optional<ApplicationDataResponse> getOngoingLoanApplication(MerchantUser merchantUser) throws PinakaException {
    ActiveApplicationsResponse activeApplicationsResponse = applicationService.findActiveApplicationsForProductTypeV2(merchantUser, PERSONAL_LOAN);
    Optional<ApplicationDataResponse> activeApplication = applicationService.getLatestActiveApplicationFromList(
            activeApplicationsResponse.getApplications(), merchantUser, PERSONAL_LOAN);
    if(activeApplication.isPresent()){
      if(lmsService.getDaysPastDisbursal(activeApplication.get().getApplicationId())<0){
        return activeApplication;
      }
    }
    return Optional.empty();
  }
}
