package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.ams.ApplicationService;
import com.flipkart.ams.ApplicationServiceV2;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.util.Objects;

@CustomLog
public class ReadRepairDataServiceImpl implements ReadRepairDataService {
    private final ReadRequestDataBuilderFactory readRequestDataBuilderFactory;

    private final ApplicationService applicationService;
    private final ApplicationServiceV2 applicationServiceV2;

    @Inject
    public ReadRepairDataServiceImpl(ReadRequestDataBuilderFactory readRequestDataBuilderFactory,
                                     ApplicationService applicationService,
                                     ApplicationServiceV2 applicationServiceV2) {
        this.readRequestDataBuilderFactory = readRequestDataBuilderFactory;
        this.applicationService = applicationService;
        this.applicationServiceV2 = applicationServiceV2;
    }

    @Override
    public boolean persistData(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) {
        try {
            ReadRepairDataRequestBuilder repairDataRequestBuilder = readRequestDataBuilderFactory.get(applicationDataResponse);
            if(Objects.isNull(repairDataRequestBuilder)){
                log.info("Did not persist readRepair data");
                return false;
            }
            ResumeApplicationRequest resumeApplicationRequest = repairDataRequestBuilder.build(applicationDataResponse);
            applicationService.resumeApplication(merchantUser,applicationDataResponse.getApplicationId(),resumeApplicationRequest);
            log.info("Persisted readRepair data");
            return true;
        }
        catch (Exception ex){
            log.error("Persistence of readRepairData failed with error : {}",ex.getMessage());
        }
        return false;
    }

    @Override
    public ApplicationDataResponse persistDataAndReturn(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) {
        try {
            ReadRepairDataRequestBuilder repairDataRequestBuilder = readRequestDataBuilderFactory.get(applicationDataResponse);
            if(Objects.isNull(repairDataRequestBuilder)){
                log.info("Did not persist readRepair data");
                return applicationDataResponse; // Return original if no repair needed
            }
            ResumeApplicationRequest resumeApplicationRequest = repairDataRequestBuilder.build(applicationDataResponse);

            // Use the V2 version that returns the complete updated application data
            ApplicationDataResponse updatedData = applicationServiceV2.resumeApplication(
                    merchantUser, applicationDataResponse.getApplicationId(), resumeApplicationRequest);

            log.info("Persisted readRepair data and returned updated data");
            return updatedData;
        }
        catch (Exception ex){
            log.error("Persistence of readRepairData failed with error : {}",ex.getMessage());
            return applicationDataResponse; // Return original if persistence failed
        }
    }
}
