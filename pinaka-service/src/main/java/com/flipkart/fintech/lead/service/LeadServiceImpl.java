package com.flipkart.fintech.lead.service;

import static com.flipkart.fintech.pinaka.api.enums.Lender.RING;
import static com.flipkart.fintech.pinaka.common.utils.LeadUtils.getDoubleFromString;
import static com.flipkart.fintech.pinaka.service.application.Constants.PROFILE_BASIC_DETAILS_MATCH;
import static com.flipkart.fintech.pinaka.service.application.Constants.PROFILE_BASIC_DETAILS_MISMATCH;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.APPLY_NOW;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.HOMEPAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.*;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.flipkart.ams.ApplicationService;
import com.flipkart.ams.ApplicationServiceV2;
import com.flipkart.ams.LEAD_APPLICATION_TYPES;
import com.flipkart.fintech.lead.model.LeadResponse;
import com.flipkart.fintech.lead.model.PaOffer;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.model.UserScores;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.service.adaptor.v6.UniversalPersonalDetailsAdapter;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.page.NoActiveApplicationErrorBehavior;
import com.flipkart.fintech.pinaka.service.core.page.PageActionResponseHandler;
import com.flipkart.fintech.pinaka.service.core.v7.CreateLeadRequestFactory;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.helper.ApplicationUserInputData;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.lead.model.LeadV4DataGatheringResponse;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.response.ProfileCRUDResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.crisys.api.CremoScores;
import com.sumo.crisys.client.CrisysClientException;

import java.util.*;
import javax.inject.Inject;

import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;

@CustomLog
public class LeadServiceImpl implements LeadService {
    private final UniversalPersonalDetailsAdapter universalPersonalDetailsAdapter;
    private final CreateLeadRequestFactory createLeadRequestFactory;
    private final ApplicationService applicationService;
    private final ApplicationServiceV2 applicationServiceV2;
    private final PageActionResponseHandler pageActionResponseHandler;
    private final ProductType productType;
    private final ProfileService profileService;
    private final UserProfileScores userProfileScores;
    private final DynamicBucket dynamicBucket;
    private final Random random;
    private final MetricRegistry metricRegistry;
    private final LeadV4DataGatheringService leadV4DataGatheringService;

    @Inject
    public LeadServiceImpl(UniversalPersonalDetailsAdapter universalPersonalDetailsAdapter,
                           CreateLeadRequestFactory createLeadRequestFactory, ApplicationService applicationService,
                           PageActionResponseHandler pageActionResponseHandler, ProfileService profileService,
                           UserProfileScores userProfileScores, DynamicBucket dynamicBucket, Random random,
                           ApplicationServiceV2 applicationServiceV2, LeadV4DataGatheringService leadV4DataGatheringService) {
        this.universalPersonalDetailsAdapter = universalPersonalDetailsAdapter;
        this.createLeadRequestFactory = createLeadRequestFactory;
        this.applicationService = applicationService;
        this.applicationServiceV2 = applicationServiceV2;
        this.pageActionResponseHandler = pageActionResponseHandler;
        this.profileService = profileService;
        this.dynamicBucket = dynamicBucket;
        this.random = random;
        this.productType = ProductType.LEAD;
        this.userProfileScores = userProfileScores;
        this.metricRegistry = PinakaMetricRegistry.getMetricRegistry();
        this.leadV4DataGatheringService = leadV4DataGatheringService;
    }

    @Override
    public LeadResponse getCurrentLeadStatus(MerchantUser merchantUser, String requestId) throws PinakaException {
        Timer.Context timer  = null;

        try {
            Optional<ApplicationDataResponse> activeApplication = applicationService.findLatestActiveApplicationV2(
                merchantUser, productType);
            if (activeApplication.isPresent()) {
                ApplicationDataResponse applicationDataResponse = activeApplication.get();
                timer = metricRegistry.timer(APPLY_NOW + applicationDataResponse.getApplicationState()).time();
                String applicationId = applicationDataResponse.getApplicationId();
                String monthlyIncome;
                if (LV4Util.isLv4Application(applicationDataResponse) || LV3Util.isLv3Application(applicationDataResponse)) {
                    monthlyIncome = getMonthlyIncomeFromApplicationV3(applicationDataResponse);
                } else {
                    monthlyIncome = getMonthlyIncomeFromApplication(applicationDataResponse);
                }
                log.warn("Cannot create another lead as there is already an active lead for MerchantUser {}, " +
                    "resuming current lead {}", merchantUser, applicationId);
                ProfileDetailedResponse profile = null;
                if (LeadDetails.LeadState.valueOf(applicationDataResponse.getApplicationState()).equals(LeadDetails.LeadState.CREATE_PROFILE_END)) {
                    ProfileCRUDResponse profileCRUDResponse = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("createProfile"), ProfileCRUDResponse.class);
                    profile = profileService.getProfileById(profileCRUDResponse.getProfileId());
                    if (Objects.isNull(profile)) {
                        throw new PinakaException("Profile fetch by ID failed: " + profileCRUDResponse.getProfileId());
                    }
                    if(LV3Util.isLv3Application(applicationDataResponse) || LV4Util.isLv4Application(applicationDataResponse)) {
                        readRepairV3(profile, applicationDataResponse);
                    } else {
                        readRepair(profile, applicationDataResponse);
                    }
                }

                LeadDetails.LeadDetailsBuilder leadDetailsBuilder = LeadDetails.builder()
                        .userProfile(profile)
                        .leadId(applicationId)
                        .applicationData(applicationDataResponse.getApplicationData())
                        .stateOfLead(LeadDetails.LeadState.valueOf(applicationDataResponse.getApplicationState()))
                        .userScores(getUserScores(merchantUser, requestId));

                if (monthlyIncome != null && !monthlyIncome.isEmpty()) {
                    leadDetailsBuilder.monthlyIncome(Integer.parseInt(monthlyIncome));
                }

                LeadDetails leadDetails = leadDetailsBuilder.build();
                PageActionResponse pageActionResponse = getPageActionResponse(applicationDataResponse, requestId, merchantUser);
                return LeadResponse.builder().leadDetails(leadDetails)
                    .pageActionResponse(pageActionResponse)
                    .build();
            }
            timer = metricRegistry.timer(APPLY_NOW + HOMEPAGE).time();
            if (LV4Util.isLv4Enabled(dynamicBucket, merchantUser.getSmUserId())) {
                log.info("Creating LV4 lead for userId: {}", merchantUser.getSmUserId());
                return createPageResponseV4(requestId, merchantUser, productType);
            } else if( LV3Util.isLv3Enabled(dynamicBucket, merchantUser.getSmUserId())) {
                log.info("Creating LV3 lead for userId: {}", merchantUser.getSmUserId());
                return createPageResponseV3(requestId, merchantUser, productType);
            } else {
                log.info("Creating LV2 or LV1 lead for userId: {}", merchantUser.getSmUserId());
                return createPageResponse(requestId, merchantUser, productType);
            }
        } catch (Exception e) {
            throw new PinakaException(e);
        } finally {
            if(timer != null) {
                timer.stop();
            }
        }
    }

    private String getMonthlyIncomeFromApplication(ApplicationDataResponse applicationDataResponse) {
        log.info("Inside getMonthlyIncomeFromApplication for userId: {}", applicationDataResponse.getSmUserId());
        Integer monthlyIncome  = 0;
        if (Objects.nonNull(applicationDataResponse.getApplicationData())){
            Map<String, Object> incomeDetails = new HashMap<>();
            if(applicationDataResponse.getApplicationData().containsKey("reviewPage2")){
                incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("reviewPage2"), Map.class);
            }
            if (Objects.nonNull(incomeDetails) && Objects.nonNull(incomeDetails.get("income"))){
                try {
                    monthlyIncome = Integer.parseInt((String) incomeDetails.getOrDefault("income", 0));
                    if(Objects.nonNull(incomeDetails.get("bonusIncome"))){
                        monthlyIncome = monthlyIncome + Integer.parseInt((String)incomeDetails.get("bonusIncome"));
                    }
                } catch (NumberFormatException e) {
                    log.warn("Number format exception while parsing income for applicationId : {}, and bonusIncome {} and monthlyIncome", applicationDataResponse.getApplicationId(),
                        incomeDetails.getOrDefault("bonusIncome", "-1"), incomeDetails.getOrDefault("income", "-1"));
                }
            }
        }
        return monthlyIncome.toString();
    }

    private String getMonthlyIncomeFromApplicationV3(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        log.info("Inside Get getMonthlyIncomeFromApplicationV3 for userId: {}", applicationDataResponse.getSmUserId());
        int monthlyIncome = 0;
        if (Objects.nonNull(applicationDataResponse.getApplicationData())) {
            ApplicationUserInputData applicationResponseData = ApplicationUserInputData.getApplicationResponseData(applicationDataResponse.getApplicationData());
            if (StringUtils.isNotBlank(applicationResponseData.getIncome())) {
                try {
                    monthlyIncome = Integer.parseInt(applicationResponseData.getIncome());
                    if (StringUtils.isNotBlank(applicationResponseData.getBonusIncome())) {
                        monthlyIncome = monthlyIncome + Integer.parseInt(applicationResponseData.getBonusIncome());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Number format exception while parsing income for applicationId : {}, and bonusIncome {} and monthlyIncome {}", applicationDataResponse.getApplicationId(),
                            applicationResponseData.getBonusIncome(), applicationResponseData.getIncome());
                }
            }
        }
        return Integer.toString(monthlyIncome);
    }

    private void readRepair(ProfileDetailedResponse profileById, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        if (LV4Util.isLv4Application(applicationDataResponse) || LV3Util.isLv3Application(applicationDataResponse)) {
            readRepairV3(profileById, applicationDataResponse);
            return;
        }
        log.info("doing read repair for profile ID: {}", profileById);
        Map<String, Object> basicDetails = new HashMap<>();
        if(applicationDataResponse.getApplicationData().containsKey("basicDetails")){
           basicDetails  = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("basicDetails"), Map.class);
        } else{
            basicDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("reviewPage1"), Map.class);
        }
        Integer pincode;
        if(basicDetails.containsKey("pincodeDetails")){
            Map<String, Object> pincodeDetails = (Map<String, Object>) basicDetails.get("pincodeDetails");
            pincode = Integer.parseInt((String) pincodeDetails.get("pincode"));
        } else {
            pincode = Integer.parseInt((String) basicDetails.get("pincode"));
        }
        EmploymentType employmentType = EmploymentType.valueOf((String) basicDetails.get("employmentType"));
        String dob = (String) basicDetails.get("dob");
        if (
            !pincode.equals(profileById.getUserEnteredPincode()) ||
                !employmentType.equals(profileById.getEmploymentType()) ||
                !dob.equals(profileById.getDob())
        ) {
            LoggerFactory.getLogger("Profile_Mismatch").info("Mismatch data for profile_id:{},dob_profile:{},app_data_dob:{},pincode_profile:{},pincode_app_data:{},profile_employment_type:{},app_data_employment_type:{}", profileById.getProfileId(), profileById.getDob(), dob, profileById.getUserEnteredPincode(), pincode, profileById.getEmploymentType(), employmentType);
            profileById.setUserEnteredPincode(pincode);
            profileById.setEmploymentType(employmentType);
            profileById.setDob(dob);
            PinakaMetricRegistry.getMetricRegistry().meter(PROFILE_BASIC_DETAILS_MISMATCH).mark();
        } else {
            PinakaMetricRegistry.getMetricRegistry().meter(PROFILE_BASIC_DETAILS_MATCH).mark();
        }
    }

    private void readRepairV3(ProfileDetailedResponse profileById, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        log.info("Inside Get readRepairV3 for userId: {}", applicationDataResponse.getSmUserId());
        ApplicationUserInputData applicationUserInputData = ApplicationUserInputData.getApplicationResponseData(applicationDataResponse.getApplicationData());
        if (
                !applicationUserInputData.getPincode().equals(profileById.getUserEnteredPincode()) ||
                        !applicationUserInputData.getEmploymentType().equals(profileById.getEmploymentType()) ||
                        !applicationUserInputData.getDob().equals(profileById.getDob())
        ) {
            log.info("Mismatch data for profile_id:{},dob_profile:{},app_data_dob:{},pincode_profile:{},pincode_app_data:{},profile_employment_type:{},app_data_employment_type:{}",
                    profileById.getProfileId(), profileById.getDob(), applicationUserInputData.getDob(), profileById.getUserEnteredPincode(),
                    applicationUserInputData.getPincode(), profileById.getEmploymentType(), applicationUserInputData.getEmploymentType());
            profileById.setUserEnteredPincode(applicationUserInputData.getPincode());
            profileById.setEmploymentType(applicationUserInputData.getEmploymentType());
            profileById.setDob(applicationUserInputData.getDob());
            PinakaMetricRegistry.getMetricRegistry().meter(PROFILE_BASIC_DETAILS_MISMATCH).mark();
        } else {
            PinakaMetricRegistry.getMetricRegistry().meter(PROFILE_BASIC_DETAILS_MATCH).mark();
        }
    }

    private LeadResponse createPageResponse(String requestId, MerchantUser merchantUser,
                                            ProductType productType) throws PinakaException {
        log.info("Inside createPageResponse for userId: {}", merchantUser.getSmUserId());
        CreateApplicationRequest request;
        int leadV2chance = random.nextInt(100);
        if(dynamicBucket.getStringArray(LEAD_V2_WL_USERS).contains(merchantUser.getSmUserId()) || leadV2chance<dynamicBucket.getInt(LEAD_V2_TRAFFIC)){
            request = createLeadRequestFactory.create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD_V2.name());
        } else {
            request = createLeadRequestFactory.create(merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD.name());
        }

        return getLeadResponse(requestId, merchantUser, request);
    }

    private LeadResponse createPageResponseV3(String requestId, MerchantUser merchantUser,
        ProductType productType) throws PinakaException {
        log.info("Inside createPageResponseV3 for userId: {}", merchantUser.getSmUserId());
        CreateApplicationRequest request;
        request = createLeadRequestFactory.create(merchantUser, productType, requestId,  LEAD_APPLICATION_TYPES.LEAD_V3.name());
        return getLeadResponse(requestId, merchantUser, request);
    }

    private LeadResponse createPageResponseV4(String requestId, MerchantUser merchantUser,
        ProductType productType) throws PinakaException {
        log.info("Inside createPageResponseV4 for userId: {}", merchantUser.getSmUserId());

        // Gather user data (name + PA offer)
        LeadV4DataGatheringResponse dataResponse = leadV4DataGatheringService.gatherData(merchantUser, requestId);
        log.info("Data gathering completed for userId: {}, scenario: {}",
                merchantUser.getSmUserId(), dataResponse.getContentScenario());

        CreateApplicationRequest request = createLeadRequestFactory.create(
            merchantUser, productType, requestId, LEAD_APPLICATION_TYPES.LEAD_V4.name());

        // Store PA offer data in application if available
        if (dataResponse.getPaOffer() != null) enrichRequestWithPaOffer(request, dataResponse.getPaOffer());

        log.info("Creating lead response for userId: {}", merchantUser.getSmUserId());
        return getLeadResponse(requestId, merchantUser, request);
    }

    private LeadResponse getLeadResponse(String requestId, MerchantUser merchantUser, CreateApplicationRequest request) throws PinakaException {
        ApplicationDataResponse applicationDataResponse = applicationServiceV2.createApplication(request);

        LeadDetails leadDetails = LeadDetails.builder()
            .leadId(applicationDataResponse.getApplicationId())
            .applicationData(applicationDataResponse.getApplicationData())
            .stateOfLead(LeadDetails.LeadState.valueOf(applicationDataResponse.getApplicationState()))
            .build();

        PageActionResponse pageActionResponse = getPageActionResponse(applicationDataResponse, requestId, merchantUser);
        return LeadResponse.builder().pageActionResponse(pageActionResponse).leadDetails(leadDetails).build();
    }

    private void enrichRequestWithPaOffer(CreateApplicationRequest request,
        LenderOfferEntity paOffer) throws PinakaException {

        Map<String, Object> applicationData = request.getApplicationData();
        if (applicationData == null) {
            throw new PinakaException("Application data cannot be null when enriching with PA offer");
        }

    applicationData.put(PA_OFFER, new PaOffer(
            /*paOffer.getLender(), */RING,
            /*paOffer.getId(),*/"123",
            /*paOffer.getAmount(),*/60000L
    ));

        request.setApplicationData(applicationData);
    }

    @Override
    public Optional<ApplicationDataResponse> findActiveApplication(MerchantUser merchantUser)
        throws PinakaException {
        return applicationService.findLatestActiveApplicationV2(merchantUser, productType);
    }

    public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse,
        String requestId, MerchantUser merchantUser) throws PinakaException {
        if (Objects.isNull(applicationDataResponse)) {
            return NoActiveApplicationErrorBehavior.getPageActionResponse();
        }
        return pageActionResponseHandler.create(requestId, merchantUser, applicationDataResponse);
    }

    private UserScores getUserScores(MerchantUser merchantUser, String requestId) {
        Double binScore = userProfileScores.getUserBin(merchantUser, requestId);
        log.info("got user bin score {} for user {}", binScore, merchantUser);
        CremoScores cremoScores = fetchCremoScores(merchantUser);
        Double losV2CremoBand = cremoScores == null ? null : getDoubleFromString(cremoScores.losV2CremoBand());
        Double idfcCremoBand = cremoScores == null ? null : getDoubleFromString(cremoScores.idfcCremoBand());
        Double granularCremoBand = cremoScores == null ? null : getDoubleFromString(cremoScores.granularCremoBand());
        log.info("Got cremo scores for user {}: losV2CremoBand={}, idfcCremoBand={}, GranularCremoBand={}", merchantUser, losV2CremoBand, idfcCremoBand, granularCremoBand);
        return UserScores.builder()
                .binScore(binScore)
                .losV2CremoBand(losV2CremoBand)
                .idfcCremoBand(idfcCremoBand)
                .granularCremoBand(granularCremoBand)
                .build();
    }

    private CremoScores fetchCremoScores(MerchantUser merchantUser) {
        try {
            return userProfileScores.getCremoScore(merchantUser);
        } catch (CrisysClientException e) {
            log.error("Error fetching cremo score for user {}", merchantUser, e);
            return null;
        }
    }

}