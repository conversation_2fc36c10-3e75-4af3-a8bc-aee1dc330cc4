package com.flipkart.fintech;

public class FinancialCalculationIssue {
    public static void main(String[] args) {
        // Using double for financial calculations
        double value = 0.1; // 10 cents
        double sum = 0.0;

        // Add 0.1 to sum 1000 times
        for (int i = 0; i < 1000; i++) {
            sum += value;
        }

        // Expected result: 100.0
        System.out.println("Expected Sum: 100.0");
        System.out.println("Actual Sum using double: " + sum);

        // Correct approach using BigDecimal
        java.math.BigDecimal valueBD = new java.math.BigDecimal("0.1");
        java.math.BigDecimal sumBD = java.math.BigDecimal.ZERO;

        for (int i = 0; i < 1000; i++) {
            sumBD = sumBD.add(valueBD);
        }

        System.out.println("Actual Sum using BigDecimal: " + sumBD);
    }
}
