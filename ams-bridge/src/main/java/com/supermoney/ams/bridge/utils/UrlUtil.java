package com.supermoney.ams.bridge.utils;

import static com.supermoney.ams.bridge.utils.PinakaConstants.PLConstants.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.Operation;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.CustomLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

@CustomLog
public class UrlUtil {
    
    public static List<NameValuePair> getQueryParams(ApplicationDataResponse response) {
        Optional<PendingTask> pendingTask = getPendingTask(response);
        String encryptedToken = createEncryptedToken(response.getApplicationId());
        List<NameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair(APPLICATION_ID, response.getApplicationId()));
        list.add(new BasicNameValuePair(TOKEN, encryptedToken));
        if (pendingTask.isPresent()) {
            PendingTask task = pendingTask.get();
            list.add(new BasicNameValuePair(TASK_ID, task.getTaskId()));
            list.add(new BasicNameValuePair(TASK_KEY, task.getTaskKey()));
            list.add(new BasicNameValuePair(PROCESS_INSTANCE_ID, task.getProcessInstanceId()));
        }
        return list;
    }

    private static Optional<PendingTask> getPendingTask(ApplicationDataResponse response) {
        List<PendingTask> pendingTasks = response.getPendingTask();
        if (CollectionUtils.isEmpty(pendingTasks))
            return Optional.empty();
        return Optional.of(pendingTasks.get(0));
    }

    private static String createEncryptedToken(String applicationId) {
        Token token = Token.builder()
            .applicationId(applicationId)
            .operation(Operation.RESUME)
            .build();
        try {
            return TokenUtils.getEncryptedToken(token);
        } catch (JsonProcessingException e) {
            log.error("Unable to create token", e);
            throw new InvalidPayloadException("Unable to create token");
        }
    }

}
