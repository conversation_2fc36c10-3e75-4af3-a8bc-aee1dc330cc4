{"batches": [{"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "5c7b58941a34417e80c6c23a5b02253a1511c654be36a346bf0207fa5b9a66df"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-aapi-prod-585cb4d8f-d48g2"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java -Xms6144m -Xmx6144m -XX:+UseG1GC -verbose:gc -Xloggc:/usr/local/flipkart/advanz-api/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Dcom.sun.management.jmxremote.port=3335 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Dlog4j.configurationFile=/usr/local/flipkart/advanz-api/resources/external/log4j.xml -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -Dlog4j2.formatMsgNoLookups=true com.flipkart.poseidon.Poseidon /usr/local/flipkart/advanz-api/resources/external/bootstrap.xml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java"}}, {"key": "process.pid", "value": {"intValue": 15}}, {"key": "process.runtime.description", "value": {"stringValue": "Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 25.202-b08"}}, {"key": "process.runtime.name", "value": {"stringValue": "Java(TM) SE Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_202-b08"}}, {"key": "service.instance.id", "value": {"stringValue": "cb4e000e-2927-4b08-9421-1bd75662f917"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pl-aapi"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "d449a771ceb786b4", "parentSpanId": "0000000000000000", "traceState": "", "name": "POST /*", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974422000000, "endTimeUnixNano": 1748858975221435400, "attributes": [{"key": "user_agent.original", "value": {"stringValue": "Mozilla/5.0 (Linux; Android 13; CPH2341 Build/TP1A.220624.003) FKUA/Retail/2230200/Android/Mobile (OPPO/CPH2341/e7519cde434dadf5d9af28b411a78d91)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "network.peer.port", "value": {"intValue": 51366}}, {"key": "url.scheme", "value": {"stringValue": "https"}}, {"key": "thread.name", "value": {"stringValue": "qtp704432829-171"}}, {"key": "url.path", "value": {"stringValue": "/api/fpg/1/action/view"}}, {"key": "server.address", "value": {"stringValue": "127.0.0.1"}}, {"key": "client.address", "value": {"stringValue": "**********"}}, {"key": "network.peer.address", "value": {"stringValue": "127.0.0.1"}}, {"key": "http.route", "value": {"stringValue": "/*"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 171}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "5c7b58941a34417e80c6c23a5b02253a1511c654be36a346bf0207fa5b9a66df"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-aapi-prod-585cb4d8f-d48g2"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java -Xms6144m -Xmx6144m -XX:+UseG1GC -verbose:gc -Xloggc:/usr/local/flipkart/advanz-api/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Dcom.sun.management.jmxremote.port=3335 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Dlog4j.configurationFile=/usr/local/flipkart/advanz-api/resources/external/log4j.xml -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -Dlog4j2.formatMsgNoLookups=true com.flipkart.poseidon.Poseidon /usr/local/flipkart/advanz-api/resources/external/bootstrap.xml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java"}}, {"key": "process.pid", "value": {"intValue": 15}}, {"key": "process.runtime.description", "value": {"stringValue": "Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 25.202-b08"}}, {"key": "process.runtime.name", "value": {"stringValue": "Java(TM) SE Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_202-b08"}}, {"key": "service.instance.id", "value": {"stringValue": "cb4e000e-2927-4b08-9421-1bd75662f917"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pl-aapi"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "ea4789eb51c232bf", "parentSpanId": "d449a771ceb786b4", "traceState": "", "name": "romeServiceHttp.romeServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974422719500, "endTimeUnixNano": 1748858974449962200, "attributes": [{"key": "thread.id", "value": {"intValue": 911}}, {"key": "thread.name", "value": {"stringValue": "hystrix-romeServiceHttpRequest-194"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "ae059f6937bab88f", "parentSpanId": "d449a771ceb786b4", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974450335200, "endTimeUnixNano": 1748858974462371300, "attributes": [{"key": "thread.id", "value": {"intValue": 708}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-95"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "b77cc08cf64dda3e", "parentSpanId": "d449a771ceb786b4", "traceState": "", "name": "PINAKA_API_POOL.STATUS.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974464055800, "endTimeUnixNano": 1748858974984694800, "attributes": [{"key": "thread.id", "value": {"intValue": 1184}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-136"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "753f63e4bd8cfa30", "parentSpanId": "d449a771ceb786b4", "traceState": "", "name": "pageServiceClientViewHttp.pageServiceClientViewHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974987644700, "endTimeUnixNano": 1748858975008719000, "attributes": [{"key": "thread.id", "value": {"intValue": 1040}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pageServiceClientViewHttpRequest-99"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "a06dae9912a9db5d", "parentSpanId": "ea4789eb51c232bf", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974422742000, "endTimeUnixNano": 1748858974449826600, "attributes": [{"key": "server.address", "value": {"stringValue": "************"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 911}}, {"key": "thread.name", "value": {"stringValue": "hystrix-romeServiceHttpRequest-194"}}, {"key": "url.full", "value": {"stringValue": "http://************/2/session/validate"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "8f61ade399b846a7", "parentSpanId": "ae059f6937bab88f", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974450355200, "endTimeUnixNano": 1748858974462304300, "attributes": [{"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 708}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-95"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v1/merchant/ACC5384577CEC564678804964349097429C?merchant=FLIPKART&phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "4f52327c866d1f1c", "parentSpanId": "753f63e4bd8cfa30", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974987660300, "endTimeUnixNano": 1748858975008540400, "attributes": [{"key": "server.address", "value": {"stringValue": "***********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 1040}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pageServiceClientViewHttpRequest-99"}}, {"key": "url.full", "value": {"stringValue": "http://***********/pages/v1/page/perso-dynam-311d7/view/client/mobile_api"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "ee5d7fdbd6a7d874", "parentSpanId": "b77cc08cf64dda3e", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974464238800, "endTimeUnixNano": 1748858974490855200, "attributes": [{"key": "server.address", "value": {"stringValue": "**********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "thread.id", "value": {"intValue": 1184}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-136"}}, {"key": "url.full", "value": {"stringValue": "http://**********:80/v1/user-profile/ACCOUNTID/ACC5384577CEC564678804964349097429C/segmentsWithSegmentGroups?channel=NEO&sc=FK"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "27ab6e9ff8fff943", "parentSpanId": "b77cc08cf64dda3e", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974512744700, "endTimeUnixNano": 1748858974984597000, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 1184}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-136"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "0b9c1cd0da5c82ef", "parentSpanId": "d449a771ceb786b4", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975034659600, "endTimeUnixNano": 1748858975218627600, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 799}}, {"key": "thread.name", "value": {"stringValue": "data-aggregator-thread-34"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.hystrix-1.4", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "5129e999492eec9eaf42434170ca60d79c0e2b729d09e65d40c9362f71e30dd5"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-pinaka-prod-6c976d8d5-zxjzb"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3584m -Xmx5734m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-pinaka-preprod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G com.flipkart.fintech.pinaka.service.application.PinakaApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "15f3a10d-4c6c-4979-b674-e2c2ab7edc2d"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pinaka"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "d2e9f4e947f97dc7", "parentSpanId": "27ab6e9ff8fff943", "traceState": "", "name": "POST /pinaka/6/pl/apply-now", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974513000000, "endTimeUnixNano": 1748858974753457200, "attributes": [{"key": "network.peer.port", "value": {"intValue": 43238}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/apply-now"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/apply-now"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_202)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "cdb403f33983c05c", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974554886000, "endTimeUnixNano": 1748858974564837600, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}, {"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "bf38d463b6b6a43c", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974565490000, "endTimeUnixNano": 1748858974570606000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}, {"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "c19fe455b3496ae9", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974570818000, "endTimeUnixNano": 1748858974587459000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "bfae2468212537ff", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974655688200, "endTimeUnixNano": 1748858974670296000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://crisys-service.sm-crisys-prod.fkcloud.in/crisys/v1/user/credit/profile/cremo-score?accountId=SMAAAAB755C611740B18D5105B843E55575"}}, {"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "crisys-service.sm-crisys-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "46d71b1db01bf860", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974588071200, "endTimeUnixNano": 1748858974655118800, "attributes": [{"key": "url.full", "value": {"stringValue": "http://************:80/dexter/1/user-profile/fetch"}}, {"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "86ee5106dbf3da5e", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974670980000, "endTimeUnixNano": 1748858974716503300, "attributes": [{"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "6ce00ec340b9aea4", "parentSpanId": "d2e9f4e947f97dc7", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974716511000, "endTimeUnixNano": 1748858974753226800, "attributes": [{"key": "thread.id", "value": {"intValue": 2576}}, {"key": "thread.name", "value": {"stringValue": "dw-2576 - POST /pinaka/6/pl/apply-now"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "14dd60fa2d7412ff", "parentSpanId": "0b9c1cd0da5c82ef", "traceState": "", "name": "POST /pinaka/6/pl/fetch-bulk-data-v2", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858975076000000, "endTimeUnixNano": 1748858975218292200, "attributes": [{"key": "network.peer.port", "value": {"intValue": 56580}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_202)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "6a841b2329f8a320", "parentSpanId": "14dd60fa2d7412ff", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975096273400, "endTimeUnixNano": 1748858975108450600, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "f63826fe3a86e3e1", "parentSpanId": "14dd60fa2d7412ff", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858975108722200, "endTimeUnixNano": 1748858975127463000, "attributes": [{"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "2e3ceb4fba798673", "parentSpanId": "14dd60fa2d7412ff", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858975127486200, "endTimeUnixNano": 1748858975147233800, "attributes": [{"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "85e190951b4f446b", "parentSpanId": "14dd60fa2d7412ff", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858975180836000, "endTimeUnixNano": 1748858975217823700, "attributes": [{"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "82fd8c9af260c776", "parentSpanId": "f63826fe3a86e3e1", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975108773400, "endTimeUnixNano": 1748858975127444700, "attributes": [{"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.smUserId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.sm_user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "3b78a5b98d3a485c", "parentSpanId": "2e3ceb4fba798673", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975127523800, "endTimeUnixNano": 1748858975147219200, "attributes": [{"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.userId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "0eae726d07ba2371", "parentSpanId": "14dd60fa2d7412ff", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975147374000, "endTimeUnixNano": 1748858975151755300, "attributes": [{"key": "url.full", "value": {"stringValue": "http://usersvc-kaas-v1.sm-user-service-prod.fkcloud.in/userservice/v2/customer/SMAAAAB755C611740B18D5105B843E55575?piiTextType=PLAINTEXT"}}, {"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "usersvc-kaas-v1.sm-user-service-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "83c4d90eeb7baeba", "parentSpanId": "14dd60fa2d7412ff", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975151997400, "endTimeUnixNano": 1748858975180107500, "attributes": [{"key": "url.full", "value": {"stringValue": "http://sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in/sm/upi/user-svc/v1/upi-user/search"}}, {"key": "thread.id", "value": {"intValue": 29613}}, {"key": "thread.name", "value": {"stringValue": "dw-29613 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "02f09dd5b9ade753ae147f713ee977a4dae04b16ac06ebac24a5c7b9511628b2"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-winterfell-87758d947-pn8lb"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3072m -Xmx4915m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-winterfell-prod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties com.flipkart.fintech.winterfell.application.WinterFellApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "c2d87540-3fe9-4407-92c1-2b9331e629ec"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-winterfell"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "89d55c7f7d3c703d", "parentSpanId": "cdb403f33983c05c", "traceState": "", "name": "GET /fintech-winterfell/1/application/activeApplicationsResponse", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974555000000, "endTimeUnixNano": 1748858974563997000, "attributes": [{"key": "thread.id", "value": {"intValue": 120948}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-120948"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 53654}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}, {"key": "network.peer.address", "value": {"stringValue": "***********"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "***********"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "6dd5d7273591a16a", "parentSpanId": "bf38d463b6b6a43c", "traceState": "", "name": "GET /fintech-winterfell/1/application/activeApplicationsResponse", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974566000000, "endTimeUnixNano": 1748858974570410500, "attributes": [{"key": "thread.id", "value": {"intValue": 121987}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-121987"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 53654}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}, {"key": "network.peer.address", "value": {"stringValue": "***********"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "***********"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "dddfd3e6058b1ce4", "parentSpanId": "89d55c7f7d3c703d", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974555453000, "endTimeUnixNano": 1748858974563444700, "attributes": [{"key": "thread.id", "value": {"intValue": 120948}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}, {"key": "thread.name", "value": {"stringValue": "dw-120948 - GET /fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "acb976af9978bf68", "parentSpanId": "6dd5d7273591a16a", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974566288000, "endTimeUnixNano": 1748858974570026500, "attributes": [{"key": "thread.id", "value": {"intValue": 121987}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}, {"key": "thread.name", "value": {"stringValue": "dw-121987 - GET /fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "7c3a0fc9f7209069", "parentSpanId": "c19fe455b3496ae9", "traceState": "", "name": "GET /fintech-winterfell/1/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974572000000, "endTimeUnixNano": 1748858974587370000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 50412}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "network.peer.address", "value": {"stringValue": "***********"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "***********"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "844f9c9a54c3cfa8", "parentSpanId": "6a841b2329f8a320", "traceState": "", "name": "GET /fintech-winterfell/1/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858975097000000, "endTimeUnixNano": 1748858975108188200, "attributes": [{"key": "thread.id", "value": {"intValue": 108136}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-108136"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 47706}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "e9db137bead417b6", "parentSpanId": "7c3a0fc9f7209069", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974572533800, "endTimeUnixNano": 1748858974580153900, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - GET /fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "a56c9688cb616827", "parentSpanId": "844f9c9a54c3cfa8", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975097272300, "endTimeUnixNano": 1748858975104803600, "attributes": [{"key": "thread.id", "value": {"intValue": 108136}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}, {"key": "thread.name", "value": {"stringValue": "dw-108136 - GET /fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "978663d9efcf6175", "parentSpanId": "7c3a0fc9f7209069", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974581673200, "endTimeUnixNano": 1748858974585857000, "attributes": [{"key": "thread.id", "value": {"intValue": 108138}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108138 - GET /fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "ec087dd7329d156d", "parentSpanId": "844f9c9a54c3cfa8", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975105877200, "endTimeUnixNano": 1748858975107001300, "attributes": [{"key": "thread.id", "value": {"intValue": 108136}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-108136 - GET /fintech-winterfell/1/application/APP2505261559388157113517712241158838615"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "aaa696e8185c5cb37d40dd8f50f479e31b3b6e506f524c0df3d9ffd57c0d0d74"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-citadel-prod-659cc7b68c-tvpwh"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3338m -Xmx5341m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-citadel-prod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G com.flipkart.fintech.citadel.service.application.CitadelApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "9ad1531c-3a3d-4628-98a7-93f1120c91d1"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-citadel"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "ccab99671347158f", "parentSpanId": "dddfd3e6058b1ce4", "traceState": "", "name": "GET /fintech-citadel/2/application/getActiveApplications", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974557000000, "endTimeUnixNano": 1748858974563671300, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 56175}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 58472}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-56175"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "fc75af812bb3be06", "parentSpanId": "ccab99671347158f", "traceState": "", "name": "SELECT Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974558087200, "endTimeUnixNano": 1748858974562097400, "attributes": [{"key": "thread.id", "value": {"intValue": 56175}}, {"key": "thread.name", "value": {"stringValue": "dw-56175 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "082ec0e551e2e1e8", "parentSpanId": "ccab99671347158f", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974562228000, "endTimeUnixNano": 1748858974563378200, "attributes": [{"key": "thread.id", "value": {"intValue": 56175}}, {"key": "thread.name", "value": {"stringValue": "dw-56175 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "f186dd63e6794726", "parentSpanId": "fc75af812bb3be06", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974558122000, "endTimeUnixNano": 1748858974562048000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 56175}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Application as generatedAlias0 where generatedAlias0.userId=:param0 */ select applicatio0_.id as id1_0_, applicatio0_.created_at as created_2_0_, applicatio0_.data as data3_0_, applicatio0_.financial_provider as financia4_0_, applicatio0_.lead_id as lead_id5_0_, applicatio0_.lender_state as lender_s6_0_, applicatio0_.lender_sub_state as lender_s7_0_, applicatio0_.merchant as merchant8_0_, applicatio0_.product_type as product_9_0_, applicatio0_.state as state10_0_, applicatio0_.updated_at as updated11_0_, applicatio0_.user_id as user_id12_0_, applicatio0_.workflow_id as workflo13_0_ from application applicatio0_ where applicatio0_.user_id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-56175 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "9f56974f39ab059d", "parentSpanId": "acb976af9978bf68", "traceState": "", "name": "GET /fintech-citadel/2/application/getActiveApplications", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974567000000, "endTimeUnixNano": 1748858974569869600, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 51780}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "022bcb521271c059", "parentSpanId": "9f56974f39ab059d", "traceState": "", "name": "SELECT Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974567882000, "endTimeUnixNano": 1748858974568723200, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "f4b89c758f76b4af", "parentSpanId": "9f56974f39ab059d", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974568826000, "endTimeUnixNano": 1748858974569530600, "attributes": [{"key": "thread.id", "value": {"intValue": 23424}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "593906af9e7bf5ba", "parentSpanId": "022bcb521271c059", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974567925000, "endTimeUnixNano": 1748858974568645000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 23424}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Application as generatedAlias0 where ( generatedAlias0.userId=:param0 ) and ( generatedAlias0.productType=:param1 ) */ select applicatio0_.id as id1_0_, applicatio0_.created_at as created_2_0_, applicatio0_.data as data3_0_, applicatio0_.financial_provider as financia4_0_, applicatio0_.lead_id as lead_id5_0_, applicatio0_.lender_state as lender_s6_0_, applicatio0_.lender_sub_state as lender_s7_0_, applicatio0_.merchant as merchant8_0_, applicatio0_.product_type as product_9_0_, applicatio0_.state as state10_0_, applicatio0_.updated_at as updated11_0_, applicatio0_.user_id as user_id12_0_, applicatio0_.workflow_id as workflo13_0_ from application applicatio0_ where applicatio0_.user_id=? and applicatio0_.product_type=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-23424 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMAAAAB755C611740B18D5105B843E55575&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "2f79a470595987da", "parentSpanId": "e9db137bead417b6", "traceState": "", "name": "GET /fintech-citadel/2/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858974573000000, "endTimeUnixNano": 1748858974579103500, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 33018}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 39644}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-33018"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "3ac25bf60d0d0551", "parentSpanId": "a56c9688cb616827", "traceState": "", "name": "GET /fintech-citadel/2/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748858975099000000, "endTimeUnixNano": 1748858975103677200, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 33025}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 36500}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-33025"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "865862c4a492f5e5", "parentSpanId": "2f79a470595987da", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974574478600, "endTimeUnixNano": 1748858974576722700, "attributes": [{"key": "thread.id", "value": {"intValue": 33018}}, {"key": "thread.name", "value": {"stringValue": "dw-33018 - GET /fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "78359f0b8c222d8e", "parentSpanId": "2f79a470595987da", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858974576947700, "endTimeUnixNano": 1748858974578630000, "attributes": [{"key": "thread.id", "value": {"intValue": 33018}}, {"key": "thread.name", "value": {"stringValue": "dw-33018 - GET /fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "6e97f87f36842f4f", "parentSpanId": "3ac25bf60d0d0551", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858975100332300, "endTimeUnixNano": 1748858975101550600, "attributes": [{"key": "thread.id", "value": {"intValue": 33025}}, {"key": "thread.name", "value": {"stringValue": "dw-33025 - GET /fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "6c4cd41472625290", "parentSpanId": "3ac25bf60d0d0551", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748858975101692000, "endTimeUnixNano": 1748858975103243800, "attributes": [{"key": "thread.id", "value": {"intValue": 33025}}, {"key": "thread.name", "value": {"stringValue": "dw-33025 - GET /fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "07b98c2fdb6b0adf", "parentSpanId": "865862c4a492f5e5", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858974574538200, "endTimeUnixNano": 1748858974576633000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 33018}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-33018 - GET /fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "85909f472f073fa20f492b78697490e0", "spanId": "eabf301bb13d694e", "parentSpanId": "6e97f87f36842f4f", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748858975100378000, "endTimeUnixNano": 1748858975101511000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 33025}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-33025 - GET /fintech-citadel/2/application/APP2505261559388157113517712241158838615"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}]}