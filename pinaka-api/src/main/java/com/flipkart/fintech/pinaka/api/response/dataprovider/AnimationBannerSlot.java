package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimationBannerSlot extends Slot  {

    public AnimationBannerSlot() {
        super(SlotType.CBC_ANIMATION_BANNER);
    }

    private Action action;

    private StyledText title;

    private Image arrowIcon;

    private Image leftImage;

    private Image rightImage;

    private String bgColor;
}
