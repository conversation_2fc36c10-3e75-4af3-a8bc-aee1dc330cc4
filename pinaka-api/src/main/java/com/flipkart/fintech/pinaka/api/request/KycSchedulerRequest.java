package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.stratum.api.models.kycScheduler.KycSchedulerData;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KycSchedulerRequest {
    @ToString.Exclude
    private KycSchedulerData kycSchedulerData;
    private String source;
    private String status;
    private String description;
    @ToString.Exclude
    private Map<String, String> executiveDetails;
    private String schedulerPhase;
    private String externalId;
}
