package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.Application;
import com.flipkart.fintech.pinaka.api.model.LenderConfig;
import com.flipkart.fintech.pinaka.api.response.v3.ApplicationMetaData;
import com.flipkart.fintech.pinaka.api.response.v5.KycSchedulerDataResponse;
import com.flipkart.fintech.pinaka.api.response.v5.VkycApplicationDataResponse;
import com.flipkart.sensitive.annotation.SensitiveField;
import com.flipkart.fintech.pinaka.api.response.v4.NavigationActionInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Created by sujeetkumar.r on 06/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FetchApplicationResponse {

    @NotNull
    @JsonProperty("tracking_id")
    private String trackingId;

    @NotNull
    @JsonProperty("status")
    private ApplicationStatus status;

    @NotNull
    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("application_data")
    private Application applicationData;

    @NotNull
    @JsonProperty("product_type")
    private ProductType productType;

    @JsonProperty("aadhaar_number")
    @ToString.Exclude
    private String aadhaar;

    @JsonProperty("aadhaar_reference")
    @ToString.Exclude
    private String aadhaarReference;

    @JsonProperty("reject_reason")
    private String rejectReason;

    @JsonProperty("is_user_kyced")
    private boolean isKyced;

    @JsonProperty("external_id")
    private String externalId;

    @JsonProperty("lender")
    private Lender lender;

    @JsonProperty("is_upgrade_applicable")
    private boolean isUpgradeApplicable;

    @JsonProperty("is_enhanced_due_diligence_done")
    private boolean isEnhancedDueDiligenceDone;

    @JsonProperty("lenderConfig")
    private LenderConfig lenderConfig;

    @JsonProperty("cohortType")
    private String cohortType;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("lenderReferenceInfo")
    private Map<String, Object> lenderReferenceInfo;

    @JsonProperty("autopaySetupMandatory")
    private Boolean autopaySetupMandatory;

    @JsonProperty("autopayApplicable")
    private Boolean autopayApplicable;

    @JsonProperty("kycApplicationRefId")
    private String kycApplicationRefId;

    @JsonProperty("applicationMetaData")
    private ApplicationMetaData applicationMetaData;

    @JsonProperty("caseId")
    private String caseId;

    @JsonProperty("c1number")
    private String c1number;

    @JsonProperty("docRequired")
    private String docRequired;

    @JsonProperty("phoneNumber")
    @SensitiveField
    private String phoneNumber;

    @JsonProperty("borrowerId")
    private Long borrowerId;

    @JsonProperty("navigationActionInfo")
    private NavigationActionInfo navigationActionInfo;

    @JsonProperty("externalRefId")
    private String externalRefId;

    @JsonProperty("kycSchedulerDataResponse")
    private KycSchedulerDataResponse kycSchedulerDataResponse;

    @JsonProperty("vkycApplicationDataResponse")
    private VkycApplicationDataResponse vkycApplicationDataResponse;
}
