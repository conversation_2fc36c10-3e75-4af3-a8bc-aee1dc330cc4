package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

/**
 * Created by rajat.mathur on 07/01/19.
 */
public class AddressSelectionFormField extends FormField {

    //to be done abhishek : ask it is not being used anywhere
    private Image icon;
    private String title;

    public AddressSelectionFormField() {
        super(FormFieldType.ADDRESS);
    }

    public Image getIcon() {
        return icon;
    }

    public void setIcon(Image icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
