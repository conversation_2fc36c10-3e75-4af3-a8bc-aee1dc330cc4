package com.flipkart.fintech.pinaka.api.builders;

import com.flipkart.fintech.pinaka.api.exceptions.IllegalDocumentException;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document.DocumentTypes;
import com.flipkart.fintech.pinaka.api.request.v6.documents.KycImage;
import com.flipkart.fintech.pinaka.api.request.v6.documents.SelfieImage;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import java.io.IOException;
import java.io.InputStream;
import org.apache.commons.io.IOUtils;
import javax.validation.constraints.NotNull;

public class UploadDocumentRequestV6Builder implements Builder<UploadDocumentRequestV6> {
  private final UploadDocumentRequestV6 uploadDocumentRequest;

  public UploadDocumentRequestV6Builder(String encryptedSymmetricKey,
      String publicKeyRefId, InputStream documentInputStream, String documentPassword,
      String documentType) throws IOException {
    Document document = buildDocument(documentInputStream, documentPassword, documentType);
    EncryptionKeyData encryptionKeyData = buildEncryptionKeyData(encryptedSymmetricKey,
        publicKeyRefId);
    this.uploadDocumentRequest = new UploadDocumentRequestV6(document, encryptionKeyData);
  }

  @Override
  public UploadDocumentRequestV6 build() {
    return uploadDocumentRequest;
  }

  @NotNull
  private static Document buildDocument(InputStream documentInputStream,
      String documentPassword, String documentType) throws IOException {
    byte[] base64EncodedFileData = IOUtils.toByteArray(documentInputStream);
    if (DocumentTypes.SELFIE_IMAGE.equals(documentType)) {
      return new SelfieImage(base64EncodedFileData);
    }
    if (DocumentTypes.KYC_IMAGE.equals(documentType)) {
      return new KycImage(base64EncodedFileData);
    }
    throw new IllegalDocumentException(documentType);
  }

  @NotNull
  private static EncryptionKeyData buildEncryptionKeyData(String encryptedSymmetricKey,
      String publicKeyRefId) {
    return EncryptionKeyData.builder()
        .encKey(encryptedSymmetricKey)
        .encKeyRef(publicKeyRefId)
        .build();
  }
}
