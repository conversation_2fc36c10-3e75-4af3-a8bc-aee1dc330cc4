package com.flipkart.fintech.pinaka.api.request.v6;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 21/12/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WebhooksRequest {
    protected String lspApplicationId;
    protected String lenderApplicationId;
    protected String applicationStatus;
    protected Long stateTransitionTime;
}



