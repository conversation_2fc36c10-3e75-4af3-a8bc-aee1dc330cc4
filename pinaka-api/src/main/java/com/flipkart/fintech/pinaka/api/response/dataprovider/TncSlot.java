package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

/**
 * Created by kunal.keshwani on 10/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TncSlot extends Slot{

    public TncSlot() {
        super(SlotType.TNC);
    }

    private String tncContent;

    public String getTncContent() {
        return tncContent;
    }

    public void setTncContent(String tncContent) {
        this.tncContent = tncContent;
    }
}
