package com.flipkart.fintech.pinaka.api.response.v3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.ConsentType;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 22/05/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
public class OtpMetaData extends DataPointConsentMetaData {
    private Boolean enableResend;
    private Long    resendTimer;

    public OtpMetaData() {
        super(ConsentType.OTP.name());
    }
}
