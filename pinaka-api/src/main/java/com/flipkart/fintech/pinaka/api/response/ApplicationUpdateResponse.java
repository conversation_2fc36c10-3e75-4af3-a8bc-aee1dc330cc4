package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 11/09/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplicationUpdateResponse  {

    @JsonProperty
    private ApplicationStatus status;

    @JsonProperty("tracking_id")
    private String trackingId;

    @JsonProperty("reject_reason")
    private String rejectReason;

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}
