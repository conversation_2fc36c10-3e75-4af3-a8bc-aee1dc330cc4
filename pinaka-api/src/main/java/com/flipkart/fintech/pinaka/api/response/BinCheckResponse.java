package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.response.dataprovider.Image;

import java.util.Map;

/**
 * Created by kunal.keshwani on 27/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BinCheckResponse {
    private boolean binValid;
    private Image image;
    private String errorMessage;
    private Map<String, String> tracking;

    public boolean isBinValid() {
        return binValid;
    }

    public void setBinValid(boolean binValid) {
        this.binValid = binValid;
    }

    public Image getImage() {
        return image;
    }

    public void setImage(Image image) {
        this.image = image;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Map<String, String> getTracking() {
        return tracking;
    }

    public void setTracking(Map<String, String> tracking) {
        this.tracking = tracking;
    }

    @Override
    public String toString() {
        return "BinCheckResponse{" +
                "binValid=" + binValid +
                ", image=" + image +
                ", errorMessage='" + errorMessage + '\'' +
                ", tracking=" + tracking +
                '}';
    }
}
