package com.flipkart.fintech.pinaka.api.request.v6.useraction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.rome.datatypes.response.fintech.supermoney.responseContext.consent.BaseConsentResponse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import lombok.experimental.SuperBuilder;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FormSubmitRequest extends UserActionRequest {
  private Map<String, Object> formData;
  private Map<String, Object> consentData;
  private List<BaseConsentResponse> consentListData;
}