package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.model.UiMetadataSection;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LandingPageMetadataResponse {

    public LandingPageMetadataResponse() {
    }

    @JsonProperty("sections")
    List<UiMetadataSection> metadataSectionList;

    public LandingPageMetadataResponse(List<UiMetadataSection> metadataSectionList) {
        this.metadataSectionList = metadataSectionList;
    }

}
