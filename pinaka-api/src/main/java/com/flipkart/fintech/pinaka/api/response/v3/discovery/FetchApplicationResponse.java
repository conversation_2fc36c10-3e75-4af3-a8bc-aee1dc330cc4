package com.flipkart.fintech.pinaka.api.response.v3.discovery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 07/10/20.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class FetchApplicationResponse {
    private ProductType                     productType;
    private Lender                          financialProvider;
    private ApplicationStatus               state;
    private FetchUserEBCEligibilityResponse userEBCEligibilityResponse;
    private FetchUserCBCEligibilityResponse userCBCEligibilityResponse;
}
