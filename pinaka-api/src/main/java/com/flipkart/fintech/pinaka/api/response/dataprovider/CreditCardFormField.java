package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import com.flipkart.fintech.pinaka.api.enums.TextBoxInputType;

/**
 * Created by kunal.keshwani on 05/04/19.
 */
public class CreditCardFormField extends FormField {

    private String validationRegex;
    private String errorMessage;
    private String binCheckRegex;
    private TextBoxInputType inputType;
    private Integer length;
    private Action validationAction;
    private Image deactivatedAxisLogo;
    private Image activatedAxisLogo;
    private Image secureImage;

    public CreditCardFormField() {
        super(FormFieldType.CREDIT_CARD);
    }

    public String getValidationRegex() {
        return validationRegex;
    }

    public void setValidationRegex(String validationRegex) {
        this.validationRegex = validationRegex;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Action getValidationAction() {
        return validationAction;
    }

    public void setValidationAction(Action validationAction) {
        this.validationAction = validationAction;
    }

    public Image getDeactivatedAxisLogo() {
        return deactivatedAxisLogo;
    }

    public void setDeactivatedAxisLogo(Image deactivatedAxisLogo) {
        this.deactivatedAxisLogo = deactivatedAxisLogo;
    }

    public Image getActivatedAxisLogo() {
        return activatedAxisLogo;
    }

    public void setActivatedAxisLogo(Image activatedAxisLogo) {
        this.activatedAxisLogo = activatedAxisLogo;
    }

    public Image getSecureImage() {
        return secureImage;
    }

    public void setSecureImage(Image secureImage) {
        this.secureImage = secureImage;
    }

    public TextBoxInputType getInputType() {
        return inputType;
    }

    public void setInputType(TextBoxInputType inputType) {
        this.inputType = inputType;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public String getBinCheckRegex() {
        return binCheckRegex;
    }

    public void setBinCheckRegex(String binCheckRegex) {
        this.binCheckRegex = binCheckRegex;
    }

    @Override
    public String toString() {
        return "CreditCardFormField{" +
                "validationRegex='" + validationRegex + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", binCheckRegex='" + binCheckRegex + '\'' +
                ", inputType=" + inputType +
                ", length=" + length +
                ", validationAction=" + validationAction +
                ", deactivatedAxisLogo=" + deactivatedAxisLogo +
                ", activatedAxisLogo=" + activatedAxisLogo +
                ", secureImage=" + secureImage +
                '}';
    }
}
