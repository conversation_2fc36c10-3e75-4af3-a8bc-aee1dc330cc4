package com.flipkart.fintech.pinaka.api.request.dataprovider;


import com.flipkart.fintech.pinaka.api.enums.dataprovider.ConfigurationContext;

/**
 * Created by rajat.mathur on 09/12/18.
 */
public class ConfigurableParameter extends com.flipkart.fintech.pinaka.api.response.dataprovider.ConfigurableParameter {

    private String enumeratedValues;
    private ConfigurationContext configurationContext;

    public ConfigurableParameter() {
    }

    public String getEnumeratedValues() {
        return enumeratedValues;
    }

    public void setEnumeratedValues(String enumeratedValues) {
        this.enumeratedValues = enumeratedValues;
    }

    public ConfigurationContext getConfigurationContext() {
        return configurationContext;
    }

    public void setConfigurationContext(ConfigurationContext configurationContext) {
        this.configurationContext = configurationContext;
    }
}
