package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import lombok.Data;

@Data
public class RichActionableBannerSlot extends Slot {
    public RichActionableBannerSlot() {
        super(SlotType.RICH_ACTIONABLE_BANNER);
    }
    private RichText message;
    private RichText title;
    private Image icon;
    private RichText actionText;
    private String navigationUrl;

}
