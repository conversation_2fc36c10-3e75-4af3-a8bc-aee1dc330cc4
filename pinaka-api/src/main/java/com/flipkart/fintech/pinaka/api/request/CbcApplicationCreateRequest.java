package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.ProductType;

/**
 * <AUTHOR>
 * @since 17/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CbcApplicationCreateRequest extends ApplicationCreateRequest {

    public CbcApplicationCreateRequest() {
        super(ProductType.CBC);
    }

    private String utmSource;
    private String storeMerge;

    public String getUtmSource() {
        return utmSource;
    }

    public String getStoreMerge() {
        return storeMerge;
    }

    public void setStoreMerge(String storeMerge) {
        this.storeMerge = storeMerge;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }
}
