package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.ContainerStyle;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * Created by kunal.keshwani on 09/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class DisplayCreditCardSlot extends Slot{

    public DisplayCreditCardSlot(String viewType) {
        super(SlotType.DISPLAY_CREDIT_CARD,viewType);
    }

    public DisplayCreditCardSlot(){

    }

    public DisplayCreditCardSlot(SlotType type, String description, ProductType productType, Lender lender, Action expiredAction, CreditCardFront front, BlockCard blockCard, CreditCardBack back, Map<String, String> viewCardDetailsTracking, String token, Action showCardDetailsAction, Image chevron, boolean masked, String headerText, Map<String, String> headerPlaceholders) {
        super(type);
        this.description = description;
        this.productType = productType;
        this.lender = lender;
        this.expiredAction = expiredAction;
        this.front = front;
        this.blockCard = blockCard;
        this.back = back;
        this.viewCardDetailsTracking = viewCardDetailsTracking;
        this.token = token;
        this.showCardDetailsAction = showCardDetailsAction;
        this.chevron = chevron;
        this.masked = masked;
        this.headerText = headerText;
        this.headerPlaceholders = headerPlaceholders;
    }

    public DisplayCreditCardSlot(SlotType type, String viewType, String description, ProductType productType, Lender lender, Action expiredAction, CreditCardFront front, BlockCard blockCard, CreditCardBack back, Map<String, String> viewCardDetailsTracking, String token, Action showCardDetailsAction, Image chevron, boolean masked, String headerText, Map<String, String> headerPlaceholders) {
        super(type, viewType);
        this.description = description;
        this.productType = productType;
        this.lender = lender;
        this.expiredAction = expiredAction;
        this.front = front;
        this.blockCard = blockCard;
        this.back = back;
        this.viewCardDetailsTracking = viewCardDetailsTracking;
        this.token = token;
        this.showCardDetailsAction = showCardDetailsAction;
        this.chevron = chevron;
        this.masked = masked;
        this.headerText = headerText;
        this.headerPlaceholders = headerPlaceholders;
    }

    private String description;
    private ProductType productType;
    private Lender lender;
    private Action expiredAction;
    private CreditCardFront front;
    private BlockCard blockCard;
    private CreditCardBack back;
    private Map<String, String> viewCardDetailsTracking;
    private String token;
    private Action showCardDetailsAction;
    private Image chevron;
    private boolean masked;
    private String headerText;
    private Map<String, String> headerPlaceholders;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreditCardBack {
        private Image backGroundCard;
        private String viewCardFrontText;
        private Image leftArrow;

        public String getViewCardFrontText() {
            return viewCardFrontText;
        }

        public void setViewCardFrontText(String viewCardFrontText) {
            this.viewCardFrontText = viewCardFrontText;
        }

        public Image getLeftArrow() {
            return leftArrow;
        }

        public void setLeftArrow(Image leftArrow) {
            this.leftArrow = leftArrow;
        }

        public Image getBackGroundCard() {
            return backGroundCard;
        }

        public void setBackGroundCard(Image backGroundCard) {
            this.backGroundCard = backGroundCard;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreditCardFront {
        private String name;
        private Image backGroundCard;
        private String viewCardBackText;
        private Image rightArrow;
        private Action displayCCAction;

        public Image getBackGroundCard() {
            return backGroundCard;
        }

        public void setBackGroundCard(Image backGroundCard) {
            this.backGroundCard = backGroundCard;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getViewCardBackText() {
            return viewCardBackText;
        }

        public void setViewCardBackText(String viewCardBackText) {
            this.viewCardBackText = viewCardBackText;
        }

        public Image getRightArrow() {
            return rightArrow;
        }

        public void setRightArrow(Image rightArrow) {
            this.rightArrow = rightArrow;
        }

        public Action getDisplayCCAction() {
            return displayCCAction;
        }

        public void setDisplayCCAction(Action displayCCAction) {
            this.displayCCAction = displayCCAction;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BlockCard {
        private StyledText title;
        private StyledText subTitle;
        private ContainerStyle blockCardStyle;
        private List<String> blockCardGradientStyle;
        private Image icon;

        public StyledText getTitle() {
            return title;
        }

        public void setTitle(StyledText title) {
            this.title = title;
        }

        public StyledText getSubTitle() {
            return subTitle;
        }

        public void setSubTitle(StyledText subTitle) {
            this.subTitle = subTitle;
        }

        public ContainerStyle getBlockCardStyle() {
            return blockCardStyle;
        }

        public void setBlockCardStyle(ContainerStyle blockCardStyle) {
            this.blockCardStyle = blockCardStyle;
        }

        public List<String> getBlockCardGradientStyle() {
            return blockCardGradientStyle;
        }

        public void setBlockCardGradientStyle(List<String> blockCardGradientStyle) {
            this.blockCardGradientStyle = blockCardGradientStyle;
        }

        public Image getIcon() {
            return icon;
        }

        public void setIcon(Image icon) {
            this.icon = icon;
        }
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Action getExpiredAction() {
        return expiredAction;
    }

    public void setExpiredAction(Action expiredAction) {
        this.expiredAction = expiredAction;
    }

    public CreditCardFront getFront() {
        return front;
    }

    public void setFront(CreditCardFront front) {
        this.front = front;
    }

    public CreditCardBack getBack() {
        return back;
    }

    public void setBack(CreditCardBack back) {
        this.back = back;
    }

    public Image getChevron() {
        return chevron;
    }

    public void setChevron(Image chevron) {
        this.chevron = chevron;
    }

    public Action getShowCardDetailsAction() {
        return showCardDetailsAction;
    }

    public void setShowCardDetailsAction(Action showCardDetailsAction) {
        this.showCardDetailsAction = showCardDetailsAction;
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public Lender getLender() {
        return lender;
    }

    public void setLender(Lender lender) {
        this.lender = lender;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getHeaderText() {
        return headerText;
    }

    public void setHeaderText(String headerText) {
        this.headerText = headerText;
    }

    public Map<String, String> getHeaderPlaceholders() {
        return headerPlaceholders;
    }

    public void setHeaderPlaceholders(Map<String, String> headerPlaceholders) {
        this.headerPlaceholders = headerPlaceholders;
    }

    public boolean isMasked() {
        return masked;
    }

    public void setMasked(boolean masked) {
        this.masked = masked;
    }

    public Map<String, String> getViewCardDetailsTracking() {
        return viewCardDetailsTracking;
    }

    public void setViewCardDetailsTracking(Map<String, String> viewCardDetailsTracking) {
        this.viewCardDetailsTracking = viewCardDetailsTracking;
    }

    public BlockCard getBlockCard() {
        return blockCard;
    }

    @Override
    public String toString() {
        return "DisplayCreditCardSlot{" +
                "description='" + description + '\'' +
                ", productType=" + productType +
                ", lender=" + lender +
                ", expiredAction=" + expiredAction +
                ", front=" + front +
                ", blockCard=" + blockCard +
                ", back=" + back +
                ", viewCardDetailsTracking=" + viewCardDetailsTracking +
                ", token='" + token + '\'' +
                ", showCardDetailsAction=" + showCardDetailsAction +
                ", chevron=" + chevron +
                ", masked=" + masked +
                ", headerText='" + headerText + '\'' +
                ", headerPlaceholders=" + headerPlaceholders +
                '}';
    }

    public void setBlockCard(BlockCard blockCard) {
        this.blockCard = blockCard;
    }


}
