package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.b on 14/02/18
 */
public class BnplApplicationUpdateResponse {

    @JsonProperty
    private ApplicationStatus status;

    @JsonProperty("tracking_id")
    private String trackingId;

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    @Override
    public String toString() {
        return "BnplApplicationUpdateResponse{" +
                "status=" + status +
                ", trackingId='" + trackingId + '\'' +
                '}';
    }
}
