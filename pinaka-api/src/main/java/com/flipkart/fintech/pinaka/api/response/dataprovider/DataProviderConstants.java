package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.response.styles.ButtonStyle;
import com.flipkart.fintech.pinaka.api.response.styles.ContainerStyle;

public class DataProviderConstants {

    public static class MapiEndPoint {
        public static String APPLICATION_LOADER_STATUS_ENDPOINT_V3 = "/3/fintech/application/loader/status?productType=%s&context=%s&productSubType=%s";
        public static String APPLICATION_LOADER_STATUS_ENDPOINT_V4 = "/4/fintech/application/loader/status?productType=%s&context=%s&applicationId=%s&aapiContext=%s&productSubType=%s&utmSource=%s";
    }

    public static class SLOT_PARAMS {
        public static String BUTTON_TYPE = "buttonType";
        public static String ACTION_CARD_TYPE = "actionCardType";
        public static String LENDER = "lender";
        public static String FORM_TYPE = "formType";
        public static String MESSAGE = "message";
        public static String LOADER_TYPE = "loaderType";
        public static String REFRESH_INTERVAL = "refreshInterval";
        public static String PRODUCT_TYPE = "productType";
        public static String DETAILS_TYPE = "detailsType";
        public static String INSTRUCTIONS_TYPE = "instructionsType";
        public static String TITLE = "title";
        public static String SLOT_DATA = "slotData";
        public static String SUB_TITLE = "subTitle";
        public static String CONTEXT = "context";
        public static String TEXT = "text";
        public static String IMAGE_URL = "imageUrl";
        public static String LANDING_URL = "landingUrl";
        public static String TRACKING_PARAMS = "trackingParams";
    }

    public static class CONTEXT {
        public static final String SETUP_AUTOPAY_NOW = "SETUP_AUTOPAY_NOW";
        public static final String SETUP_AUTOPAY_LATER = "SETUP_AUTOPAY_LATER";
        public static final String AUTOPAY_SETUP_FAILED = "AUTOPAY_SETUP_FAILED";
        public static final String AUTOPAY_SETUP_COMPLETED = "AUTOPAY_SETUP_COMPLETED";

    }

    public static class SLOT_PARAM_VALUES {
        public static final String MANDATE = "MANDATE";
        public static final String NEW_ADDRESS = "NEW_ADDRESS";
        public static final String INLINE = "INLINE";
        public static final String SETUP_LATER = "Setup Later";
        public static final String SETUP_AUTO_PAY = "Setup Auto Pay";
        public static final String HORIZONTAL_LAYOUT = "HORIZONTAL";
        public static final String MANDATE_OPTION_STRING = "%sth of every month";
        public static final String CONTINUE = "Continue";
        public static final String API = "API";



    }

    public static class MAPI_PAGE_QUERY_PARAMS {
    }

    public static String getServiceabilityResultStoreKey(String policyId){
        return "SERVICEABILITY_" + policyId;
    }

    public static String getPayoutResultStoreKey(String policyId){
        return "PAYOUT_" + policyId;
    }

    public static int MAX_MEDIA_UPLOAD_RETRIES = 3;

    public static class TRACKING {
        public static String PROP28 = "prop28";
        public static String EV50 = "ev50";
    }

    public static class AutoPayParams {
        public static String SETUP_AUTOPAY_MANDATORY = "setup_autopay_mandatory";
        public static String SETUP_AUTOPAY_APPLICABLE = "setup_autopay_applicable";
    }

    public static final String PAGE_TRACKING_TYPE = "pageTrackingType";

    public static class TextStyle {
        public static final String NEED_HELP_TEXT_COLOUR = "#333333";
        public static final String VIEW_DETAILS_TEXT_COLOUR = "#212121";
        public static final String CONTACT_US_TEXT_COLOUR = "#2874f0";
        public static final String SUBTITLE_TEXT_COLOUR = "#878787";
        public static final String LINK_TEXT_COLOUR = "#2874f0";
        public static final String TITLE_TEXT_COLOUR = "#212121";
        public static final String KNOW_MORE_TEXT_COLOUR = "#2874f0";
        public static final String FONT_FAMILY = "roboto_regular";
        public static final String FONT_FAMILY_MEDIUM = "roboto_medium";
        public static final String FONT_WEIGHT_NORMAL = "normal";
        public static final String FONT_WEIGHT_BOLD = "bold";
        public static final String BLUE_COLOUR = "#2874f0";
        public static final String BRASS_COLOUR = "#cd9575";
        public static final String VIOLET_COLOUR = "#526cfa";
        public static final String WHITE_COLOUR = "#ffffff";
        public static final String BORDER_COLOUR = "#f0f0f0";
        public static final String GREY_COLOR = "#808080";
        public static final String ALIGN_LEFT = "left";
        public static final String ALIGN_CENTER = "center";
        public static final String FONT_STYLE_NORMAL = "normal";
        public static final String BUTTON_COLOUR = "#fb641b";
        public static final String BUTTON_TEXT_COLOUR = "#ffffff";
        public static final String BUTTON_DISABLED_COLOUR = "#c2c2c2";
        public static final String LABEL_COLOR = "#878787";
        public static final String STATUS_COLOUR = "#e2a500";
        public static final String TEXT_COLOUR = "#3a3a3a";
        public static final String ERROR_RED_COLOUR = "#ff4343";
        public static final String BANK_INSTRUCTIONS_COLOUR = "#37474f";
        public static final String TITLE_TEXT_SIZE = "14";
    }

    public static ButtonStyle getWhiteBackgroundBlueTextButtonStyle() {
        ContainerStyle containerStyle = new ContainerStyle();
        containerStyle.setBackgroundColor(DataProviderConstants.TextStyle.WHITE_COLOUR);
        containerStyle.setBorderColor(DataProviderConstants.TextStyle.BORDER_COLOUR);

        com.flipkart.fintech.pinaka.api.response.styles.TextStyle textStyle = new com.flipkart.fintech.pinaka.api.response.styles.TextStyle();
        textStyle.setColor(DataProviderConstants.TextStyle
                .BLUE_COLOUR);
        ButtonStyle buttonStyle = new ButtonStyle();
        buttonStyle.setTextStyle(textStyle);
        buttonStyle.setContainerStyle(containerStyle);
        return buttonStyle;
    }

    public static ButtonStyle getBlueBackgroundWhiteColourButtonStyle() {
        ContainerStyle containerStyle = new ContainerStyle();
        containerStyle.setBackgroundColor(DataProviderConstants.TextStyle.BLUE_COLOUR);
        containerStyle.setBorderColor(DataProviderConstants.TextStyle.BORDER_COLOUR);

        com.flipkart.fintech.pinaka.api.response.styles.TextStyle textStyle = new com.flipkart.fintech.pinaka.api.response.styles.TextStyle();
        textStyle.setColor(DataProviderConstants.TextStyle
                .WHITE_COLOUR);
        ButtonStyle buttonStyle = new ButtonStyle();
        buttonStyle.setTextStyle(textStyle);
        buttonStyle.setContainerStyle(containerStyle);
        return buttonStyle;
    }

    public static final int MAX_MEDIA_CAPTURE_COUNT = 15;
    public static final String RUPEE_SYMBOL_UNICODE = "\u20B9 ";
    public static final String RUPEE_SYMBOL = "\u20B9";
}
