package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.request.v6.DataEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, visible = true, property = "data_enum")
@JsonSubTypes({
        @JsonSubTypes.Type(value = HelpDataEnumResponse.class, name = "HELP"),
        @JsonSubTypes.Type(value = AnnouncementDataEnumResponse.class, name = "ANNOUNCEMENT"),
        @JsonSubTypes.Type(value = BannerDataEnumResponse.class, name = "BANNER"),
        @JsonSubTypes.Type(value = FormDataEnumResponse.class, name = "FORM")
})
public class DataEnumResponse {
    private DataEnum dataEnum;
}
