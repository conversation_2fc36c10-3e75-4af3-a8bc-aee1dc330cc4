package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConsentMetaData {

    @JsonProperty("consentType")
    private String consentType;

    @JsonProperty("userIP")
    private String userIP;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("deviceInfo")
    private String deviceInfo;

    @JsonProperty("deviceParams")
    private String deviceParams;

    @JsonProperty("currentTimeStamp")
    private Long currentTimeStamp;

    @JsonProperty("consentId")
    private String consentId;

    @JsonProperty("consentFor")
    private String consentFor;
}
