package com.flipkart.fintech.pinaka.api.request.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pinaka.api.enums.ApplicationUpdateContext;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
public class EkycPanAadhaarSubmitRequest extends ApplicationUpdateRequest {
    @NonNull
    private String encryptedPanNumber;
    private EncryptionKeyData encryptionKeyData;
    @NonNull
    private String encryptedAadhaarNumber;
    @NonNull
    private FinancialProvider financialProvider;

    public EkycPanAadhaarSubmitRequest() {
        super(ApplicationUpdateContext.EKYC_PAN_AADHAAR_SUBMIT);
        this.encryptedPanNumber = "";
        this.encryptedAadhaarNumber = "";
    }
}
