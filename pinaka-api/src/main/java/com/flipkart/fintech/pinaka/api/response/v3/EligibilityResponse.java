package com.flipkart.fintech.pinaka.api.response.v3;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.flipkart.affordability.underwriting.model.CreditConfig;
import com.flipkart.affordability.underwriting.model.DataTypeInfo;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pinaka.api.enums.RejectReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 18/02/20.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "type",
        defaultImpl = EligibilityResponse.class)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "revalidation",
        value = RevalEligibilityResponse.class
)})
public class EligibilityResponse {
    @JsonProperty("referenceNumber")
    private String       referenceNumber;
    @JsonProperty("eligibleAmount")
    private Double       eligibleAmount;
    @JsonProperty("isEligible")
    private boolean      isEligible;
    @JsonProperty("customer_cibil_bucket")
    private int          customerCibilBucket;
    @JsonProperty("lenderReferenceNumber")
    private String       lenderReferenceNumber;
    @JsonProperty("info")
    private String       info;
    @JsonProperty("STATUS")
    private STATUS       status;
    @JsonProperty("cibil_score_bucket")
    private Integer      cibilScoreBucket;
    @JsonProperty("autopay_setup_mandatory")
    private Boolean      autopaySetupMandatory;
    @JsonProperty("autopay_setup_applicable")
    private Boolean      autopaySetupApplicable;
    @JsonProperty("credit_config")
    private CreditConfig creditConfig;
    private List<DataTypeInfo> dataTypeInfoList;

    @JsonProperty("sms_upgradable")
    private boolean smsUpgradable;

    @JsonProperty("bureau_upgradable")
    private boolean bureauUpgradable;

    @JsonProperty("intent_score")
    private Double intentScore;

    @JsonProperty("affordability_lower_bound")
    private Double affordabilityLowerBound;

    @JsonProperty("affordability_upper_bound")
    private Double affordabilityUpperBound;

    @JsonProperty("affordability_score")
    private Double affordabilityScore;

    @JsonProperty("reject_reason")
    private RejectReason rejectReason = RejectReason.OTHER;

    @JsonProperty("limit_upgraded")
    private Boolean limitUpgraded;

    @JsonProperty("is_existing_customer")
    private Boolean isExistingCustomer;

    @JsonProperty("lms_config_id")
    private String lmsConfigId;

    private int age;

    private String pincode;

    private String ruleName;
}
