package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;

import javax.validation.constraints.NotNull;

/**
 * Created by kunal.keshwani on 28/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(
    property = "product_type",
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    visible = true
)
@JsonSubTypes({@JsonSubTypes.Type(
    name = "CBC",
    value = CbcApplicationSubmitRequest.class
)})
public abstract class ApplicationSubmitRequest {

    @NotNull
    @JsonProperty("product_type")
    private ProductType productType;

    @NotNull
    @JsonProperty("lender")
    private Lender lender;

    @NotNull
    @JsonProperty("external_id")
    private String externalId;

    public ApplicationSubmitRequest(ProductType productType) {
        this.productType = productType;
        this.externalId = "";
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public Lender getLender() {
        return lender;
    }

    public void setLender(Lender lender) {
        this.lender = lender;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    @Override
    public String toString() {
        return "ApplicationSubmitRequest{" +
            "productType=" + productType +
            ", lender=" + lender +
            ", externalId='" + externalId + '\'' +
            '}';
    }
}
