package com.flipkart.fintech.pinaka.api.request.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.LoaderContext;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.Consent;
import lombok.Data;

import java.util.Map;

/**
 * Created by rajat.mathur on 10/12/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PageSlotRequest {

    private String pageId;
    private ProductType productType;
    private AddressFormType addressFormType;
    private EmploymentStatusFormType employmentStatusFormType;
    private Integer pageVersion;
    private String entityId;
    private String otpReferenceId;
    private Consent.ConsentType otpType;
    private String addressFieldName;
    private String masked;
    private Channel channel;
    private LoaderContext loaderContext;
    private String appVersion;
    private String utmSource;
    private String utmSourceContext = "default";
    private String utmSourceRedirectionUrl;
    private String visitId;

    // Note : making map to make it generic so that rome deployment can be prevented
    // else everytime we have to add a new field and also redeploy rome for new client version
    // (current use case - addressData populate for edit page)
    private Map<String, String> queryParamData;
}