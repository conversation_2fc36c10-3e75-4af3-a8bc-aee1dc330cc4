package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.page.action.v1.calm.AutoSubmitForm;
import javax.annotation.Nullable;

import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ToString
public class FormAutoSubmitResponse {
  @Nullable
  private Action action;
  @Nullable
  private AutoSubmitForm form;
}
