package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;

import java.util.List;

public class ActionCardListSlot extends Slot {
    private StyledText title;
    private StyledText subtitle;
    private List<ActionCardSlot> actionCardSlotList;

    public ActionCardListSlot() {
        super(SlotType.ACTION_CARD_LIST);
    }

    public StyledText getTitle() {
        return title;
    }

    public void setTitle(StyledText title) {
        this.title = title;
    }

    public StyledText getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(StyledText subtitle) {
        this.subtitle = subtitle;
    }

    public List<ActionCardSlot> getActionCardSlotList() {
        return actionCardSlotList;
    }

    public void setActionCardSlotList(List<ActionCardSlot> actionCardSlotList) {
        this.actionCardSlotList = actionCardSlotList;
    }
}
