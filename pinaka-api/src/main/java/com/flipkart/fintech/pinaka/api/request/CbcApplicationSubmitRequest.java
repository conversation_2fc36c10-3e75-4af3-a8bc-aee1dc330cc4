package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.CbcApplication;
import com.flipkart.fintech.pinaka.api.model.KycSchedulerData;

/**
 * <AUTHOR>
 * @since 19/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CbcApplicationSubmitRequest extends ApplicationSubmitRequest {

    @JsonProperty("form_data")
    private CbcApplication formData;

    @JsonProperty("kyc_scheduler_data")
    private KycSchedulerData kycSchedulerData;

    private Boolean storeMerge;

    public CbcApplicationSubmitRequest() {
        super(ProductType.CBC);
    }

    public CbcApplication getFormData() {
        return formData;
    }

    public void setFormData(CbcApplication formData) {
        this.formData = formData;
    }

    public KycSchedulerData getKycSchedulerData() {
        return kycSchedulerData;
    }

    public void setKycSchedulerData(KycSchedulerData kycSchedulerData) {
        this.kycSchedulerData = kycSchedulerData;
    }

    public Boolean getStoreMerge() {
        return storeMerge;
    }

    public void setStoreMerge(Boolean storeMerge) {
        this.storeMerge = storeMerge;
    }

    @Override
    public String toString() {
        return "CbcApplicationSubmitRequest{" +
            "formData=" + formData +
            ", storeMerge=" + storeMerge +
            '}';
    }
}