package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.CommunicationType;
import com.flipkart.fintech.pinaka.api.enums.ProductType;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 02/11/17.
 */
public class AsyncCommRequest {

    @JsonProperty("type")
    private CommunicationType type;

    @JsonProperty("template")
    private String template;

    @JsonProperty("product_type")
    private ProductType productType;

    @JsonProperty("communication_data")
    private Map<String, Object> communicationData;

    public CommunicationType getType() {
        return type;
    }

    public void setType(CommunicationType type) {
        this.type = type;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public Map<String, Object> getCommunicationData() {
        return communicationData;
    }

    public void setCommunicationData(Map<String, Object> communicationData) {
        this.communicationData = communicationData;
    }
}
