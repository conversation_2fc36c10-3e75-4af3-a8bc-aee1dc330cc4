package com.flipkart.fintech.pinaka.api.response;

import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by rajat.mathur on 15/02/19.
 */

@NoArgsConstructor
public abstract class TrackableComponent {

    private Map<String, String> tracking;

    public Map<String, String> getTracking() {
        return tracking;
    }

    public void setTracking(Map<String, String> tracking) {
        this.tracking = tracking;
    }

}
