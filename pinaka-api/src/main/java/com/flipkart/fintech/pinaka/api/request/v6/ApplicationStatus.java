package com.flipkart.fintech.pinaka.api.request.v6;

public enum ApplicationStatus {
    CI_DETAILS,
    CUSTOMER_IDENTIFICATION_START,
    CUSTOMER_IDENTIFICATION_END,
    <PERSON>DI<PERSON>ON<PERSON>_DETAILS,
    ELIG<PERSON>LE_OFFER_START,
    ELIG<PERSON>LE_OFFER_END,
    <PERSON><PERSON><PERSON>_DETAILS,
    S<PERSON><PERSON><PERSON>_OFFER_START,
    <PERSON><PERSON><PERSON><PERSON>_OFFER_END,
    LENDER_PLATFORM,
    APPLICATION_COMPLETED,
    SUCCESS,
    REJECTED,
    BA<PERSON><PERSON>_DETAILS,
    VERIFY_PAN,
    GENERATE_OFFER,
    <PERSON><PERSON><PERSON>_OFFER_TIMER,
    P<PERSON><PERSON>_OFFER,
    OFFER_SCREEN,
    SEARCH_DOWNLOAD_CKYC,
    CKYC_DETAILS,
    GENERATE_SELFIE_TOKEN,
    SELFIE_SCREEN,
    MATCH_SELFIE_DATA,
    UPLOAD_SELFIE_DATA,
    START_LOAN,
    GET_APPROVAL_STATUS,
    BANK_DETAILS,
    IFSC_DETAILS,
    REPA<PERSON>MENT_MODES,
    INITIATE_PENNY_DROP,
    GENERATE_EMANDATE_URL,
    EMANDATE_REDIRECTION,
    GET_EMANDATE_STATUS,
    GET_EMANDATE_STATUS_RETRY,
    ONBOARD_LOAN,
    KFS_SCREEN,
    GENERATE_KFS_OTP,
    KFS_OTP_VERIFICATION,
    VALIDATE_KFS_OTP,
    AUTH_AUTO_DISBURSAL,
    APPLICATION_STATUS,
    GET_PAYMENT_DETAILS,
    CREATE_PROFILE_END,
    CREATE_PROFILE_START;
}
