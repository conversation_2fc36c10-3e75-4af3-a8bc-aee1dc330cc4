package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.model.FormFieldDetails;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 13/08/18.
 */
public class FormFieldsResponse {

    private List<FormFieldDetails> fields;

    public List<FormFieldDetails> getFields() {
        return fields;
    }

    public void setFields(List<FormFieldDetails> fields) {
        this.fields = fields;
    }
}
