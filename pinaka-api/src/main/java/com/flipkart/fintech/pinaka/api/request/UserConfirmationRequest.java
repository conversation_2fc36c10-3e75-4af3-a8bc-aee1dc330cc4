package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.model.UserAccount;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 13/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserConfirmationRequest extends UserAccount {

    @NotNull
    @JsonProperty("accepted")
    boolean userAccepted;

    @NotNull
    @JsonProperty("tracking_id")
    String trackingId;

    public boolean isUserAccepted() {
        return userAccepted;
    }

    public void setUserAccepted(boolean userAccepted) {
        this.userAccepted = userAccepted;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }
}
