package com.flipkart.fintech.pinaka.api.response.styles;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ContainerStyle {

    @JsonProperty("background_colour")
    private String backgroundColor;

    @JsonProperty("border_color")
    private String borderColor;

    @JsonProperty("border_width")
    private int borderWidth;

    @JsonProperty("border_radius")
    private int borderRadius;

    @JsonProperty("flex")
    private int flex;

    @JsonProperty("justify_content")
    private String justifyContent;

    @JsonProperty("alignItems")
    private String alignItems;

    @JsonProperty("alignSelf")
    private String alignSelf;

    @JsonProperty("position")
    private String position;

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public int getBorderWidth() {
        return borderWidth;
    }

    public void setBorderWidth(int borderWidth) {
        this.borderWidth = borderWidth;
    }

    public int getBorderRadius() {
        return borderRadius;
    }

    public void setBorderRadius(int borderRadius) {
        this.borderRadius = borderRadius;
    }

    public int getFlex() {
        return flex;
    }

    public void setFlex(int flex) {
        this.flex = flex;
    }

    public String getJustifyContent() {
        return justifyContent;
    }

    public void setJustifyContent(String justifyContent) {
        this.justifyContent = justifyContent;
    }

    public String getAlignItems() {
        return alignItems;
    }

    public void setAlignItems(String alignItems) {
        this.alignItems = alignItems;
    }

    public String getAlignSelf() {
        return alignSelf;
    }

    public void setAlignSelf(String alignSelf) {
        this.alignSelf = alignSelf;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

}
