package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 16/08/17.
 */

@JsonTypeInfo(
        property = "product_type",
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        use = JsonTypeInfo.Id.NAME,
        visible = true
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "CBC",
        value = CbcApplicationCreateRequest.class
)})
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public abstract class ApplicationCreateRequest {

    @NotNull
    @JsonProperty("product_type")
    private ProductType productType;

    @NotNull
    @JsonProperty("external_id")
    private String externalId;

    @JsonProperty("correlation_id")
    private String correlationId;

    @JsonProperty("lender_name")
    private Lender lender;

    public ApplicationCreateRequest(ProductType productType) {
        this.productType = productType;
        this.externalId = "";
    }
}
