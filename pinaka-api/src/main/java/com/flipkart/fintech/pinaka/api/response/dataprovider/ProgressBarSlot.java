package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;


public class ProgressBarSlot extends Slot {

    public ProgressBarSlot() {
        super(SlotType.PROGRESS_BAR);
    }

    private int currentValue;
    private int maxValue;
    private String backgroundColor;
    private String color;

    public int getCurrentValue() {
        return currentValue;
    }

    public void setCurrentValue(int currentValue) {
        this.currentValue = currentValue;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}