package com.flipkart.fintech.pinaka.api.response.dataprovider;

import java.util.List;

/**
 * Created by rajat.mathur on 07/01/19.
 */
public class InstructionNote {

    private int stepNo;
    private List<String> instructions;

    public InstructionNote() {
    }

    public int getStepNo() {
        return stepNo;
    }

    public void setStepNo(int stepNo) {
        this.stepNo = stepNo;
    }

    public List<String> getInstructions() {
        return instructions;
    }

    public void setInstructions(List<String> instructions) {
        this.instructions = instructions;
    }

    @Override
    public String toString() {
        return "InstructionNote{" + "stepNo=" + stepNo +
                ", instructions=" + instructions +
                '}';
    }
}
