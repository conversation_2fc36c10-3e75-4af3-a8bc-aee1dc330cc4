package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.enums.LimitStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LimitDiscoveryResponse {
    @NotNull
    private LimitStatus status;
    private String calloutMessage;
    private String validTill; //format => "MMM-DD-YY"
    private String ctaActionUrl;
    private double totalLimit;
    private double emiLimit;
    private double paylaterLimit;
    private double fsupLimit;
}
