package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.response.v6.Charge;
import com.flipkart.fintech.pinaka.api.response.v6.Emi;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LoanDetails {
    private long loanAmount;
    private BigDecimal roi;
    private Map<Charge, ChargeDetails> charges;
    private BigDecimal netDisbursalAmount;
    private Emi emi;
    private Tenure tenure;
    private BigDecimal flipkartCharges;

}
