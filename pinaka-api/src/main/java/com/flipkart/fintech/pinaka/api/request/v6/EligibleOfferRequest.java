package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ConsentMetadata;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.LoanPurpose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EligibleOfferRequest extends WorkflowHttpBaseRequest{
    @NotNull
    private EmploymentType employmentType;
    private String employerId;
    private String employerName;
    private String industryId;
    private String industryName;
    private BigDecimal monthlyIncome;
    private BigDecimal bonusIncome = BigDecimal.ZERO;
    private BigDecimal annualTurnOver;
    @NotNull
    private String binScore;
    @NotNull
    private String shippingAddressId;

    private LoanPurpose loanPurpose;

    @JsonProperty("consent_meta_data")
    private ConsentMetaData consentMetaData;

    @JsonProperty("consent_meta_data_list")
    private List<ConsentMetaData> consentMetaDataList;

    private String houseNumber;
    private String area;
    private String city;
    private String state;
    private String pincode;
    private String firstName;
    private String lastName;
    private Integer version;
    @NotNull
    private boolean panConsentProvided;


    public BigDecimal getMonthlyIncomeWithBonus() {
        if(Objects.isNull(monthlyIncome)) {
            return null;
        }
        if(Objects.isNull(bonusIncome)) {
            bonusIncome = BigDecimal.ZERO;
        }
        return monthlyIncome.add(bonusIncome);
    }
}
