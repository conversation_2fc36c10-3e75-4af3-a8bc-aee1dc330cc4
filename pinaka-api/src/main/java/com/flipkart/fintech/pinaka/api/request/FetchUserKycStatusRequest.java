package com.flipkart.fintech.pinaka.api.request;

import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FetchUserKycStatusRequest {
    private String merchantUserId;
    private Lender financialProvider;
    private String  applicationRefId;
    private Channel channel;
    private String appVersion;
    private ProductType product;
}
