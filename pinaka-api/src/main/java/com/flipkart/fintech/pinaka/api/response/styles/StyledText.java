package com.flipkart.fintech.pinaka.api.response.styles;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by vaibhavgupta.v on 22/04/19.
 */
public class StyledText {

    @JsonProperty("text")
    private String text;

    @JsonProperty("text_style")
    private TextStyle textStyle;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public TextStyle getTextStyle() {
        return textStyle;
    }

    public void setTextStyle(TextStyle textStyle) {
        this.textStyle = textStyle;
    }
}
