package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.ButtonStyle;

/**
 * Created by rajat.mathur on 10/12/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessagingSlot extends Slot {

    public MessagingSlot() {
        super(SlotType.MESSAGING);
    }

    private RichText title;
    private RichText subTitle;
    private RichText description;
    private Action action;
    private Image image;
    private ButtonStyle buttonStyle;

    public RichText getTitle() {
        return title;
    }

    public void setTitle(RichText title) {
        this.title = title;
    }

    public RichText getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(RichText subTitle) {
        this.subTitle = subTitle;
    }

    public RichText getDescription() {
        return description;
    }

    public void setDescription(RichText description) {
        this.description = description;
    }

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public Image getImage() {
        return image;
    }

    public void setImage(Image image) {
        this.image = image;
    }

    public ButtonStyle getButtonStyle() {
        return buttonStyle;
    }

    public void setButtonStyle(ButtonStyle buttonStyle) {
        this.buttonStyle = buttonStyle;
    }
}
