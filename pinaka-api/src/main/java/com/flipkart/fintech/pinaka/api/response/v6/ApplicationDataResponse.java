package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.request.v6.LenderDetails;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationDataResponse {
    private String applicationId;
    private String externalUserId;
    private String smUserId;
    private String financialProvider;
    private String journeyContext;
    private String productType;
    private UserDetails userDetails;
    private String expiry;
    private LenderDetails lenderDetails;
}
