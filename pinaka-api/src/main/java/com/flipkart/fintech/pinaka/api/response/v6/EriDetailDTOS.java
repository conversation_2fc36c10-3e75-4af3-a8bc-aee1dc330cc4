package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EriDetailDTOS {
    private String dueDate;
    private BigDecimal eriAmount;
    private BigDecimal principalContribution;
    private BigDecimal interestContribution;
    private int openingPrincipal;
    private BigDecimal closingPrincipal;
}
