package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.Application;
import com.flipkart.fintech.pinaka.api.model.Consent;
import com.flipkart.fintech.pinaka.api.model.EncryptionKeyData;

import java.util.List;

/**
 * Created by sushan<PERSON><PERSON>.b on 30/01/18
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BnplApplicationCreateRequest extends ApplicationCreateRequest {
    @JsonProperty("form_data")
    private Application formData;

    @JsonProperty("aadhaar_number")
    private String aadhaar;

    @JsonProperty("masked_aadhaar")
    private String maskedAadhaar;

    @JsonProperty("consent_details")
    private List<Consent> consentDetails;

    @JsonProperty("enc_key_details")
    protected EncryptionKeyData encKeyData;

    public BnplApplicationCreateRequest() {
        super(ProductType.CFA);
    }

    public Application getFormData() {
        return formData;
    }

    public void setFormData(Application formData) {
        this.formData = formData;
    }

    public String getAadhaar() {
        return aadhaar;
    }

    public void setAadhaar(String aadhaar) {
        this.aadhaar = aadhaar;
    }

    public String getMaskedAadhaar() {
        return maskedAadhaar;
    }

    public void setMaskedAadhaar(String maskedAadhaar) {
        this.maskedAadhaar = maskedAadhaar;
    }

    public List<Consent> getConsentDetails() {
        return consentDetails;
    }

    public void setConsentDetails(List<Consent> consentDetails) {
        this.consentDetails = consentDetails;
    }

    public EncryptionKeyData getEncKeyData() {
        return encKeyData;
    }

    public void setEncKeyData(EncryptionKeyData encKeyData) {
        this.encKeyData = encKeyData;
    }
}
