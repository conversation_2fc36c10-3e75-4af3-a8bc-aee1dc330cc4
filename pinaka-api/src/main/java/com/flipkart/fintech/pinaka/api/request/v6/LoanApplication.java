package com.flipkart.fintech.pinaka.api.request.v6;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoanApplication {
    private String externalUserId;
    private String smUserId;
    private String financialProvider;
    private String journeyContext;
    private String productType;
    private UserDetails userDetails;
    private String expiry;
    private LenderDetails lenderDetails;
    private FormDetails formDetails;
    private String errorMessage;
    private Status code;
    private List<ConsentDetails> consentDetailsList;
    private String applicationCreatedAt;
    private String monthlyIncome;
}
