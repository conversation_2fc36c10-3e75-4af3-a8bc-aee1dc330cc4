package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ConsentMetadata;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.LoanPurpose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdentifyCustomerRequest extends WorkflowHttpBaseRequest {
    @NotNull
    private String panNumber;
    @NotNull
    private String dob;
    @NotNull
    private Gender gender;
    @NotNull
    private String shippingAddressId;

    private LoanPurpose loanPurpose;

    @JsonProperty("consent_meta_data")
    private ConsentMetaData consentMetaData;

    private String houseNumber;
    private String area;
    private String city;
    private String state;
    private String pincode;

    private String firstName;

    private String lastName;

    @NotNull
    private String binScore;
    @NotNull
    private boolean panConsentProvided;
}
