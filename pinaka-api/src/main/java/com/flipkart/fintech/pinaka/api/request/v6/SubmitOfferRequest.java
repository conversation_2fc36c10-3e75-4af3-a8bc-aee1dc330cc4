package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ConsentMetadata;
import com.flipkart.fintech.pinaka.api.response.v6.Charge;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmitOfferRequest extends WorkflowHttpBaseRequest {
    @NotNull
    private long loanAmount;
    @NotNull
    private BigDecimal roi;
    @NotNull
    private Tenure tenure;
    @NotNull
    private BigDecimal emi;
    @NotNull
    private BigDecimal netDisbursalAmount;

    @JsonProperty("consent_meta_data")
    private ConsentMetaData consentMetaData;
    @NotNull
    private String callbackUrl;
    @NotNull
    private boolean offerConsentProvided;
    @NotNull
    private String lenderApplicationId;
    @NotNull
    private Map<Charge, ChargeDetails> charges;
}
