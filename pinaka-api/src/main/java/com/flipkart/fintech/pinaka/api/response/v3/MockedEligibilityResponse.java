package com.flipkart.fintech.pinaka.api.response.v3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR> - 13/07/22
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MockedEligibilityResponse {
    private String accountId;
    private EligibilityResponse eligibilityResponse;
}
