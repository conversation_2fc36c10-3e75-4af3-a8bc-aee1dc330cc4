package com.flipkart.fintech.pinaka.api.response.dataprovider;

import java.util.Map;

public class TrackingContext {

    private String pageName;
    private String navigationalPageName;
    private String navigationalPageType;
    private String pageType;
    private Map<String, String> tracking;

    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }

    public String getNavigationalPageName() {
        return navigationalPageName;
    }

    public void setNavigationalPageName(String navigationalPageName) {
        this.navigationalPageName = navigationalPageName;
    }

    public String getNavigationalPageType() {
        return navigationalPageType;
    }

    public void setNavigationalPageType(String navigationalPageType) {
        this.navigationalPageType = navigationalPageType;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public Map<String, String> getTracking() {
        return tracking;
    }

    public void setTracking(Map<String, String> tracking) {
        this.tracking = tracking;
    }

    @Override
    public String toString() {
        return "TrackingContext{" +
                "pageName='" + pageName + '\'' +
                ", navigationalPageName='" + navigationalPageName + '\'' +
                ", navigationalPageType='" + navigationalPageType + '\'' +
                ", pageType='" + pageType + '\'' +
                ", tracking=" + tracking +
                '}';
    }

}
