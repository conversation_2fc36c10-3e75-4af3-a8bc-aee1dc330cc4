package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.b on 14/02/18
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BnplApplicationUpdateRequest {

    @JsonProperty
    private ApplicationStatus status;

    @JsonProperty("account_id")
    private String accountId;

    @NotNull
    @JsonProperty("tracking_id")
    private String trackingId;

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    @Override
    public String toString() {
        return "BnplApplicationUpdateRequest{" +
                "status=" + status +
                ", accountId='" + accountId + '\'' +
                ", trackingId='" + trackingId + '\'' +
                '}';
    }
}
