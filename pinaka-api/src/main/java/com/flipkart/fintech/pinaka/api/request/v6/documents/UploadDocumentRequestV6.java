package com.flipkart.fintech.pinaka.api.request.v6.documents;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@AllArgsConstructor
public class UploadDocumentRequestV6 {
  private Document document;
  private EncryptionKeyData encryptionKeyData;

  public Map<String, String> createD42MetaData() {
    Map<String, String> metaData = document.getMetaData();
    metaData.put("encKeyRef", encryptionKeyData.getEncKeyRef());
    metaData.put("encKey", encryptionKeyData.getEncKey());
    return metaData;
  }

}
