package com.flipkart.fintech.pinaka.api.response.styles;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ButtonStyle {

    @JsonProperty("text_style")
    private TextStyle textStyle;

    @JsonProperty("container_style")
    private ContainerStyle containerStyle;

    public TextStyle getTextStyle() {
        return textStyle;
    }

    public void setTextStyle(TextStyle textStyle) {
        this.textStyle = textStyle;
    }

    public ContainerStyle getContainerStyle() {
        return containerStyle;
    }

    public void setContainerStyle(ContainerStyle containerStyle) {
        this.containerStyle = containerStyle;
    }
}

