package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 17/08/17.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NudgeResponse {

    @JsonProperty("tracking_id")
    private String trackingId;

    @JsonProperty("external_id")
    private String externalId;

    @JsonProperty("landing_url")
    private String landingUrl;

   
}
