package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LandingPageRequest {
    @NotNull
    private String accountId;
    @NotNull
    private String smUserId;
    private String source;
    @NotNull
    private Operation operation;
    @NotNull
    private String merchantId;
    @Nullable
    private Map<String,String> additionalParameters;
}
