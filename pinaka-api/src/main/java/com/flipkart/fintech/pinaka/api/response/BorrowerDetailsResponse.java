package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.enums.ProductType;

public class BorrowerDetailsResponse {

  private String merchantUserRefId;

  private ProductType productType;

  private String merchantId;

  public BorrowerDetailsResponse() {
  }

  public BorrowerDetailsResponse(String merchantUserRefId,
                                 ProductType productType, String merchantId) {
    this.merchantUserRefId = merchantUserRefId;
    this.productType = productType;
    this.merchantId = merchantId;
  }

  public String getMerchantUserRefId() {
    return merchantUserRefId;
  }

  public void setMerchantUserRefId(String merchantUserRefId) {
    this.merchantUserRefId = merchantUserRefId;
  }

  public ProductType getProductType() {
    return productType;
  }

  public void setProductType(ProductType productType) {
    this.productType = productType;
  }

  public String getMerchantId() {
    return merchantId;
  }

  public void setMerchantId(String merchantId) {
    this.merchantId = merchantId;
  }
}
