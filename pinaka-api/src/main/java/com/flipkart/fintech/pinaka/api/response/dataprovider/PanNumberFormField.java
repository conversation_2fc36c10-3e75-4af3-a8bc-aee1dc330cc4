package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

/**
 * Created by rajat.mathur on 07/01/19.
 */
public class PanNumberFormField extends FormField {

    private Image successIcon;
    private Image errorIcon;
    private String validationRegex;
    private String regexValidationErrorMessage;

    public PanNumberFormField() {
        super(FormFieldType.PAN_NUMBER);
    }

    public Image getSuccessIcon() {
        return successIcon;
    }

    public void setSuccessIcon(Image successIcon) {
        this.successIcon = successIcon;
    }

    public Image getErrorIcon() {
        return errorIcon;
    }

    public void setErrorIcon(Image errorIcon) {
        this.errorIcon = errorIcon;
    }

    public String getValidationRegex() {
        return validationRegex;
    }

    public void setValidationRegex(String validationRegex) {
        this.validationRegex = validationRegex;
    }

    public String getRegexValidationErrorMessage() {
        return regexValidationErrorMessage;
    }

    public void setRegexValidationErrorMessage(String regexValidationErrorMessage) {
        this.regexValidationErrorMessage = regexValidationErrorMessage;
    }
}
