package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.PanStatus;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 06/12/17.
 */
public class PanVerificationResponse {

    @NotNull
    @JsonProperty(value = "status")
    private PanStatus status;

    @NotNull
    @JsonProperty(value = "pan_number")
    private String panNumber;

    @JsonProperty(value = "pan_verified_name")
    private String panVerifiedName;

    @JsonProperty("info")
    private String info;

    public PanStatus getStatus() {
        return status;
    }

    public void setStatus(PanStatus status) {
        this.status = status;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getPanVerifiedName() {
        return panVerifiedName;
    }

    public void setPanVerifiedName(String panVerifiedName) {
        this.panVerifiedName = panVerifiedName;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
