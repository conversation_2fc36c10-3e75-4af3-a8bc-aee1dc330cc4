package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;

import javax.validation.constraints.NotNull;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AutopayMandateCreateRequest {
    @NotNull
    @JsonProperty("product_type")
    private ProductType productType;

    @NotNull
    @JsonProperty("autopay_date")
    private String autopayDate;

    @NotNull
    @JsonProperty("entity_id")
    private String entityId;

    @JsonProperty("lender_name")
    private Lender lender;

    @Override
    public String toString() {
        return "AutopayMandateCreateRequest{" +
                "productType=" + productType +
                ", autopayDate='" + autopayDate + '\'' +
                ", entityId='" + entityId + '\'' +
                ", lender=" + lender +
                ", dataMap=" + dataMap +
                '}';
    }

    @JsonProperty("data_map")
    private Map<String, Object> dataMap;

    public AutopayMandateCreateRequest() {
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public Lender getLender() {
        return lender;
    }

    public void setLender(Lender lender) {
        this.lender = lender;
    }

    public Map<String, Object> getDataMap() {
        return dataMap;
    }

    public void setDataMap(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getAutopayDate() {
        return autopayDate;
    }

    public void setAutopayDate(String autopayDate) {
        this.autopayDate = autopayDate;
    }
}
