package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.dataprovider.DataType;
import com.flipkart.fintech.pinaka.api.enums.dataprovider.ParameterType;

/**
 * Created by rajat.mathur on 07/12/18.
 */
public class ConfigurableParameter {

    private ParameterType type;
    private String key;
    private String displayName;
    private boolean multiInput;
    private DataType dataType;
    private String callbackUri;
    private boolean searchable;
    private boolean advanced;

    public ConfigurableParameter() {
    }

    public ParameterType getType() {
        return type;
    }

    public void setType(ParameterType type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public boolean isMultiInput() {
        return multiInput;
    }

    public void setMultiInput(boolean multiInput) {
        this.multiInput = multiInput;
    }

    public DataType getDataType() {
        return dataType;
    }

    public void setDataType(DataType dataType) {
        this.dataType = dataType;
    }

    public String getCallbackUri() {
        return callbackUri;
    }

    public void setCallbackUri(String callbackUri) {
        this.callbackUri = callbackUri;
    }

    public boolean isSearchable() {
        return searchable;
    }

    public void setSearchable(boolean searchable) {
        this.searchable = searchable;
    }

    public boolean isAdvanced() {
        return advanced;
    }

    public void setAdvanced(boolean advanced) {
        this.advanced = advanced;
    }
}

