package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;

public class InfoSlot extends Slot {
    public InfoSlot() {
        super(SlotType.INFO);
    }

    @JsonProperty("title")
    private StyledText title;
    @JsonProperty("subtitle")
    private StyledText subtitle;
    @JsonProperty("action")
    private Action action;


    public StyledText getTitle() {
        return title;
    }

    public void setTitle(StyledText title) {
        this.title = title;
    }

    public StyledText getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(StyledText subtitle) {
        this.subtitle = subtitle;
    }

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }
}
