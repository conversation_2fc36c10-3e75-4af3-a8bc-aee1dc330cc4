package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.RewardType;
import com.flipkart.fintech.pinaka.api.enums.TransactionType;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class RewardsDetailsRequestBody {
    @NotNull
    private String applicationSerno;
    @NotNull
    private Double amount;
    @NotNull
    private String merchantTransactionId;
    @NotNull
    private TransactionType transactionType;
    @NotNull
    private RewardType rewardType;
    private Long statementStartDate;
    private Long statementEndDate;

    @JsonCreator
    @Builder
    public RewardsDetailsRequestBody(@JsonProperty("applicationSerno") String applicationSerno,
                                     @JsonProperty("amount") Double amount,
                                     @JsonProperty("merchantTransactionId") String merchantTransactionId,
                                     @JsonProperty("transactionType") TransactionType transactionType,
                                     @JsonProperty("rewardType") RewardType rewardType,
                                     @JsonProperty("statementStartDate") Long statementStartDate,
                                     @JsonProperty("statementEndDate") Long statementEndDate) {
        this.applicationSerno = applicationSerno;
        this.amount = amount;
        this.merchantTransactionId = merchantTransactionId;
        this.transactionType = transactionType;
        this.rewardType = rewardType;
        this.statementStartDate = statementStartDate;
        this.statementEndDate = statementEndDate;
    }
}
