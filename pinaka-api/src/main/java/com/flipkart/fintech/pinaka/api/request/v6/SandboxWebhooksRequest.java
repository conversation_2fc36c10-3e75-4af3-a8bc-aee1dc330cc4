package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 21/12/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
public class SandboxWebhooksRequest extends WebhooksRequest {
    private String applicationState;
    private String status;
    private String subStatus;

    @Builder
    public SandboxWebhooksRequest(String lspApplicationId, String lenderApplicationId, String applicationStatus, Long stateTransitionTime,
                                  String applicationState, String status, String subStatus) {
        super(lspApplicationId, lenderApplicationId, applicationStatus, stateTransitionTime);
        this.applicationState = applicationState;
        this.status = status;
        this.subStatus = subStatus;
    }
}
