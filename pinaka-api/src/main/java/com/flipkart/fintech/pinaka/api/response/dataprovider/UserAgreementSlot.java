package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

/**
 * Created by kunal.keshwani on 04/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserAgreementSlot extends Slot {

    public UserAgreementSlot() {
        super(SlotType.USER_AGREEMENT);
    }

    private boolean agreed;
    private boolean agreementNotRequired;
    private String preTncText;
    private String tnc;
    private String buttonText;
    private Action buttonAction;

    public boolean isAgreed() {
        return agreed;
    }

    public void setAgreed(boolean agreed) {
        this.agreed = agreed;
    }

    public boolean isAgreementNotRequired() {
        return agreementNotRequired;
    }

    public void setAgreementNotRequired(boolean agreementNotRequired) {
        this.agreementNotRequired = agreementNotRequired;
    }

    public String getPreTncText() {
        return preTncText;
    }

    public void setPreTncText(String preTncText) {
        this.preTncText = preTncText;
    }

    public String getTnc() {
        return tnc;
    }

    public void setTnc(String tnc) {
        this.tnc = tnc;
    }

    public String getButtonText() {
        return buttonText;
    }

    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }

    public Action getButtonAction() {
        return buttonAction;
    }

    public void setButtonAction(Action buttonAction) {
        this.buttonAction = buttonAction;
    }
}
