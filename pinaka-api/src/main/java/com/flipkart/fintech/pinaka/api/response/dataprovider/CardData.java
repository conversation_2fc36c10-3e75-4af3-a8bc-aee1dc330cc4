package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by kunal.keshwani on 10/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardData {

    private String maskedCardNumber;
    private String bankName;
    private Image paymentMethodLogo;
    private Image bankLogo;
    private boolean selected;

    public String getMaskedCardNumber() {
        return maskedCardNumber;
    }

    public void setMaskedCardNumber(String maskedCardNumber) {
        this.maskedCardNumber = maskedCardNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Image getPaymentMethodLogo() {
        return paymentMethodLogo;
    }

    public void setPaymentMethodLogo(Image paymentMethodLogo) {
        this.paymentMethodLogo = paymentMethodLogo;
    }

    public Image getBankLogo() {
        return bankLogo;
    }

    public void setBankLogo(Image bankLogo) {
        this.bankLogo = bankLogo;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
