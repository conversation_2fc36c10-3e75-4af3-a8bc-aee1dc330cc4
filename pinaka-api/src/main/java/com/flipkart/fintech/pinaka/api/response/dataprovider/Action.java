package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.dataprovider.ActionType;
import com.flipkart.fintech.pinaka.api.response.TrackableComponent;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by rajat.mathur on 10/12/18.
 */

@ToString
public class Action extends TrackableComponent {

    private String actionText;
    private StyledText actionStyledText;
    private ActionType actionType;
    private String apiUrl;
    private String navigationUrl;
    private BottomSheetBehaviourConfig bottomSheetBehaviourConfig;
    //Note : also using context as a identifier for populateData api in rome
    private String context;
    private Map<String, Object> params = new HashMap<>();


    public StyledText getActionStyledText() {
        return actionStyledText;
    }

    public void setActionStyledText(StyledText actionStyledText) {
        this.actionStyledText = actionStyledText;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public String getActionText() {
        return actionText;
    }

    public void setActionText(String actionText) {
        this.actionText = actionText;
    }

    public ActionType getActionType() {
        return actionType;
    }

    public void setActionType(ActionType actionType) {
        this.actionType = actionType;
    }

    public String getNavigationUrl() {
        return navigationUrl;
    }

    public void setNavigationUrl(String navigationUrl) {
        this.navigationUrl = navigationUrl;
    }

    public BottomSheetBehaviourConfig getBottomSheetBehaviourConfig() {
        return bottomSheetBehaviourConfig;
    }

    public void setBottomSheetBehaviourConfig(BottomSheetBehaviourConfig bottomSheetBehaviourConfig) {
        this.bottomSheetBehaviourConfig = bottomSheetBehaviourConfig;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
}
