package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationStatusResponse {
    private String applicationId;
    private String status;
    private String lenderApplicationId;
    private String lenderStatus;
    private String lenderSubStatus;
}
