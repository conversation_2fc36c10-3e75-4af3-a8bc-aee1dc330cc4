package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.AutoCapitalize;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import com.flipkart.fintech.pinaka.api.enums.TextBoxInputType;

/**
 * Created by vaibhavgupta.v on 11/03/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TextBoxFormField extends FormField {

  private Integer maxLength;
  private Integer minLength;
  private Boolean multiline = Boolean.FALSE;
  private Integer numberOfLines = 1;
  private String validationRegex;
  private String regexValidationError;
  private String subText;
  private Action action;
  private AutoCapitalize autoCapitalize;
  private TextBoxInputType textBoxInputType;
  private String prefix;
  private String value;
  private Boolean showCharacterCount;
  private Boolean visible;
  private RichText title;

  public TextBoxFormField() {
    super(FormFieldType.TEXT);
  }


  public RichText getTitle() {
    return title;
  }

  public void setTitle(RichText title) {
    this.title = title;
  }

  public Boolean getMultiline() {
    return multiline;
  }

  public void setMultiline(Boolean multiline) {
    this.multiline = multiline;
  }

  public Integer getNumberOfLines() {
    return numberOfLines;
  }

  public void setNumberOfLines(Integer numberOfLines) {
    this.numberOfLines = numberOfLines;
  }

  public String getValidationRegex() {
    return validationRegex;
  }

  public void setValidationRegex(String validationRegex) {
    this.validationRegex = validationRegex;
  }

  public String getRegexValidationError() {
    return regexValidationError;
  }

  public void setRegexValidationError(String regexValidationError) {
    this.regexValidationError = regexValidationError;
  }

  public String getSubText() {
    return subText;
  }

  public void setSubText(String subText) {
    this.subText = subText;
  }

  public Action getAction() {
    return action;
  }

  public void setAction(Action action) {
    this.action = action;
  }

  public AutoCapitalize getAutoCapitalize() {
    return autoCapitalize;
  }

  public void setAutoCapitalize(AutoCapitalize autoCapitalize) {
    this.autoCapitalize = autoCapitalize;
  }

  public TextBoxInputType getTextBoxInputType() {
    return textBoxInputType;
  }

  public void setTextBoxInputType(TextBoxInputType textBoxInputType) {
    this.textBoxInputType = textBoxInputType;
  }

  public String getPrefix() {
    return prefix;
  }

  public void setPrefix(String prefix) {
    this.prefix = prefix;
  }

  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }

  public Integer getMaxLength() {
    return maxLength;
  }

  public void setMaxLength(Integer maxLength) {
    this.maxLength = maxLength;
  }

  public Integer getMinLength() {
    return minLength;
  }

  public void setMinLength(Integer minLength) {
    this.minLength = minLength;
  }

  public Boolean getShowCharacterCount() {
        return showCharacterCount;
  }
  public void setShowCharacterCount(Boolean showCharacterCount) {
        this.showCharacterCount = showCharacterCount;
  }
  public Boolean getVisible() {
        return visible;
  }

  public void setVisible(Boolean visible) {
        this.visible = visible;
    }
}