package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Contact<PERSON>orm<PERSON>ield extends FormField {

    private RichText title;
    private RichText edit;
    private RichText remove;
    private String prefix;
    private Header header;
    private String value;
    private String validationRegex;
    private String errorMessage;
    private String buttonTitle;
    private String tag;

    public ContactFormField() {
        super(FormFieldType.CONTACT);
    }

}
