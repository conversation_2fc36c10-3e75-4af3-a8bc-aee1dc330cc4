package com.flipkart.fintech.pinaka.api.request.v6.useraction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    property = "type",
    include = As.EXISTING_PROPERTY,
    visible = true
)
@JsonSubTypes({
    @JsonSubTypes.Type(name = "FORM", value = FormSubmitRequest.class),
    @JsonSubTypes.Type(name = "DOCUMENT", value = DocumentSubmitRequest.class),
    @JsonSubTypes.Type(name = "MANDATE", value = MandateSubmitRequest.class),
    @JsonSubTypes.Type(name = "VKYC", value = VkycSubmitRequest.class)
})
public abstract class UserActionRequest {
  private String accountId;
  private String smUserId;
  private String applicationId;
  private String taskId;
  private String processInstance;
  private String pageName;
  private String taskKey;
  private UserRequestActionType type;
}
