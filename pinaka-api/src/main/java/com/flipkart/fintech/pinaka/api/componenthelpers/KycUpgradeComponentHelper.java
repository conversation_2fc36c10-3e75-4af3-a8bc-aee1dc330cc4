package com.flipkart.fintech.pinaka.api.componenthelpers;

import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.winterfell.api.request.VariableData;

import java.util.HashMap;
import java.util.Map;

public class KycUpgradeComponentHelper {

    private KycUpgradeComponentHelper() {
    }

    private static final String CHANNEL               = "channel";
    private static final String APP_VERSION           = "appVersion";
    private static final String PENNY_DROP_REQUIRED   = "pennyDropRequired";
    private static final String PENNY_DROP_COMPLETED  = "pennyDropCompleted";
    private static final String PENNY_DROP_CONTEXT    = "pennyDropContext";
    private static final String FINANCIAL_PROVIDER    = "financialProvider";
    private static final String KYC_APPLICATION_ID    = "kycApplicationRefId";

    private static final String KYC_UPGRADE           = "KYC_UPGRADE";

    public static Map<String, VariableData> initializeKycUpgradeApplication(Channel channel, String appVersion, Lender financialProvider) {
        Map<String, VariableData > workflowData = new HashMap<>();
        setPennyDropRequired(workflowData, true);
        setChannel(workflowData, channel.name());
        setAppVersion(workflowData, appVersion);
        setFinancialProvider(workflowData, financialProvider.name());
        setPennyDropContext(workflowData, KYC_UPGRADE);
        return workflowData;
    }

    public static Map<String, VariableData> completeKycFormData(String kycApplicationRefId) {
        Map<String, VariableData > workflowData = new HashMap<>();
        setKycApplicationId(workflowData, kycApplicationRefId);
        return workflowData;
    }

    private static void setPennyDropRequired(Map<String, VariableData > workflowData, boolean pennyDropRequired) {
        workflowData.put(PENNY_DROP_REQUIRED, new VariableData(false, pennyDropRequired));
    }

    private static void setChannel(Map<String, VariableData > workflowData, String channel) {
        workflowData.put(CHANNEL, new VariableData(false, channel));
    }

    private static void setAppVersion(Map<String, VariableData > workflowData, String appVersion) {
        workflowData.put(APP_VERSION, new VariableData(false, appVersion));
    }

    private static void setPennyDropCompleted(Map<String, VariableData > workflowData, boolean pennyDropCompleted) {
        workflowData.put(PENNY_DROP_COMPLETED, new VariableData(false, pennyDropCompleted));
    }

    private static void setFinancialProvider(Map<String, VariableData > workflowData, String financialProvider) {
        workflowData.put(FINANCIAL_PROVIDER, new VariableData(false, financialProvider));
    }

    private static void setKycApplicationId(Map<String, VariableData > workflowData, String kycApplicationId) {
        workflowData.put(KYC_APPLICATION_ID, new VariableData(false, kycApplicationId));
    }

    private static void setPennyDropContext(Map<String, VariableData > workflowData, String pennyDropContext) {
        workflowData.put(PENNY_DROP_CONTEXT, new VariableData(false, pennyDropContext));
    }
}
