package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.EbcCallbackRequestStatus;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.HashMap;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EbcCallbackRequest {

    @NotNull
    @JsonProperty("fk_account_id")
    private String fkAccountId;

    @NotNull
    private EbcCallbackRequestStatus status;

    private String reason;

    private long limit;

    private HashMap context;

}
