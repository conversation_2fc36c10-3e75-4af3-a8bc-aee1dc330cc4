package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.List;

/**
 * Created by rajat.mathur on 10/12/18.
 */
public class InstructionsSlot extends Slot {

    public InstructionsSlot() {
        super(SlotType.INSTRUCTIONS);
    }

    private List<InstructionNote> instructions;

    public List<InstructionNote> getInstructions() {
        return instructions;
    }

    public void setInstructions(List<InstructionNote> instructions) {
        this.instructions = instructions;
    }
}
