package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.model.cfa.CardDetails;
import com.flipkart.fintech.pinaka.api.model.cfa.CreditDetails;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 05/04/21.
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class CfaCallbackRequest {

    @NotNull
    @JsonProperty("fk_account_id")
    private String fkAccountId;

    @NotNull
    @JsonProperty("fk_application_id")
    private String fkApplicationId;

    @NotNull
    @JsonProperty("application_status")
    private String applicationStatus;

    private String reason;

    @JsonProperty("credit_details")
    private CreditDetails creditDetails;

    @JsonProperty("card_details")
    private CardDetails cardDetails;

    @JsonProperty("custom_attributes")
    private Map<String, String> customAttributes;
}
