package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.List;

/**
 * Created by rajat.mathur on 10/12/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FormSlot extends Slot {

    public FormSlot() {
        super(SlotType.FORM);
    }

    private String title;
    private String subTitle;
    private String formId;
    private List<FormField> formFields;
    private ProductType productType;
    private Lender lender;
    private String subtitleFont;
    private String titleFont;
    private String subtitleFontFamily;
    private String titleFontFamily;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public List<FormField> getFormFields() {
        return formFields;
    }

    public void setFormFields(List<FormField> formFields) {
        this.formFields = formFields;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public Lender getLender() {
        return lender;
    }

    public void setLender(Lender lender) {
        this.lender = lender;
    }

    public String getSubtitleFont() {
        return subtitleFont;
    }

    public void setSubtitleFont(String subtitleFont) {
        this.subtitleFont = subtitleFont;
    }

    public String getTitleFont() {
        return titleFont;
    }

    public void setTitleFont(String titleFont) {
        this.titleFont = titleFont;
    }

    public String getSubtitleFontFamily() {
        return subtitleFontFamily;
    }

    public void setSubtitleFontFamily(String subtitleFontFamily) {
        this.subtitleFontFamily = subtitleFontFamily;
    }

    public String getTitleFontFamily() {
        return titleFontFamily;
    }

    public void setTitleFontFamily(String titleFontFamily) {
        this.titleFontFamily = titleFontFamily;
    }
}
