package com.flipkart.fintech.pinaka.api.response.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import com.flipkart.rome.datatypes.response.fintech.supermoney.AccordionItemValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.RichTitleValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.AccordionWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.ValueType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class AccordianWidgetDeserializer extends StdDeserializer<AccordionWidgetData> {

    public AccordianWidgetDeserializer() {
        this(null);

    }

    public AccordianWidgetDeserializer(Class<?> vc) {
        super(vc);
    }


    @Override
    public AccordionWidgetData deserialize(JsonParser jsonParser, DeserializationContext context)
            throws IOException {
        AccordionWidgetData accordionWidgetData = new AccordionWidgetData();
        List<RenderableComponent<AccordionItemValue>> renderableComponents =new ArrayList<>();
        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        JsonNode accordionWidgetDataJson = mapper.readTree(jsonParser);
        JsonNode renderableComponentsJson = accordionWidgetDataJson.get("renderableComponents");
        for(JsonNode jsonNode:renderableComponentsJson)
        {
            RenderableComponent<AccordionItemValue>renderableComponent = new RenderableComponent<>();
            AccordionItemValue accordionItemValue = new AccordionItemValue();
            JsonNode accordianItemJson = jsonNode.get("value");
            JsonNode titleNode = accordianItemJson.get("title");
            JsonNode titleType = titleNode.get("type");
            JsonNode contentTypeNode = accordianItemJson.get("contentType");
            JsonNode contentNode = accordianItemJson.get("content");
            if (titleType != null) {
                accordionItemValue.setTitle(mapper.treeToValue(titleNode, RichTitleValue.class));
            }
            if (contentTypeNode != null) {
                String contentType = mapper.treeToValue(contentTypeNode, String.class);
                accordionItemValue.setContentType(ValueType.valueOf(contentType));
                accordionItemValue.setContent(
                        (Value) mapper.treeToValue(contentNode, ValueType.valueOf(contentType).getClazz()));
            }
            renderableComponent.setValue(accordionItemValue);
            renderableComponents.add(renderableComponent);
        }
        accordionWidgetData.setRenderableComponents(renderableComponents);
        return accordionWidgetData;
    }
}
