package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AutopayDetailsResponse {
    ApplicationStatus status;
    Boolean applicable = false;
    Boolean mandatory = false;
    Boolean completed = false;
    int retryCount;
    String errorMessage;
}
