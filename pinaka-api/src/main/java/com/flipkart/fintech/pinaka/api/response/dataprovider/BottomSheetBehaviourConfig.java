package com.flipkart.fintech.pinaka.api.response.dataprovider;


import com.flipkart.fintech.pinaka.api.enums.dataprovider.BottomSheetContentMode;

/**
 * Created by rajat.mathur on 15/12/18.
 */
public class BottomSheetBehaviourConfig {

    private BottomSheetContentMode contentMode;

    private Double peekPercentage;

    public BottomSheetBehaviourConfig() {
        contentMode = BottomSheetContentMode.DEFAULT;
    }

    public BottomSheetContentMode getContentMode() {
        return contentMode;
    }

    public void setContentMode(BottomSheetContentMode contentMode) {
        this.contentMode = contentMode;
    }

    public Double getPeekPercentage() {
        return peekPercentage;
    }

    public void setPeekPercentage(Double peekPercentage) {
        this.peekPercentage = peekPercentage;
    }
}
