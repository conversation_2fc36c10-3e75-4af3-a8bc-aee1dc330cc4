package com.flipkart.fintech.pinaka.api.response.dataprovider;

import java.util.Map;

/**
 * Created by rajat.mathur on 11/12/18.
 */
public class PageResponse {

    private Map<Integer, Slot> slots;

    private Map<String, Action> pageEventResponseMap;

    public Map<Integer, Slot> getSlots() {
        return slots;
    }

    public void setSlots(Map<Integer, Slot> slots) {
        this.slots = slots;
    }

    public Map<String, Action> getPageEventResponseMap() {
        return pageEventResponseMap;
    }

    public void setPageEventResponseMap(Map<String, Action> pageEventResponseMap) {
        this.pageEventResponseMap = pageEventResponseMap;
    }
}
