package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AutopayMandateCreateResponse extends GenericApiActionResponse {

    @JsonProperty("mandate_identifier")
    private String mandateIdentifier;

    @JsonProperty("mandate_token")
    private String mandateToken;

    @JsonProperty("mandate_status")
    private String mandateStatus;

    @JsonProperty("redirection_url")
    private String redirectionURL;

    public String getMandateIdentifier() {
        return mandateIdentifier;
    }

    public void setMandateIdentifier(String mandateIdentifier) {
        this.mandateIdentifier = mandateIdentifier;
    }

    public String getMandateToken() {
        return mandateToken;
    }

    public void setMandateToken(String mandateToken) {
        this.mandateToken = mandateToken;
    }

    public String getMandateStatus() {
        return mandateStatus;
    }

    public void setMandateStatus(String mandateStatus) {
        this.mandateStatus = mandateStatus;
    }

    public String getRedirectionURL() {
        return redirectionURL;
    }

    public void setRedirectionURL(String redirectionURL) {
        this.redirectionURL = redirectionURL;
    }

}

