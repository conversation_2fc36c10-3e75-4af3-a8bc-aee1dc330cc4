package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpdateApplicationStatusRequest {

    private String accountId;
    @SensitiveField(keyName = "cbcKey")
    private String mobileNumber;
    private String caseReferenceId;
    private String c1Number;
    private STATUS status;
    private boolean success;
    private String message;
    private String applicationSerno;
    private String validationToken;
    private boolean isCardApproved;
}
