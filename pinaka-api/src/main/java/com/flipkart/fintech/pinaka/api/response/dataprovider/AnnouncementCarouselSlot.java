package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by kunal.keshwani on 10/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnouncementCarouselSlot extends Slot {

    public AnnouncementCarouselSlot() {
        super(SlotType.ANNOUNCEMENT_CAROUSEL);
    }

    private List<AnnouncementCarouselData> announcementCarousalList = new ArrayList<>();

    public List<AnnouncementCarouselData> getAnnouncementCarousalList() {
        return announcementCarousalList;
    }

    public void setAnnouncementCarousalList(List<AnnouncementCarouselData> announcementCarousalList) {
        this.announcementCarousalList = announcementCarousalList;
    }

    public void addAnnouncementCarousalData(AnnouncementCarouselData data) {
        this.announcementCarousalList.add(data);
    }

}
