package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConsentDetails {
    ConsentType consentType;
    private String userIP;
    private String deviceId;
    private String deviceInfo;
    private String currentTimeStamp;
}
