package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import com.flipkart.fintech.pinaka.api.model.Address;
import lombok.Data;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddressPickerFormField extends FormField{
    private Address homeAddress;
    private Address workAddress;
    private Action homeAddressAction;
    private Action workAddressAction;
    private boolean homeAddressServiceable;
    private boolean workAddressServiceable;
    private boolean radioViewDisabled;
    private Address value;
    private Map<String, Integer> containerStyle;

    public AddressPickerFormField() {
        super(FormFieldType.ADDRESS_PICKER);
    }

}
