package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;


public class RadioSelectionValue {

    @JsonProperty("title")
    private StyledText title;

    @JsonProperty("subtitle")
    private StyledText subtitle;

    @JsonProperty("selected")
    private boolean selected;

    @JsonProperty("action")
    private Action action;

    public StyledText getTitle() {
        return title;
    }

    public void setTitle(StyledText title) {
        this.title = title;
    }

    public StyledText getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(StyledText subtitle) {
        this.subtitle = subtitle;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }
}
