package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.enums.Lender;

import javax.validation.constraints.NotNull;

public class LenderAccountDetailsResponse {

  @NotNull
  private String lenderUserRefId;

  @NotNull
  private Lender lender;

  public LenderAccountDetailsResponse(String lenderUserRefId,
                                      Lender lender) {
    this.lenderUserRefId = lenderUserRefId;
    this.lender = lender;
  }

  public LenderAccountDetailsResponse() {
    this.lenderUserRefId = "";
  }

  public String getLenderUserRefId() {
    return lenderUserRefId;
  }

  public void setLenderUserRefId(String lenderUserRefId) {
    this.lenderUserRefId = lenderUserRefId;
  }

  public Lender getLender() {
    return lender;
  }

  public void setLender(Lender lender) {
    this.lender = lender;
  }
}
