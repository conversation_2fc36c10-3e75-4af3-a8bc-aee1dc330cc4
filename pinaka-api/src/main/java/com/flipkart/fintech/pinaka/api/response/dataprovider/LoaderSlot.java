package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.ApplicationWorkflowType;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.enums.ProgressBarShapeType;
import com.flipkart.fintech.pinaka.api.enums.dataprovider.LoaderType;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import lombok.Data;

/**
 * Created by rajat.mathur on 10/12/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LoaderSlot extends Slot {

    public LoaderSlot() {
        super(SlotType.LOADER);
    }

    private LoaderType loaderType;
    private String title;
    private String subTitle;
    private String message;
    private Image image;
    private boolean hideProgressBar;
    private Integer loaderTimeout;
    private String context;
    private ApplicationWorkflowType workflowType;
    private ProductType productType;
    private int refreshInterval;
    private String landingUrl;
    private String apiUrl;
    private String apiHttpMethod;
    private ProgressBarShapeType progressBarShape;
    private boolean invokeOnMount;
    private String expiredStateTitle;
    private String expiredStateSubtitle;
}
