package com.flipkart.fintech.pinaka.api.response.dataprovider;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by rajat.mathur on 07/01/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Option {

    private String id;
    private String title;
    private Action action;
    private boolean disabled;
    private String disabledText;

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getDisabledText() {
        return disabledText;
    }

    public void setDisabledText(String disabledText) {
        this.disabledText = disabledText;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }
}
