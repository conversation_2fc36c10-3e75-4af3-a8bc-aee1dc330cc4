package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.*;
import com.flipkart.sensitive.annotation.SensitiveField;


/**
 * Created by kunal.keshwani on 28/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CbcApplication {

    @JsonProperty("name_on_card")
    private String nameOnCard;
    @JsonProperty("card_name")
    private String cardName;
    @JsonProperty("mothers_name")
    private String mothersName;
    @JsonProperty("pan")
    @SensitiveField
    private String pan;
    @JsonProperty("mobile_number")
    @SensitiveField
    private String mobileNumber;
    @JsonProperty("date_of_birth")
    private String dateOfBirth;
    @JsonProperty("gender")
    private Gender gender;
    @JsonProperty("marital_status")
    private MaritalStatus maritalStatus;
    @JsonProperty("education")
    private String education;
    @JsonProperty("annual_income")
    private String annualIncome;
    @JsonProperty("current_organization_name")
    private String currentOrganizationName;
    @JsonProperty("industry_type")
    private  String industryType;
    @JsonProperty("name_of_business")
    private  String nameOfBusiness;
    @JsonProperty("ownership_type")
    private  String ownershipType;
    @JsonProperty("nature_of_business")
    private  String natureOfBusiness;
    @JsonProperty("email")
    @SensitiveField
    private String email;
    // TODO: have to make a new status enum for CBC.
    @JsonProperty("employment_status")
    private String employmentStatus;
    @JsonProperty("auto_debit")
    private String autoDebit;

    @JsonProperty("address")
    private Address address;
    @JsonProperty("delivery_address")
    private YesNoType deliveryAddress;
    @JsonProperty("permanent_address")
    private YesNoType permanentAddress;
    @JsonProperty("address_type")
    private AddressLocationType addressLocationType;
    @JsonProperty("delivery_address_type")
    private AddressLocationType deliveryAddressLocationType;
    @JsonProperty("home_address")
    private Address homeAddress;
    @JsonProperty("work_address")
    private Address workAddress;

    @JsonProperty("card_usage_type")
    private CardUsageType cardUsageType;
    @JsonProperty("increase_credit_limit")
    private boolean increaseCreditLimit;
    @JsonProperty("card_protection_plan")
    private boolean cardProtectionPlan;
    @JsonProperty("contact_less_transactions")
    private boolean contactLessTransactions;
    @JsonProperty("online_offline")
    private String onlineOffline;
    @JsonProperty("international_domestic")
    private String internationalDomestic;
    @JsonProperty("annual_income_text")
    private String annualIncomeText;
    @JsonProperty("employer_sector")
    private String employerSector;
    @JsonProperty("years_in_business")
    private String yearsInBusiness;
    @JsonProperty("existing_employer")
    private boolean existingEmployer;

    @JsonProperty("father_first_name")
    private String fatherFirstName;
    @JsonProperty("father_last_name")
    private String fatherLastName;
    @JsonProperty("caste")
    private String caste;
    @JsonProperty("residence_type")
    private String residenceType;
    @JsonProperty("is_official")
    private String isOfficial;
    @JsonProperty("name_of_official")
    private String nameOfOffical;
    @JsonProperty("name_of_bank")
    private String nameOfBank;
    @JsonProperty("position_in_bank")
    private String positionInBank;
    @JsonProperty("relationship")
    private String relationship;

    public boolean isContactLessTransactions() {
        return contactLessTransactions;
    }

    public void setContactLessTransactions(boolean contactLessTransactions) {
        this.contactLessTransactions = contactLessTransactions;
    }

    public String getOnlineOffline() {
        return onlineOffline;
    }

    public void setOnlineOffline(String onlineOffline) {
        this.onlineOffline = onlineOffline;
    }

    public String getInternationalDomestic() {
        return internationalDomestic;
    }

    public void setInternationalDomestic(String internationalDomestic) {
        this.internationalDomestic = internationalDomestic;
    }

    public String getNameOnCard() {
        return nameOnCard;
    }

    public void setNameOnCard(String nameOnCard) {
        this.nameOnCard = nameOnCard;
    }

    public String getMothersName() {
        return mothersName;
    }

    public void setMothersName(String mothersName) {
        this.mothersName = mothersName;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmploymentStatus() {
        return employmentStatus;
    }

    public void setEmploymentStatus(String employmentStatus) {
        this.employmentStatus = employmentStatus;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public YesNoType getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(YesNoType deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public YesNoType getPermanentAddress() {
        return permanentAddress;
    }

    public void setPermanentAddress(YesNoType permanentAddress) {
        this.permanentAddress = permanentAddress;
    }

    public Address getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(Address homeAddress) {
        this.homeAddress = homeAddress;
    }

    public Address getWorkAddress() {
        return workAddress;
    }

    public void setWorkAddress(Address workAddress) {
        this.workAddress = workAddress;
    }

    public CardUsageType getCardUsageType() {
        return cardUsageType;
    }

    public void setCardUsageType(CardUsageType cardUsageType) {
        this.cardUsageType = cardUsageType;
    }

    public boolean isIncreaseCreditLimit() {
        return increaseCreditLimit;
    }

    public void setIncreaseCreditLimit(boolean increaseCreditLimit) {
        this.increaseCreditLimit = increaseCreditLimit;
    }

    public boolean isCardProtectionPlan() {
        return cardProtectionPlan;
    }

    public void setCardProtectionPlan(boolean cardProtectionPlan) {
        this.cardProtectionPlan = cardProtectionPlan;
    }

    public AddressLocationType getAddressLocationType() {
        return addressLocationType;
    }

    public void setAddressLocationType(AddressLocationType addressLocationType) {
        this.addressLocationType = addressLocationType;
    }

    public AddressLocationType getDeliveryAddressLocationType() {
        return deliveryAddressLocationType;
    }

    public void setDeliveryAddressLocationType(AddressLocationType deliveryAddressLocationType) {
        this.deliveryAddressLocationType = deliveryAddressLocationType;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public MaritalStatus getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(MaritalStatus maritalStatus) {
        this.maritalStatus = MaritalStatus.MARRIED;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getAnnualIncome() {
        return annualIncome;
    }

    public void setAnnualIncome(String annualIncome) {
        this.annualIncome = annualIncome;
    }

    public String getCurrentOrganizationName() {
        return currentOrganizationName;
    }

    public void setCurrentOrganizationName(String currentOrganizationName) {
        this.currentOrganizationName = currentOrganizationName;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public String getNameOfBusiness() {
        return nameOfBusiness;
    }

    public void setNameOfBusiness(String nameOfBusiness) {
        this.nameOfBusiness = nameOfBusiness;
    }

    public String getOwnershipType() {
        return ownershipType;
    }

    public void setOwnershipType(String ownershipType) {
        this.ownershipType = ownershipType;
    }

    public String getNatureOfBusiness() {
        return natureOfBusiness;
    }

    public void setNatureOfBusiness(String natureOfBusiness) {
        this.natureOfBusiness = natureOfBusiness;
    }

    public String getAutoDebit() {
        return autoDebit;
    }

    public void setAutoDebit(String autoDebit) {
        this.autoDebit = autoDebit;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getAnnualIncomeText() {
        return annualIncomeText;
    }

    public void setAnnualIncomeText(String annualIncomeText) {
        this.annualIncomeText = annualIncomeText;
    }

    public String getEmployerSector() {
        return employerSector;
    }

    public void setEmployerSector(String employerSector) {
        this.employerSector = employerSector;
    }

    public String getYearsInBusiness() {
        return yearsInBusiness;
    }

    public void setYearsInBusiness(String yearsInBusiness) {
        this.yearsInBusiness = "0";
    }

    public boolean getExistingEmployer() {
        return existingEmployer;
    }

    public void setExistingEmployer(boolean existingEmployer) {
        this.existingEmployer =existingEmployer;
    }

    public String getFatherFirstName() {
        return fatherFirstName;
    }

    public void setFatherFirstName(String fatherFirstName) {
        this.fatherFirstName = fatherFirstName;
    }

    public String getFatherLastName() {
        return fatherLastName;
    }

    public void setFatherLastName(String fatherLastName) {
        this.fatherLastName = fatherLastName;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getResidenceType() {
        return residenceType;
    }

    public void setResidenceType(String residenceType) {
        this.residenceType = residenceType;
    }

    public String getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(String isOfficial) {
        this.isOfficial = isOfficial;
    }

    public String getNameOfOffical() {
        return nameOfOffical;
    }

    public void setNameOfOffical(String nameOfOffical) {
        this.nameOfOffical = nameOfOffical;
    }

    public String getNameOfBank() {
        return nameOfBank;
    }

    public void setNameOfBank(String nameOfBank) {
        this.nameOfBank = nameOfBank;
    }

    public String getPositionInBank() {
        return positionInBank;
    }

    public void setPositionInBank(String positionInBank) {
        this.positionInBank = positionInBank;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    @Override
    public String toString() {
        return "CbcApplication{" +
            "nameOnCard='" + nameOnCard + '\'' +
            ", cardName='" + cardName + '\'' +
            ", mothersName='" + mothersName + '\'' +
            ", dateOfBirth='" + dateOfBirth + '\'' +
            ", gender=" + gender +
            ", maritalStatus=" + maritalStatus +
            ", education='" + education + '\'' +
            ", annualIncome='" + annualIncome + '\'' +
            ", currentOrganizationName='" + currentOrganizationName + '\'' +
            ", industryType='" + industryType + '\'' +
            ", nameOfBusiness='" + nameOfBusiness + '\'' +
            ", ownershipType='" + ownershipType + '\'' +
            ", natureOfBusiness='" + natureOfBusiness + '\'' +
            ", employmentStatus='" + employmentStatus + '\'' +
            ", autoDebit='" + autoDebit + '\'' +
            ", deliveryAddress=" + deliveryAddress +
            ", permanentAddress=" + permanentAddress +
            ", addressLocationType=" + addressLocationType +
            ", deliveryAddressLocationType=" + deliveryAddressLocationType +
            ", cardUsageType=" + cardUsageType +
            ", increaseCreditLimit=" + increaseCreditLimit +
            ", cardProtectionPlan=" + cardProtectionPlan +
            ", contactLessTransactions=" + contactLessTransactions +
            ", onlineOffline='" + onlineOffline + '\'' +
            ", internationalDomestic='" + internationalDomestic + '\'' +
            ", annualIncomeText='" + annualIncomeText + '\'' +
            ", employerSector='" + employerSector + '\'' +
            ", yearsInBusiness='" + yearsInBusiness + '\'' +
            ", fatherFirstName='" + fatherFirstName + '\'' +
            ", fatherLastName='" + fatherLastName + '\'' +
            ", caste='" + caste + '\'' +
            ", residenceType='" + residenceType + '\'' +
            ", isOfficial=" + isOfficial +
            ", nameOfOffical='" + nameOfOffical + '\'' +
            ", nameOfBank='" + nameOfBank + '\'' +
            ", positionInBank='" + positionInBank + '\'' +
            ", relationship='" + relationship + '\'' +
            '}';
    }
}