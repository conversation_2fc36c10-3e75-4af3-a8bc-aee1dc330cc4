package com.flipkart.fintech.pinaka.api.response.v3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.affordability.underwriting.model.CreditConfig;
import com.flipkart.affordability.underwriting.model.DataTypeInfo;
import com.flipkart.affordability.underwriting.model.SublimitType;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pinaka.api.enums.RejectReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
public class RevalEligibilityResponse extends EligibilityResponse {
    Map<SublimitType, Boolean> newLimitTypeMap;

    @Builder(builderMethodName = "revalEligibilityResponseBuilder")
    public RevalEligibilityResponse(String referenceNumber, Double eligibleAmount, boolean isEligible, int customerCibilBucket, String lenderReferenceNumber, String info, STATUS status, Integer cibilScoreBucket, Boolean autopaySetupMandatory, Boolean autopaySetupApplicable, CreditConfig creditConfig, List<DataTypeInfo> dataTypeInfoList, boolean smsUpgradable, boolean bureauUpgradable, Double intentScore, Double affordabilityLowerBound, Double affordabilityUpperBound, Double affordabilityScore, RejectReason rejectReason, Boolean limitUpgraded, Boolean isExistingCustomer, String lmsConfigId, int age, String pincode, String ruleName, Map<SublimitType, Boolean> newLimitTypeMap) {
        super(referenceNumber, eligibleAmount, isEligible, customerCibilBucket, lenderReferenceNumber, info, status, cibilScoreBucket, autopaySetupMandatory, autopaySetupApplicable, creditConfig, dataTypeInfoList, smsUpgradable, bureauUpgradable, intentScore, affordabilityLowerBound, affordabilityUpperBound, affordabilityScore, rejectReason, limitUpgraded, isExistingCustomer, lmsConfigId, age, pincode, ruleName);
        this.newLimitTypeMap = newLimitTypeMap;
    }
}
