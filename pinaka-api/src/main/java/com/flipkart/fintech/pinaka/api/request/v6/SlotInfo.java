package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.annotation.Nullable;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@NoArgsConstructor
@ToString
public class SlotInfo {

    private int slotId;
    private WidgetTypeV4 widgetTypeV4;
    @Nullable
    private String contentId;
    @Nullable
    private String formType;
    @Nullable
    private String staticContentId;

    public SlotInfo(int slotId, WidgetTypeV4 widgetTypeV4, @Nullable String formType) {
        this.slotId = slotId;
        this.widgetTypeV4 = widgetTypeV4;
        this.formType = formType;
    }

    public SlotInfo(int slotId, WidgetTypeV4 widgetTypeV4) {
        this.slotId = slotId;
        this.widgetTypeV4 = widgetTypeV4;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public void setStaticContentId(@Nullable String staticContentId) {
        this.staticContentId = staticContentId;
    }
}
