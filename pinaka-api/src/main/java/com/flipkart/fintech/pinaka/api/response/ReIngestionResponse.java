package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ReIngestionResponse {

    @JsonProperty("message")
    private String message;

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ReIngestionResponse{" +
                "message=" + message;
    }
}
