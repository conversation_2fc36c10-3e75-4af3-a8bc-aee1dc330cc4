package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.response.TrackableComponent;

import java.util.List;

/**
 * Created by kunal.keshwani on 31/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class KnowMoreContent extends TrackableComponent {
    private String title;
    private List<String> callouts;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getCallouts() {
        return callouts;
    }

    public void setCallouts(List<String> callouts) {
        this.callouts = callouts;
    }

    @Override
    public String toString() {
        return "KnowMoreContent{" +
                "title='" + title + '\'' +
                ", callouts=" + callouts +
                '}';
    }
}
