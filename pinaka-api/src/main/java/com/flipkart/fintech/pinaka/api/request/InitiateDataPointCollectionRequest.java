package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class InitiateDataPointCollectionRequest {
    private String dataPointType;
    private String userDeviceId;
    private String userIpAddress;
    private Date   consentValidUpto;
    private String consentTncUrl;
    private String requestId; // is tracking id
}
