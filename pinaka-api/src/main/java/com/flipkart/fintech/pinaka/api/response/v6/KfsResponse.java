package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.v6.Value;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KfsResponse{
    private Status status;

    private long loanAmount;

    private BigDecimal interestCharge;

    private BigDecimal processingFeecharge;

    private BigDecimal processingFeeTax;

    private BigDecimal totalProcessingFee;

    private BigDecimal insurance;

    private BigDecimal totalStampDuty;

    private BigDecimal netDisbursedAmount;

    private BigDecimal totalAmount;

    private BigDecimal totalCharges;

    private BigDecimal eapr;

    private int tenureOfLoan;

    private Value repaymentFrequency;

    private int noOfInstallments;

    private BigDecimal amountOfEachInstallment;

    private BigDecimal bounceCharge;

    private BigDecimal bounceChargeTax;

    private BigDecimal totalBounceCharge;

    private BigDecimal value;

    private BigDecimal foreClosureCharges;

    private String duplicateStatementIssuance;

    private String duplicateAmortizationSchedule;

    private String duplicateInterestCertificate;

    private String cibilIssuanceCharges;

    private String chargesAgreementPhotocopy;

    private CoolingOffUnit unit;

    private int foreclosureCoolingOffPerValue;

    private String swapCharges;

    private String name;

    private String designation;

    private String address;

    private String phoneNumber;

    private String lspdetails;

    private List<EriDetailDTOS> eriDetailDTOS;

    private String tnc;

    private String latePaymentInterestDescription;

    private String redirectionUrl;

    private BigDecimal roi;

}
