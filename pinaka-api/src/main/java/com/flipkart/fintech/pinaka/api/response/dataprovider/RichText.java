package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by kunal.keshwani on 04/06/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RichText {
    private String text;
    private String color;
    private String fontSize;
    private FontStyle fontStyle;
    private TextAlign textAlign;
    private Integer lineHeight;
    private FontWeight fontWeight;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getFontSize() {
        return fontSize;
    }

    public void setFontSize(String fontSize) {
        this.fontSize = fontSize;
    }

    public FontStyle getFontStyle() {
        return fontStyle;
    }

    public void setFontStyle(FontStyle fontStyle) {
        this.fontStyle = fontStyle;
    }

    public TextAlign getTextAlign() {
        return textAlign;
    }

    public void setTextAlign(TextAlign textAlign) {
        this.textAlign = textAlign;
    }

    public Integer getLineHeight() {
        return lineHeight;
    }

    public void setLineHeight(Integer lineHeight) {
        this.lineHeight = lineHeight;
    }

    public FontWeight getFontWeight() {
        return fontWeight;
    }

    public void setFontWeight(FontWeight fontWeight) {
        this.fontWeight = fontWeight;
    }

    @Override
    public String toString() {
        return "RichText{" +
                "text='" + text + '\'' +
                ", color='" + color + '\'' +
                ", fontSize='" + fontSize + '\'' +
                ", fontStyle=" + fontStyle +
                ", textAlign=" + textAlign +
                ", lineHeight=" + lineHeight +
                '}';
    }
}
