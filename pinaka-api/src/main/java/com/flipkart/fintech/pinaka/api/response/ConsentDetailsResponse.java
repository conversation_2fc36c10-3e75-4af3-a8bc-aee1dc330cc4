package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsentDetailsResponse {
    private Boolean isConsentGiven = Boolean.FALSE;
    private String  consentType;
    private String  metaData;
    private String  deviceId;
    private String  ipAddress;
    private String  tncUrl;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "IST")
    private Date    validUpto;
}
