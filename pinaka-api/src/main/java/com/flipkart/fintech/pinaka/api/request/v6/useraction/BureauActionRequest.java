package com.flipkart.fintech.pinaka.api.request.v6.useraction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BureauActionRequest extends FormSubmitRequestV2 {
    private String merchantAccountId;
    private String smAccountId;
}