package com.flipkart.fintech.pinaka.api.response.dataprovider;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import lombok.Data;

import java.util.List;

@Data
public class ApplicationStatusSlot extends Slot {

    private Image clockIcon;
    private RichText title;
    private List<ApplicationStatusStep> steps;
    public ApplicationStatusSlot() {
        super(SlotType.APPLICATION_STATUS);
    }

}
