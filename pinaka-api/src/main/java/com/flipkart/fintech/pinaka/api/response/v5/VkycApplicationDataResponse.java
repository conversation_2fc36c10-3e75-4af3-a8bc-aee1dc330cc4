package com.flipkart.fintech.pinaka.api.response.v5;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.stratum.api.models.vkyc.VideoKycEventName;
import com.flipkart.fintech.stratum.api.models.vkyc.VkycApplicationData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Core application response model for crud operation
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.LowerCaseWithUnderscoresStrategy.class)
public class VkycApplicationDataResponse {
    private String applicationReferenceId;
    private VideoKycEventName eventName;
    private VkycApplicationData vkycApplicationData;
    private String formKey;
}
