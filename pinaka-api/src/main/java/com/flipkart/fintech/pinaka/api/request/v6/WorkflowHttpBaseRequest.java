package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowHttpBaseRequest {
    @NotNull
    @JsonProperty("application_id")
    private String applicationId;
    @NotNull
    @JsonProperty("account_id")
    private String accountId;
    @JsonProperty("sm_user_id")
    private String smUserId;
    @NotNull
    @JsonProperty("request_id")
    private String requestId;
    @NotNull
    @JsonProperty("category_type")
    private String categoryType;
}