package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.List;

/**
 * Created by rajat.mathur on 10/12/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CalloutSlot extends Slot {

    public CalloutSlot() {
        super(SlotType.CALLOUT);
    }

    private String title;
    private String subTitle;
    private List<Callout> callouts;
    private String footerTitle;
    private Action footerAction;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<Callout> getCallouts() {
        return callouts;
    }

    public void setCallouts(List<Callout> callouts) {
        this.callouts = callouts;
    }

    public String getFooterTitle() {
        return footerTitle;
    }

    public void setFooterTitle(String footerTitle) {
        this.footerTitle = footerTitle;
    }

    public Action getFooterAction() {
        return footerAction;
    }

    public void setFooterAction(Action footerAction) {
        this.footerAction = footerAction;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }
}
