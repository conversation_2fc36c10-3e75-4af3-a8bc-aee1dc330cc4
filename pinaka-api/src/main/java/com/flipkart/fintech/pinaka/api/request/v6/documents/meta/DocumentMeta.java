package com.flipkart.fintech.pinaka.api.request.v6.documents.meta;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document.DocumentTypes;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    property = "documentType",
    visible = true
)
@JsonSubTypes({
    @JsonSubTypes.Type(name = DocumentTypes.AADHAAR_XML, value = AadhaarXmlMeta.class),
    @JsonSubTypes.Type(name = DocumentTypes.KYC_IMAGE, value = KycImageMeta.class),
    @JsonSubTypes.Type(name = DocumentTypes.SELFIE_IMAGE, value = SelfieImageMeta.class)
})
public abstract class DocumentMeta {
  private String documentType;
}
