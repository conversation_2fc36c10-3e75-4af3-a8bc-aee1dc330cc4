package com.flipkart.fintech.pinaka.api.response.v3.discovery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 05/10/20.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class FetchUserEBCEligibilityResponse {
    private boolean eligibleForJourney;
    private String  bannerUrl;
    private int     height;
    private int     width;
    private String  landingUrl;
    private boolean killCurrentPage;
}
