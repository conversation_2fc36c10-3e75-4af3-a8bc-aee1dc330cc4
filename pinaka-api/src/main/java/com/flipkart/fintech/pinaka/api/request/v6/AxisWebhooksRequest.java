package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
public class AxisWebhooksRequest extends WebhooksRequest {
    private Stage stage;

    @JsonCreator
    @Builder
    public AxisWebhooksRequest(@JsonProperty("lsp_application_id") String lspApplicationId, @JsonProperty("lender_application_id") String lenderApplicationId,
                               @JsonProperty("application_status") String applicationStatus, @JsonProperty("state_transition_time") Long stateTransitionTime, @JsonProperty("stage") Stage stage) {
        super(lspApplicationId, lenderApplicationId, applicationStatus, stateTransitionTime);
        this.stage = stage;
    }
}
