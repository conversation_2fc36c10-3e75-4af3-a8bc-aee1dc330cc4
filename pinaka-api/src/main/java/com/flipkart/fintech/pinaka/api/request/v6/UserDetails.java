package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDetails {
    private String pan;
    private String dob;
    private String gender;
    private String shippingAddressId;

    private String houseNumber;
    private String area;
    private String city;
    private String state;
    private String pincode;
    // TODO encapsulate
    private String binScore;
    private EmploymentType employmentType;
    private String employerId;
    private String employerName;
    private String industryId;
    private String industryName;
    private BigDecimal monthlyIncome;
    private BigDecimal annualTurnOver;
    private boolean panConsentProvided;
    private boolean offerConsentProvided;
    private String segment;
    private String subsegment;
}
