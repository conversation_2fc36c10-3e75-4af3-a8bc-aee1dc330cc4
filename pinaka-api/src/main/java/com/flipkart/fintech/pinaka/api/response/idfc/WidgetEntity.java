package com.flipkart.fintech.pinaka.api.response.idfc;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize.Inclusion;
import com.flipkart.fintech.pinaka.api.response.deserializer.WidgetEntityDeserializer;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@JsonSerialize(include = Inclusion.NON_NULL)
@Data
@JsonDeserialize(using = WidgetEntityDeserializer.class)
@NoArgsConstructor
@AllArgsConstructor
public class WidgetEntity {

    @JsonProperty("slotId")
    private int slotId;
    @JsonProperty("widgetType")
    private WidgetTypeV4 widgetType;
    @JsonProperty("widgetData")
    private WidgetData widgetData;

}
