package com.flipkart.fintech.pinaka.api.response.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NavigationActionInfo {

    @JsonProperty("landing_url")
    private String landingUrl;

    @JsonProperty("kill_page")
    private Boolean killPage;
}
