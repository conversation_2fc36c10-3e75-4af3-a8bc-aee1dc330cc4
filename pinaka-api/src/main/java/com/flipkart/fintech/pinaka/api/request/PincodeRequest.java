package com.flipkart.fintech.pinaka.api.request;

import com.flipkart.fintech.pinaka.api.enums.CbcKycMethod;
import com.flipkart.fintech.pinaka.api.enums.KycServicibilityType;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PincodeRequest {

    @NotNull
    private ProductType productType;

    @NotNull
    private Lender lender;

    @NotNull
    private String pincode;

    private CbcKycMethod cbcKycMethod;

    private KycServicibilityType kycServicibilityType;
}
