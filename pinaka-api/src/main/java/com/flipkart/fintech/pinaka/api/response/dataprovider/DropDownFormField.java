package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import com.flipkart.fintech.pinaka.api.enums.dataprovider.DisplayType;
import com.flipkart.fintech.pinaka.api.enums.dataprovider.StackType;

import java.util.List;

/**
 * Created by rajat.mathur on 07/01/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DropDownFormField extends FormField {

    private Image icon;
    private String title;
    private String validationRegex;
    private DisplayType displayType;
    private StackType stackType;
    private Option selectedOption;
    private List<Option> options;
    private String subText;
    private Boolean fireTrackingOnce;
    private String notes;

    public DropDownFormField() {
        super(FormFieldType.DROPDOWN);
    }

    public Image getIcon() {
        return icon;
    }

    public void setIcon(Image icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<Option> getOptions() {
        return options;
    }

    public void setOptions(List<Option> options) {
        this.options = options;
    }

    public DisplayType getDisplayType() {
        return displayType;
    }

    public void setDisplayType(DisplayType displayType) {
        this.displayType = displayType;
    }

    public Option getSelectedOption() {
        return selectedOption;
    }

    public void setSelectedOption(Option selectedOption) {
        this.selectedOption = selectedOption;
    }

    public StackType getStackType() {
        return stackType;
    }

    public void setStackType(StackType stackType) {
        this.stackType = stackType;
    }

    public String getValidationRegex() {
        return validationRegex;
    }

    public void setValidationRegex(String validationRegex) {
        this.validationRegex = validationRegex;
    }

    public String getSubText() {
        return subText;
    }

    public void setSubText(String subText) {
        this.subText = subText;
    }

    public Boolean getFireTrackingOnce() {
        return fireTrackingOnce;
    }

    public void setFireTrackingOnce(Boolean fireTrackingOnce) {
        this.fireTrackingOnce = fireTrackingOnce;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}
