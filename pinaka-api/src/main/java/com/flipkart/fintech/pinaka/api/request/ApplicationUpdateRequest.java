package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.model.UserAccount;
import lombok.Data;
import lombok.ToString;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 22/08/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString(callSuper = true)
public class ApplicationUpdateRequest extends UserAccount {

    @JsonProperty
    private ApplicationStatus status;

    @JsonProperty("reject_reason")
    private String rejectReason;

    @JsonProperty("tracking_id")
    private String trackingId;

    @JsonProperty("pan_number")
    @ToString.Exclude
    private String panNumber;

    @JsonProperty("external_reference_id")
    private String externalReferenceId;

}
