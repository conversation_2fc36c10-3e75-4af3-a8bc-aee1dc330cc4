package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import com.flipkart.fintech.pinaka.api.model.Address;

import java.util.List;
import java.util.Map;

/**
 * Created by kunal.keshwani on 01/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddressFormField extends FormField{

    public AddressFormField() {
        super(FormFieldType.ADDRESS_SELECTION);
    }

    private String chooseAddressText;
    private Action chooseAddressAction;
    private String enterNewAddressText;
    private Action enterNewAddressAction;
    private String conjunctionText;
    private String chooseAddressTitle;
    private String chooseAddressSubTitle;
    private Address value;
    private boolean visible;
    private String textBoxLabel;

    //new fields as per new Address UI
    private String chooseAddressTextColor;
    private Integer chooseAddressTextSize;

    private String enterNewAddressTextColor;
    private Integer enterNewAddressTextSize;

    private String editAddressText;
    private Action editAddressAction;
    private String editAddressTextColor;
    private Integer editAddressTextSize;

    private Integer numberOfLines;

    private Image icon;
    private Image editIcon;
    private Image forwardIcon;
    private List<String> dependentField;

    private Map<String, String> savedAddressTracking;

    private String subtext;
    private Address homeAddresses;
    private Address workAddress;

    public String getChooseAddressText() {
        return chooseAddressText;
    }

    public void setChooseAddressText(String chooseAddressText) {
        this.chooseAddressText = chooseAddressText;
    }

    public String getEnterNewAddressText() {
        return enterNewAddressText;
    }

    public void setEnterNewAddressText(String enterNewAddressText) {
        this.enterNewAddressText = enterNewAddressText;
    }

    public Action getChooseAddressAction() {
        return chooseAddressAction;
    }

    public void setChooseAddressAction(Action chooseAddressAction) {
        this.chooseAddressAction = chooseAddressAction;
    }

    public Action getEnterNewAddressAction() {
        return enterNewAddressAction;
    }

    public void setEnterNewAddressAction(Action enterNewAddressAction) {
        this.enterNewAddressAction = enterNewAddressAction;
    }

    public String getConjunctionText() {
        return conjunctionText;
    }

    public void setConjunctionText(String conjunctionText) {
        this.conjunctionText = conjunctionText;
    }

    public Address getValue() {
        return value;
    }

    public void setValue(Address value) {
        this.value = value;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public String getChooseAddressTitle() {
        return chooseAddressTitle;
    }

    public void setChooseAddressTitle(String chooseAddressTitle) {
        this.chooseAddressTitle = chooseAddressTitle;
    }

    public String getTextBoxLabel() {
        return textBoxLabel;
    }

    public void setTextBoxLabel(String textBoxLabel) {
        this.textBoxLabel = textBoxLabel;
    }

    public String getChooseAddressSubTitle() {
        return chooseAddressSubTitle;
    }

    public void setChooseAddressSubTitle(String chooseAddressSubTitle) {
        this.chooseAddressSubTitle = chooseAddressSubTitle;
    }

    public String getChooseAddressTextColor() {
        return chooseAddressTextColor;
    }

    public Integer getChooseAddressTextSize() {
        return chooseAddressTextSize;
    }

    public String getEnterNewAddressTextColor() {
        return enterNewAddressTextColor;
    }

    public Integer getEnterNewAddressTextSize() {
        return enterNewAddressTextSize;
    }

    public String getEditAddressText() {
        return editAddressText;
    }

    public Action getEditAddressAction() {
        return editAddressAction;
    }

    public String getEditAddressTextColor() {
        return editAddressTextColor;
    }

    public Integer getEditAddressTextSize() {
        return editAddressTextSize;
    }

    public Image getIcon() {
        return icon;
    }

    public Image getEditIcon() {
        return editIcon;
    }

    public Image getForwardIcon() {
        return forwardIcon;
    }

    public void setChooseAddressTextColor(String chooseAddressTextColor) {
        this.chooseAddressTextColor = chooseAddressTextColor;
    }

    public void setChooseAddressTextSize(Integer chooseAddressTextSize) {
        this.chooseAddressTextSize = chooseAddressTextSize;
    }

    public void setEnterNewAddressTextColor(String enterNewAddressTextColor) {
        this.enterNewAddressTextColor = enterNewAddressTextColor;
    }

    public void setEnterNewAddressTextSize(Integer enterNewAddressTextSize) {
        this.enterNewAddressTextSize = enterNewAddressTextSize;
    }

    public void setEditAddressText(String editAddressText) {
        this.editAddressText = editAddressText;
    }

    public void setEditAddressAction(Action editAddressAction) {
        this.editAddressAction = editAddressAction;
    }

    public void setEditAddressTextColor(String editAddressTextColor) {
        this.editAddressTextColor = editAddressTextColor;
    }

    public void setEditAddressTextSize(Integer editAddressTextSize) {
        this.editAddressTextSize = editAddressTextSize;
    }

    public void setIcon(Image icon) {
        this.icon = icon;
    }

    public void setEditIcon(Image editIcon) {
        this.editIcon = editIcon;
    }

    public void setForwardIcon(Image forwardIcon) {
        this.forwardIcon = forwardIcon;
    }

    public Integer getNumberOfLines() {
        return numberOfLines;
    }

    public void setNumberOfLines(Integer numberOfLines) {
        this.numberOfLines = numberOfLines;
    }

    public Map<String, String> getSavedAddressTracking() {
        return savedAddressTracking;
    }

    public void setSavedAddressTracking(Map<String, String> savedAddressTracking) {
        this.savedAddressTracking = savedAddressTracking;
    }

    public List<String> getDependentField() {
        return dependentField;
    }

    public void setDependentField(List<String> dependentField) {
        this.dependentField = dependentField;
    }

    public String getSubtext() {
        return subtext;
    }

    public void setSubtext(String subtext) {
        this.subtext = subtext;
    }

    public Address getHomeAddresses() {
        return homeAddresses;
    }

    public void setHomeAddresses(Address homeAddresses) {
        this.homeAddresses = homeAddresses;
    }

    public Address getWorkAddress() {
        return workAddress;
    }

    public void setWorkAddress(Address workAddress) {
        this.workAddress = workAddress;
    }
}
