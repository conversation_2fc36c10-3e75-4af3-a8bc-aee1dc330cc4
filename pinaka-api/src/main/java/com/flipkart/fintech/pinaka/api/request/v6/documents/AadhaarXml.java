package com.flipkart.fintech.pinaka.api.request.v6.documents;

import java.util.HashMap;
import java.util.Map;
import javax.validation.constraints.NotNull;

/**
 * This is created as per KycDocumentDetails being used for BNPL
 */
public class AadhaarXml extends Document {
  private final String password;

  public AadhaarXml(@NotNull byte[] base64EncodedData, String password) {
    super(DocumentTypes.AADHAAR_XML, base64EncodedData);
    this.password = password;
  }

  @Override
  protected Map<String, String> getDocumentSpecificMetaData() {
    Map<String, String> metaData = new HashMap<>();
    metaData.put("password", password);
    return metaData;
  }
}
