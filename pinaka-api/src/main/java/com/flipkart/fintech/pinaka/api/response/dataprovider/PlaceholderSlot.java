package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.Map;

/**
 * Created by kunal.keshwani on 12/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PlaceholderSlot extends Slot{

    public PlaceholderSlot() {
        super(SlotType.PLACEHOLDER);
    }

    private Map<String, String> placeholders;
    private Image image;
    private RichText title;
    private RichText description;

    public Map<String, String> getPlaceholders() {
        return placeholders;
    }

    public void setPlaceholders(Map<String, String> placeholders) {
        this.placeholders = placeholders;
    }

    public RichText getTitle() {
        return title;
    }

    public void setTitle(RichText title) {
        this.title = title;
    }

    public RichText getDescription() {
        return description;
    }

    public void setDescription(RichText description) {
        this.description = description;
    }

    public Image getImage() {
        return image;
    }

    public void setImage(Image image) {
        this.image = image;
    }
}
