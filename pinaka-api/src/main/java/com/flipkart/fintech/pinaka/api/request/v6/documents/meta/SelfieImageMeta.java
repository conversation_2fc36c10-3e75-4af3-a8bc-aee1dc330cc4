package com.flipkart.fintech.pinaka.api.request.v6.documents.meta;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class SelfieImageMeta extends DocumentMeta {
  private String transactionId;
  private String status;
  private int livenessScore;
}