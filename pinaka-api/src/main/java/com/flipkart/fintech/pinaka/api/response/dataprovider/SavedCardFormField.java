package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

import java.util.List;

/**
 * Created by kunal.keshwani on 10/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SavedCardF<PERSON><PERSON>ield extends FormField {

    public SavedCardFormField() {
        super(FormFieldType.SAVED_CARD);
    }

    private List<CardData> cardList;
    private String preCardText;
    private String enterCardText;

    public List<CardData> getCardList() {
        return cardList;
    }

    public void setCardList(List<CardData> cardList) {
        this.cardList = cardList;
    }

    public String getPreCardText() {
        return preCardText;
    }

    public void setPreCardText(String preCardText) {
        this.preCardText = preCardText;
    }

    public String getEnterCardText() {
        return enterCardText;
    }

    public void setEnterCardText(String enterCardText) {
        this.enterCardText = enterCardText;
    }

    @Override
    public String toString() {
        return "SavedCardFormField{" +
                "cardList=" + cardList +
                ", preCardText='" + preCardText + '\'' +
                ", enterCardText='" + enterCardText + '\'' +
                '}';
    }
}
