package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.*;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.List;
import java.util.Map;

/**
 * Created by rajat.mathur on 10/12/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FormSubmitButtonSlot extends Slot {

    public FormSubmitButtonSlot() {
        super(SlotType.FORM_SUBMIT_BUTTON);
    }

    private Action action ;
    private ProductType productType;
    private ApplicationWorkflowType workflowType;
    private Lender lender;
    private List<String> formIds;
    private String context;
    private String token;
    private Action disabledAction;

    //Note : adding this field for omniture button tracking as not able to use trackable component tracking
    // field which Slot extends bcoz in case of pages having multple slots with tracking map data "rome/mapi" overwrites
    // the pageTracking map according tracking map data of last slot in that page which contributes to unexpected behaviour
    private Map<String, String> buttonClickTracking;

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public ApplicationWorkflowType getWorkflowType() {
        return workflowType;
    }

    public void setWorkflowType(ApplicationWorkflowType workflowType) {
        this.workflowType = workflowType;
    }

    public Lender getLender() {
        return lender;
    }

    public void setLender(Lender lender) {
        this.lender = lender;
    }

    public List<String> getFormIds() {
        return formIds;
    }

    public void setFormIds(List<String> formIds) {
        this.formIds = formIds;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Map<String, String> getButtonClickTracking() {
        return buttonClickTracking;
    }

    public void setButtonClickTracking(Map<String, String> buttonClickTracking) {
        this.buttonClickTracking = buttonClickTracking;
    }

    public Action getDisabledAction() {
        return disabledAction;
    }

    public void setDisabledAction(Action disabledAction) {
        this.disabledAction = disabledAction;
    }

    @Override
    public String toString() {
        return "FormSubmitButtonSlot{" +
                "action=" + action +
                ", productType=" + productType +
                ", workflowType=" + workflowType +
                ", lender=" + lender +
                ", formIds=" + formIds +
                ", context='" + context + '\'' +
                ", token='" + token + '\'' +
                ", type=" + type +
                '}';
    }
}
