package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

import java.util.List;

public class MultipleButtonSlot extends Slot {
    public MultipleButtonSlot() {
        super(SlotType.MULTIPLE_BUTTON);
    }

    @JsonProperty("buttons")
    private List<ButtonSlot> buttons;

    private String layoutType;

    public List<ButtonSlot> getButtons() {
        return buttons;
    }

    public void setButtons(List<ButtonSlot> buttons) {
        this.buttons = buttons;
    }

    public String getLayoutType() {
        return layoutType;
    }

    public void setLayoutType(String layoutType) {
        this.layoutType = layoutType;
    }
}
