package com.flipkart.fintech.pinaka.api.request.v6.documents;

import java.util.HashMap;
import java.util.Map;
import javax.validation.constraints.NotNull;

public class SelfieImage extends Document {

  public SelfieImage(@NotNull byte[] base64EncodedData) {
    super(DocumentTypes.SELFIE_IMAGE, base64EncodedData);
  }

  @Override
  public Map<String, String> getDocumentSpecificMetaData() {
    return new HashMap<>();
  }
}
