package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;

/**
 * Created by rajat.mathur on 10/12/18.
 */
public class BannerSlot extends Slot {

    public BannerSlot() {
        super(SlotType.BANNER);
    }

    private Action action;
    private Image image;
    private ProductType productType;
    private Lender lender;

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public Image getImage() {
        return image;
    }

    public void setImage(Image image) {
        this.image = image;
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public Lender getLender() {
        return lender;
    }

    public void setLender(Lender lender) {
        this.lender = lender;
    }
}
