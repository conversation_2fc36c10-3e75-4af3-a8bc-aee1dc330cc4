package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SlotPickerFormField extends FormField {

    private RichText title;
    private RichText subTitle;
    private List<SlotPickerValue> slots;
    private Map<String, Integer> containerStyle;

    public SlotPickerFormField() {
        super(FormFieldType.SLOT_PICKER);
    }

}
