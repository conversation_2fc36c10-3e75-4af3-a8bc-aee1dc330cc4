package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

/**
 * Created by kunal.keshwani on 05/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class OtpFormField extends FormField {

    public OtpFormField() {
        super(FormFieldType.OTP);
    }

    private String validationRegex;
    private String errorMessage;
    private String resendText;
    private Action resendAction;
    private String description;
    private boolean autoCaptureEnabled;
    private Integer length;

    public String getValidationRegex() {
        return validationRegex;
    }

    public void setValidationRegex(String validationRegex) {
        this.validationRegex = validationRegex;
    }

    public String getResendText() {
        return resendText;
    }

    public void setResendText(String resendText) {
        this.resendText = resendText;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isAutoCaptureEnabled() {
        return autoCaptureEnabled;
    }

    public void setAutoCaptureEnabled(boolean autoCaptureEnabled) {
        this.autoCaptureEnabled = autoCaptureEnabled;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Action getResendAction() {
        return resendAction;
    }

    public void setResendAction(Action resendAction) {
        this.resendAction = resendAction;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
