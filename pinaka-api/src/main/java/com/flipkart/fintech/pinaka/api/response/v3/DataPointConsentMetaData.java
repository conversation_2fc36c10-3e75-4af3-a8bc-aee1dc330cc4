package com.flipkart.fintech.pinaka.api.response.v3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 22/05/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        property = "consentType",
        include = JsonTypeInfo.As.EXISTING_PROPERTY
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "OTP",
        value = OtpMetaData.class
)})
public class DataPointConsentMetaData {
    private String consentType;

    public DataPointConsentMetaData(String consentType) {
        this.consentType = consentType;
    }
}
