package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.response.styles.StyledText;

public class AccountSummaryValue {
    private StyledText label;
    private StyledText value;
    private Image icon;
    private Action iconAction;
    private Image imageValueAction;

    public StyledText getLabel() {
        return label;
    }

    public void setLabel(StyledText label) {
        this.label = label;
    }

    public StyledText getValue() {
        return value;
    }

    public void setValue(StyledText value) {
        this.value = value;
    }

    public Image getIcon() {
        return icon;
    }

    public void setIcon(Image icon) {
        this.icon = icon;
    }

    public Action getIconAction() {
        return iconAction;
    }

    public void setIconAction(Action iconAction) {
        this.iconAction = iconAction;
    }

    public Image getImageValueAction() {
        return imageValueAction;
    }

    public void setImageValueAction(Image imageValueAction) {
        this.imageValueAction = imageValueAction;
    }
}
