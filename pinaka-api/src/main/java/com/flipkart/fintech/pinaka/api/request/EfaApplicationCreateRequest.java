package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.Application;
import com.flipkart.fintech.pinaka.api.model.Consent;
import com.flipkart.fintech.pinaka.api.model.EncryptionKeyData;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by su<PERSON><PERSON><PERSON>.b on 30/01/18
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString(callSuper = true)
public class EfaApplicationCreateRequest extends ApplicationCreateRequest {

    @NotNull
    @JsonProperty("form_data")
    private Application formData;

    @ToString.Exclude
    @JsonProperty("aadhaar_number")
    private String aadhaar;

    @JsonProperty("masked_aadhaar")
    private String maskedAadhaar;

    @JsonProperty("consent_details")
    private List<Consent> consentDetails;

    @ToString.Exclude
    @JsonProperty("enc_key_details")
    protected EncryptionKeyData encKeyData;

    public EfaApplicationCreateRequest() {
        super(ProductType.EFA);
    }
}
