package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.TrackableComponent;

/**
 * Created by rajat.mathur on 10/12/18.
 */

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        visible = true,
        property = "type"
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "FORM",
        value = FormSlot.class
), @JsonSubTypes.Type(
        name = "BUTTON",
        value = ButtonSlot.class
), @JsonSubTypes.Type(
        name = "FORM_SUBMIT_BUTTON",
        value = FormSubmitButtonSlot.class
), @JsonSubTypes.Type(
        name = "LOADER",
        value = LoaderSlot.class
), @JsonSubTypes.Type(
        name = "BANNER",
        value = BannerSlot.class
), @JsonSubTypes.Type(
        name = "CALLOUT",
        value = CalloutSlot.class
), @JsonSubTypes.Type(
        name = "INSTRUCTIONS",
        value = InstructionsSlot.class
), @JsonSubTypes.Type(
        name = "MESSAGING",
        value = MessagingSlot.class
), @JsonSubTypes.Type(
        name = "DISPLAY_CREDIT_CARD",
        value = DisplayCreditCardSlot.class
), @JsonSubTypes.Type(
        name = "ANNOUNCEMENT_CAROUSEL",
        value = AnnouncementCarouselSlot.class
), @JsonSubTypes.Type(
        name = "TNC",
        value = TncSlot.class
), @JsonSubTypes.Type(
        name = "PLACEHOLDER",
        value = PlaceholderSlot.class
), @JsonSubTypes.Type(
        name = "USER_AGREEMENT",
        value = UserAgreementSlot.class
),@JsonSubTypes.Type(
        name = "ACTION_CARD",
        value = ActionCardSlot.class
),@JsonSubTypes.Type(
        name = "MULTIPLE_BUTTON",
        value = MultipleButtonSlot.class
),@JsonSubTypes.Type(
        name = "INFO",
        value = InfoSlot.class
),@JsonSubTypes.Type(
        name = "CURRENT_LOCATION",
        value = CurrentLocationSlot.class
), @JsonSubTypes.Type(
        name = "BANNER_INFO",
        value = BannerInfoSlot.class
), @JsonSubTypes.Type(
        name = "APPLICATION_STATUS",
        value = ApplicationStatusSlot.class
), @JsonSubTypes.Type(
        name = "PROGRESS_BAR",
        value = ProgressBarSlot.class
), @JsonSubTypes.Type(
        name = "ACTION_CARD_LIST",
        value = ActionCardListSlot.class
), @JsonSubTypes.Type(
        name = "ACCOUNT_SUMMARY",
        value = AccountSummarySlot.class
), @JsonSubTypes.Type(
        name = "RICH_ACTIONABLE_BANNER",
        value = RichActionableBannerSlot.class
),@JsonSubTypes.Type(
        name = "MARKUP_MESSAGES",
        value = MarkupMessagesSlot.class
), @JsonSubTypes.Type(
        name = "CBC_APPLY_BANNER",
        value = ApplyBannerSlot.class
), @JsonSubTypes.Type(
        name = "CBC_ANIMATION_BANNER",
        value = AnimationBannerSlot.class
)
})
public abstract class Slot extends TrackableComponent {

    protected SlotType type;

    protected String viewType;

    public Slot(){

    }

    public Slot(SlotType type) {
        this.type = type;
    }

    public Slot(SlotType type, String viewType){
        this.type = type;
        this.viewType = viewType;
    }

    public SlotType getType() {
        return type;
    }

    public String getViewType() {
        return viewType;
    }
}
