package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class EligibleOfferRequestTest {

    @Test
    void create() {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("eligible-offer.json")) {
            ObjectMapper objectMapper = new ObjectMapper();
            EligibleOfferRequest eligibleOfferRequest = objectMapper.readValue(is, EligibleOfferRequest.class);
            assertNotNull(eligibleOfferRequest);
            assertEquals("PERSONAL", eligibleOfferRequest.getLoanPurpose().name());
            assertNotNull(eligibleOfferRequest.getConsentMetaData());
            assertNotNull(eligibleOfferRequest.getConsentMetaData().getCurrentTimeStamp());
        } catch (IOException e) {
            throw new RuntimeException("Failed to read JSON file", e);
        }
    }

    @Test
    void getMonthlyIncomeWithBonus_whenMonthlyIncomeIsNull_shouldReturnNull() {
        // Arrange
        EligibleOfferRequest request = new EligibleOfferRequest();
        request.setMonthlyIncome(null);
        request.setBonusIncome(BigDecimal.valueOf(5000));

        // Act
        BigDecimal result = request.getMonthlyIncomeWithBonus();

        // Assert
        assertNull(result, "Should return null when monthlyIncome is null");
    }

    @Test
    void getMonthlyIncomeWithBonus_whenBonusIncomeIsNull_shouldUseZero() {
        // Arrange
        EligibleOfferRequest request = new EligibleOfferRequest();
        request.setMonthlyIncome(BigDecimal.valueOf(10000));
        request.setBonusIncome(null);

        // Act
        BigDecimal result = request.getMonthlyIncomeWithBonus();

        // Assert
        assertEquals(BigDecimal.valueOf(10000), result, "Should use zero for bonusIncome when it's null");
    }

    @Test
    void getMonthlyIncomeWithBonus_whenBothValuesPresent_shouldAddCorrectly() {
        // Arrange
        EligibleOfferRequest request = new EligibleOfferRequest();
        request.setMonthlyIncome(BigDecimal.valueOf(10000));
        request.setBonusIncome(BigDecimal.valueOf(5000));

        // Act
        BigDecimal result = request.getMonthlyIncomeWithBonus();

        // Assert
        assertEquals(BigDecimal.valueOf(15000), result, "Should correctly add monthlyIncome and bonusIncome");
    }

    @Test
    void getMonthlyIncomeWithBonus_whenBonusIncomeIsZero_shouldReturnMonthlyIncome() {
        // Arrange
        EligibleOfferRequest request = new EligibleOfferRequest();
        request.setMonthlyIncome(BigDecimal.valueOf(10000));
        request.setBonusIncome(BigDecimal.ZERO);

        // Act
        BigDecimal result = request.getMonthlyIncomeWithBonus();

        // Assert
        assertEquals(BigDecimal.valueOf(10000), result, "Should return monthlyIncome when bonusIncome is zero");
    }

}