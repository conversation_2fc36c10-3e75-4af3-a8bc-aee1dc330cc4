package com.flipkart.fintech.pinaka.api.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import org.junit.jupiter.api.Test;

class LeadDetailsTest {

  @Test
  void create() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("LeadDetails.json");
    LeadDetails leadDetails = objectMapper.readValue(resourceAsStream, LeadDetails.class);
    assertNotNull(leadDetails);
    assertEquals(78, leadDetails.getUserProfile().getProfileId());
    assertEquals(560103, leadDetails.getUserProfile().getUserEnteredPincode());
  }

}