package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ConsentMetadata;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;

class IdentifyCustomerRequestTest {

    @Test
    void create() {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("identify-customer.json")) {
            ObjectMapper objectMapper = new ObjectMapper();
            IdentifyCustomerRequest identifyCustomerRequest = objectMapper.readValue(is, IdentifyCustomerRequest.class);
            assertNotNull(identifyCustomerRequest);
            assertEquals("PERSONAL", identifyCustomerRequest.getLoanPurpose().name());
            assertNotNull(identifyCustomerRequest.getConsentMetaData());
            assertNotNull(identifyCustomerRequest.getConsentMetaData().getCurrentTimeStamp());
        } catch (IOException e) {
            throw new RuntimeException("Failed to read JSON file", e);
        }
    }

}