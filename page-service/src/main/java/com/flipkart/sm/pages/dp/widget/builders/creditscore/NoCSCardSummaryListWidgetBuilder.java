package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;

public class NoCSCardSummaryListWidgetBuilder extends WidgetBuilder<CardSummaryListWidgetData> {
    private static final String WIDGET_IDENTIFIER = "NO_CS_TIPS_LIST";
    private final WidgetTemplateProvider widgetTemplateProvider;


    public NoCSCardSummaryListWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }


    @Override
    public CardSummaryListWidgetData getWidgetData() {

        return widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<CardSummaryListWidgetData>() {
        });
    }

}