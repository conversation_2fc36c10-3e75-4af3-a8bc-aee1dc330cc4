{"batches": [{"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "632d2948dd26353c7898b1df1b3200ab9398488bfd7e972596e5dafcd81758f6"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-aapi-prod-649c6f99c4-x2jg7"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java -Xms6144m -Xmx6144m -XX:+UseG1GC -verbose:gc -Xloggc:/usr/local/flipkart/advanz-api/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Dcom.sun.management.jmxremote.port=3335 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Dlog4j.configurationFile=/usr/local/flipkart/advanz-api/resources/external/log4j.xml -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -Dlog4j2.formatMsgNoLookups=true com.flipkart.poseidon.Poseidon /usr/local/flipkart/advanz-api/resources/external/bootstrap.xml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java"}}, {"key": "process.pid", "value": {"intValue": 15}}, {"key": "process.runtime.description", "value": {"stringValue": "Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 25.202-b08"}}, {"key": "process.runtime.name", "value": {"stringValue": "Java(TM) SE Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_202-b08"}}, {"key": "service.instance.id", "value": {"stringValue": "c66758d9-6305-4a1b-aee3-6670d6448c9a"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pl-aapi"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "98ae45a7e352b505", "parentSpanId": "0000000000000000", "traceState": "", "name": "POST /*", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046484000000, "endTimeUnixNano": 1748774047860557000, "attributes": [{"key": "user_agent.original", "value": {"stringValue": "Mozilla/5.0 (Linux; Android 15; V2432 Build/AP3A.240905.015.A2_V000L1) FKUA/Retail/2230200/Android/Mobile (vivo/V2432/9c1f98030709cb240765c82385ad8cdc)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "network.peer.port", "value": {"intValue": 58116}}, {"key": "url.scheme", "value": {"stringValue": "https"}}, {"key": "thread.name", "value": {"stringValue": "qtp1085543867-242"}}, {"key": "url.path", "value": {"stringValue": "/api/fpg/1/action/view"}}, {"key": "server.address", "value": {"stringValue": "127.0.0.1"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "network.peer.address", "value": {"stringValue": "127.0.0.1"}}, {"key": "http.route", "value": {"stringValue": "/*"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 242}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "632d2948dd26353c7898b1df1b3200ab9398488bfd7e972596e5dafcd81758f6"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-aapi-prod-649c6f99c4-x2jg7"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java -Xms6144m -Xmx6144m -XX:+UseG1GC -verbose:gc -Xloggc:/usr/local/flipkart/advanz-api/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Dcom.sun.management.jmxremote.port=3335 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Dlog4j.configurationFile=/usr/local/flipkart/advanz-api/resources/external/log4j.xml -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -Dlog4j2.formatMsgNoLookups=true com.flipkart.poseidon.Poseidon /usr/local/flipkart/advanz-api/resources/external/bootstrap.xml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jre1.8.0_202/bin/java"}}, {"key": "process.pid", "value": {"intValue": 15}}, {"key": "process.runtime.description", "value": {"stringValue": "Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 25.202-b08"}}, {"key": "process.runtime.name", "value": {"stringValue": "Java(TM) SE Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_202-b08"}}, {"key": "service.instance.id", "value": {"stringValue": "c66758d9-6305-4a1b-aee3-6670d6448c9a"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pl-aapi"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d1cd7b2be1af4fd2", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "romeServiceHttp.romeServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046484708400, "endTimeUnixNano": 1748774046517911000, "attributes": [{"key": "thread.id", "value": {"intValue": 998}}, {"key": "thread.name", "value": {"stringValue": "hystrix-romeServiceHttpRequest-190"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d2eced20edc23a94", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046518264800, "endTimeUnixNano": 1748774046527446300, "attributes": [{"key": "thread.id", "value": {"intValue": 769}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-98"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 2, "message": ""}, "events": [{"timeUnixNano": 1748774046527445000, "attributes": [{"key": "exception.type", "value": {"stringValue": "java.lang.RuntimeException"}}, {"key": "exception.message", "value": {"stringValue": "500 {\"error_code\":500,\"error_message\":\"No Merchant Account Mapping Exist\"}\n(URI = /loginservice/user/v1/merchant/ACC0CCB75EDD16B45A08223932752402416D?merchant=FLIPKART&phoneNumberFormat=E164, METHOD = GET)"}}, {"key": "exception.stacktrace", "value": {"stringValue": "java.lang.RuntimeException: 500 {\"error_code\":500,\"error_message\":\"No Merchant Account Mapping Exist\"}\n(URI = /loginservice/user/v1/merchant/ACC0CCB75EDD16B45A08223932752402416D?merchant=FLIPKART&phoneNumberFormat=E164, METHOD = GET)\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.handleException(SinglePoolHttpTaskHandler.java:331)\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.execute(SinglePoolHttpTaskHandler.java:157)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.run(TaskHandlerExecutor.java:250)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.run(TaskHandlerExecutor.java:48)\n\tat com.netflix.hystrix.HystrixCommand$2.call(HystrixCommand.java:301)\n\tat com.netflix.hystrix.HystrixCommand$2.call(HystrixCommand.java:297)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:46)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:45)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:15)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:51)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OperatorSubscribeOn$1.call(OperatorSubscribeOn.java:94)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:56)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:47)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction.call(HystrixContexSchedulerAction.java:69)\n\tat rx.internal.schedulers.ScheduledAction.run(ScheduledAction.java:55)\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\n\tat java.util.concurrent.FutureTask.run(FutureTask.java:266)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\nCaused by: com.flipkart.aapi.serviceclients.login.v5.LoginServiceFailureException: 500 {\"error_code\":500,\"error_message\":\"No Merchant Account Mapping Exist\"}\n\n\tat sun.reflect.GeneratedConstructorAccessor269.newInstance(Unknown Source)\n\tat sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.lang.reflect.Constructor.newInstance(Constructor.java:423)\n\tat com.flipkart.poseidon.serviceclients.ServiceResponseDecoder.decode(ServiceResponseDecoder.java:145)\n\tat com.flipkart.poseidon.serviceclients.ServiceResponseDecoder.decode(ServiceResponseDecoder.java:38)\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.execute(SinglePoolHttpTaskHandler.java:154)\n\t... 33 more\n"}}], "droppedAttributesCount": 0, "name": "exception"}]}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "0f57cd3277635a58", "parentSpanId": "d2eced20edc23a94", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.fallback", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046527470300, "endTimeUnixNano": 1748774046527585000, "attributes": [{"key": "thread.id", "value": {"intValue": 769}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-98"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 2, "message": ""}, "events": [{"timeUnixNano": 1748774046527585000, "attributes": [{"key": "exception.type", "value": {"stringValue": "java.lang.UnsupportedOperationException"}}, {"key": "exception.message", "value": {"stringValue": "No fallback available."}}, {"key": "exception.stacktrace", "value": {"stringValue": "java.lang.UnsupportedOperationException: No fallback available.\n\tat com.flipkart.poseidon.handlers.http.impl.SinglePoolHttpTaskHandler.getFallBack(SinglePoolHttpTaskHandler.java:231)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.getFallback(TaskHandlerExecutor.java:293)\n\tat com.flipkart.phantom.task.impl.TaskHandlerExecutor.getFallback(TaskHandlerExecutor.java:48)\n\tat com.netflix.hystrix.HystrixCommand$3.call(HystrixCommand.java:321)\n\tat com.netflix.hystrix.HystrixCommand$3.call(HystrixCommand.java:317)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:46)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:45)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:15)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OperatorOnErrorResumeNextViaFunction$4.onError(OperatorOnErrorResumeNextViaFunction.java:142)\n\tat rx.internal.operators.OnSubscribeDoOnEach$DoOnEachSubscriber.onError(OnSubscribeDoOnEach.java:87)\n\tat rx.internal.operators.OnSubscribeDoOnEach$DoOnEachSubscriber.onError(OnSubscribeDoOnEach.java:87)\n\tat com.netflix.hystrix.AbstractCommand$HystrixObservableTimeoutOperator$3.onError(AbstractCommand.java:1176)\n\tat rx.internal.operators.OperatorSubscribeOn$1$1.onError(OperatorSubscribeOn.java:59)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat rx.internal.operators.OnSubscribeDoOnEach$DoOnEachSubscriber.onError(OnSubscribeDoOnEach.java:87)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat com.netflix.hystrix.AbstractCommand$DeprecatedOnRunHookApplication$1.onError(AbstractCommand.java:1413)\n\tat com.netflix.hystrix.AbstractCommand$ExecutionHookApplication$1.onError(AbstractCommand.java:1344)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedSubscriber.onError(TracedSubscriber.java:81)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat rx.observers.Subscribers$5.onError(Subscribers.java:230)\n\tat rx.internal.operators.OnSubscribeThrow.call(OnSubscribeThrow.java:44)\n\tat rx.internal.operators.OnSubscribeThrow.call(OnSubscribeThrow.java:28)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:51)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:45)\n\tat io.opentelemetry.javaagent.shaded.instrumentation.rxjava.v1_0.TracedOnSubscribe.call(TracedOnSubscribe.java:15)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:51)\n\tat rx.internal.operators.OnSubscribeDefer.call(OnSubscribeDefer.java:35)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:41)\n\tat rx.internal.operators.OnSubscribeDoOnEach.call(OnSubscribeDoOnEach.java:30)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)\n\tat rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)\n\tat rx.Observable.unsafeSubscribe(Observable.java:10151)\n\tat rx.internal.operators.OperatorSubscribeOn$1.call(OperatorSubscribeOn.java:94)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:56)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction$1.call(HystrixContexSchedulerAction.java:47)\n\tat com.netflix.hystrix.strategy.concurrency.HystrixContexSchedulerAction.call(HystrixContexSchedulerAction.java:69)\n\tat rx.internal.schedulers.ScheduledAction.run(ScheduledAction.java:55)\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\n\tat java.util.concurrent.FutureTask.run(FutureTask.java:266)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:748)\n"}}], "droppedAttributesCount": 0, "name": "exception"}]}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "7d0804dafc90e8b5", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "OAuthPoolHttpTaskHandler.userServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046527896600, "endTimeUnixNano": 1748774046557334800, "attributes": [{"key": "thread.id", "value": {"intValue": 1385}}, {"key": "thread.name", "value": {"stringValue": "hystrix-userServiceHttpRequest-97"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "55722831fee96130", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046557535500, "endTimeUnixNano": 1748774046627794700, "attributes": [{"key": "thread.id", "value": {"intValue": 773}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-99"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "69aeda85c3ca93d5", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "pinakaServiceHttp.pinakaServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046628075500, "endTimeUnixNano": 1748774046725096400, "attributes": [{"key": "thread.id", "value": {"intValue": 1028}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaServiceHttpRequest-10"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d73d1b3b92a2f060", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "loginServiceHttp.loginServiceHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046725320700, "endTimeUnixNano": 1748774046773427000, "attributes": [{"key": "thread.id", "value": {"intValue": 773}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-99"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "1e934225b658800e", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "PINAKA_API_POOL.STATUS.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046775427300, "endTimeUnixNano": 1748774047317726000, "attributes": [{"key": "thread.id", "value": {"intValue": 411}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-5"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "4e6008c4579fa563", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "pageServiceClientViewHttp.pageServiceClientViewHttpRequest.execute", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047319089700, "endTimeUnixNano": 1748774047353057500, "attributes": [{"key": "thread.id", "value": {"intValue": 1039}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pageServiceClientViewHttpRequest-91"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "993cf60704ce1717", "parentSpanId": "d1cd7b2be1af4fd2", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046484728800, "endTimeUnixNano": 1748774046517780500, "attributes": [{"key": "server.address", "value": {"stringValue": "************"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 998}}, {"key": "thread.name", "value": {"stringValue": "hystrix-romeServiceHttpRequest-190"}}, {"key": "url.full", "value": {"stringValue": "http://************/2/session/validate"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d9cb8685ba2070e7", "parentSpanId": "d2eced20edc23a94", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046518286600, "endTimeUnixNano": 1748774046527160300, "attributes": [{"key": "error.type", "value": {"stringValue": "500"}}, {"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 769}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-98"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v1/merchant/ACC0CCB75EDD16B45A08223932752402416D?merchant=FLIPKART&phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 500}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 2, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "121d1c26f0fd29ec", "parentSpanId": "7d0804dafc90e8b5", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046527918800, "endTimeUnixNano": 1748774046557245000, "attributes": [{"key": "server.address", "value": {"stringValue": "***********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 1385}}, {"key": "thread.name", "value": {"stringValue": "hystrix-userServiceHttpRequest-97"}}, {"key": "url.full", "value": {"stringValue": "http://***********/userservice/v0.1/customer/ACC0CCB75EDD16B45A08223932752402416D"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "65ba535426ff7c92", "parentSpanId": "55722831fee96130", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046557553700, "endTimeUnixNano": 1748774046627724800, "attributes": [{"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 773}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-99"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v2?phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "7761937f5df3ccb9", "parentSpanId": "69aeda85c3ca93d5", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046628102100, "endTimeUnixNano": 1748774046724959200, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 1028}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaServiceHttpRequest-10"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/migrate"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f3d91005cd6c4891", "parentSpanId": "d73d1b3b92a2f060", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046725347000, "endTimeUnixNano": 1748774046773341400, "attributes": [{"key": "server.address", "value": {"stringValue": "kavach-service-prod.kavach-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 773}}, {"key": "thread.name", "value": {"stringValue": "hystrix-loginServiceHttpRequest-99"}}, {"key": "url.full", "value": {"stringValue": "http://kavach-service-prod.kavach-prod.fkcloud.in/loginservice/user/v2?phoneNumberFormat=E164"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d36127e4d74ddac2", "parentSpanId": "4e6008c4579fa563", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047319110100, "endTimeUnixNano": 1748774047352919300, "attributes": [{"key": "server.address", "value": {"stringValue": "***********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "thread.id", "value": {"intValue": 1039}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pageServiceClientViewHttpRequest-91"}}, {"key": "url.full", "value": {"stringValue": "http://***********/pages/v1/page/perso-dynam-311d7/view/client/mobile_api"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "60a32510be3bdaa6", "parentSpanId": "1e934225b658800e", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046775713800, "endTimeUnixNano": 1748774046800312300, "attributes": [{"key": "server.address", "value": {"stringValue": "**********"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "thread.id", "value": {"intValue": 411}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-5"}}, {"key": "url.full", "value": {"stringValue": "http://**********:80/v1/user-profile/ACCOUNTID/ACC0CCB75EDD16B45A08223932752402416D/segmentsWithSegmentGroups?channel=NEO&sc=FK"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "4f51b7f11fc6d917", "parentSpanId": "1e934225b658800e", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046823737600, "endTimeUnixNano": 1748774047317492700, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 411}}, {"key": "thread.name", "value": {"stringValue": "hystrix-pinakaStatusActionPool-5"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "8f53ebc4b7206273", "parentSpanId": "98ae45a7e352b505", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047376999200, "endTimeUnixNano": 1748774047856544300, "attributes": [{"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "thread.id", "value": {"intValue": 583}}, {"key": "thread.name", "value": {"stringValue": "data-aggregator-thread-22"}}, {"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.hystrix-1.4", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "e8e46f02abd67f82d85742d2bb1c878cb7e195078cff34e15bca0e9721655ef8"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-pinaka-prod-6c976d8d5-zvxlk"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3584m -Xmx5734m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-pinaka-preprod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G com.flipkart.fintech.pinaka.service.application.PinakaApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "df20ed2b-6d5b-4c7c-be7f-c7b726eeee95"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-pinaka"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "61245b6584e59976", "parentSpanId": "7761937f5df3ccb9", "traceState": "", "name": "POST /pinaka/6/pl/migrate", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046630000000, "endTimeUnixNano": 1748774046724775000, "attributes": [{"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 20030}}, {"key": "thread.name", "value": {"stringValue": "dw-20030"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 53842}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/migrate"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/migrate"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "bc01e61ceb95a1ef", "parentSpanId": "61245b6584e59976", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046661976000, "endTimeUnixNano": 1748774046724612900, "attributes": [{"key": "thread.id", "value": {"intValue": 20030}}, {"key": "thread.name", "value": {"stringValue": "dw-20030 - POST /pinaka/6/pl/migrate"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "5c77534f02caf0ef", "parentSpanId": "4f51b7f11fc6d917", "traceState": "", "name": "POST /pinaka/6/pl/apply-now", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046824000000, "endTimeUnixNano": 1748774047316530400, "attributes": [{"key": "network.peer.port", "value": {"intValue": 50818}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/apply-now"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/apply-now"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_202)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "c02cfa67fcdf1b48", "parentSpanId": "f6b2048fe9701c17", "traceState": "", "name": "GET /pinaka/2/profile/basic-profile", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046976000000, "endTimeUnixNano": 1748774047084128000, "attributes": [{"key": "network.peer.port", "value": {"intValue": 41450}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/2/profile/basic-profile"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/2/profile/basic-profile"}}, {"key": "url.query", "value": {"stringValue": "smUserId=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 20100}}, {"key": "thread.name", "value": {"stringValue": "dw-20100"}}, {"key": "http.response.status_code", "value": {"intValue": 204}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_322)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "119a1a93317ba642", "parentSpanId": "8f53ebc4b7206273", "traceState": "", "name": "POST /pinaka/6/pl/fetch-bulk-data-v2", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047378000000, "endTimeUnixNano": 1748774047856349000, "attributes": [{"key": "network.peer.port", "value": {"intValue": 56026}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.route", "value": {"stringValue": "/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "url.path", "value": {"stringValue": "/pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (HttpUrlConnection 1.8.0_202)"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "6121e01daaa9bb94", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046886145800, "endTimeUnixNano": 1748774046895926300, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "6659391457d5a32f", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046896518100, "endTimeUnixNano": 1748774046904513500, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "47bf220fb86c1b3f", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047084696600, "endTimeUnixNano": 1748774047184809500, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/create"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "79eee0a2443db130", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047185059600, "endTimeUnixNano": 1748774047193321500, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f64e14a51023b204", "parentSpanId": "119a1a93317ba642", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047397881600, "endTimeUnixNano": 1748774047405035300, "attributes": [{"key": "url.full", "value": {"stringValue": "http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f4206d72ac72c9b0", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046904796700, "endTimeUnixNano": 1748774046939741200, "attributes": [{"key": "url.full", "value": {"stringValue": "http://************:80/dexter/1/user-profile/fetch"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "9c2d5987cc89328b", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046940208600, "endTimeUnixNano": 1748774046974355500, "attributes": [{"key": "url.full", "value": {"stringValue": "http://************:80/dexter/1/user-profile/fetch"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "server.port", "value": {"intValue": 80}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f6b2048fe9701c17", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046974619400, "endTimeUnixNano": 1748774047084505600, "attributes": [{"key": "url.full", "value": {"stringValue": "http://pinaka-service.sm-pinaka-prod.fkcloud.in/pinaka/2/profile/basic-profile?smUserId=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}, {"key": "http.response.status_code", "value": {"intValue": 204}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "pinaka-service.sm-pinaka-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "2ca97405a1c74175", "parentSpanId": "119a1a93317ba642", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047444199200, "endTimeUnixNano": 1748774047449594000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://usersvc-kaas-v1.sm-user-service-prod.fkcloud.in/userservice/v2/customer/SMA98A5554C8D1A456C905B6222875AE5EF?piiTextType=PLAINTEXT"}}, {"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "usersvc-kaas-v1.sm-user-service-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "1a0954f726ed9c01", "parentSpanId": "119a1a93317ba642", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047449853700, "endTimeUnixNano": 1748774047817071000, "attributes": [{"key": "url.full", "value": {"stringValue": "http://sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in/sm/upi/user-svc/v1/upi-user/search"}}, {"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "server.address", "value": {"stringValue": "sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "1dcd4af40483ed8a", "parentSpanId": "c02cfa67fcdf1b48", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047003415000, "endTimeUnixNano": 1748774047030469600, "attributes": [{"key": "thread.id", "value": {"intValue": 20100}}, {"key": "thread.name", "value": {"stringValue": "dw-20100 - GET /pinaka/2/profile/basic-profile?smUserId=SMA98A5554C8D1A456C905B6222875AE5EF"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "c0f331beb6b74118", "parentSpanId": "c02cfa67fcdf1b48", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047030559200, "endTimeUnixNano": 1748774047083975000, "attributes": [{"key": "thread.id", "value": {"intValue": 20100}}, {"key": "thread.name", "value": {"stringValue": "dw-20100 - GET /pinaka/2/profile/basic-profile?smUserId=SMA98A5554C8D1A456C905B6222875AE5EF"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "52ec934911eb8609", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047193920500, "endTimeUnixNano": 1748774047255784000, "attributes": [{"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "37cefd6320cc9928", "parentSpanId": "5c77534f02caf0ef", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047255787500, "endTimeUnixNano": 1748774047316311300, "attributes": [{"key": "thread.id", "value": {"intValue": 20013}}, {"key": "thread.name", "value": {"stringValue": "dw-20013 - POST /pinaka/6/pl/apply-now"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "451b5313ab457d1b", "parentSpanId": "119a1a93317ba642", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047405527600, "endTimeUnixNano": 1748774047424806700, "attributes": [{"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "750729134f1a76e7", "parentSpanId": "119a1a93317ba642", "traceState": "", "name": "SELECT Profile", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047424836400, "endTimeUnixNano": 1748774047444049700, "attributes": [{"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "1ca92f449ca838a3", "parentSpanId": "119a1a93317ba642", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047817605400, "endTimeUnixNano": 1748774047855876600, "attributes": [{"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "40260c788e048590", "parentSpanId": "1dcd4af40483ed8a", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047003479000, "endTimeUnixNano": 1748774047030440700, "attributes": [{"key": "thread.id", "value": {"intValue": 20100}}, {"key": "thread.name", "value": {"stringValue": "dw-20100 - GET /pinaka/2/profile/basic-profile?smUserId=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.smUserId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.sm_user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d475629ffc13ec47", "parentSpanId": "451b5313ab457d1b", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047405587200, "endTimeUnixNano": 1748774047424778800, "attributes": [{"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.smUserId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.sm_user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "4be877db0a60e32a", "parentSpanId": "750729134f1a76e7", "traceState": "", "name": "SELECT profile.profile", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047424888800, "endTimeUnixNano": 1748774047444035300, "attributes": [{"key": "thread.id", "value": {"intValue": 20049}}, {"key": "thread.name", "value": {"stringValue": "dw-20049 - POST /pinaka/6/pl/fetch-bulk-data-v2"}}, {"key": "db.name", "value": {"stringValue": "profile"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "profile"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Profile as generatedAlias0 where generatedAlias0.userId=:param0 */ select profile0_.profile_id as profile_1_4_, profile0_.pan as pan2_4_, profile0_.sm_user_id as sm_user_3_4_, profile0_.user_id as user_id4_4_ from profile profile0_ where profile0_.user_id=?"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in:3306"}}, {"key": "server.address", "value": {"stringValue": "master.supermoney-dev-sm-mysql-prod.prod.altair.fkcloud.in"}}, {"key": "db.user", "value": {"stringValue": "v-sm-m-QmBS5epr8"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "b27f34f27fe2025b8baf7725229a9d7081def272f721b6569ffbf12ccd93e8e7"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-winterfell-87758d947-sjmgm"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3072m -Xmx4915m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-winterfell-prod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties com.flipkart.fintech.winterfell.application.WinterFellApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "96f0ee43-9744-46e0-a4c5-3bd5327f2d59"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-winterfell"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "3a68e5f20bfe7caf", "parentSpanId": "6121e01daaa9bb94", "traceState": "", "name": "GET /fintech-winterfell/1/application/activeApplicationsResponse", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046887000000, "endTimeUnixNano": 1748774046895662800, "attributes": [{"key": "thread.id", "value": {"intValue": 80281}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-80281"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 53408}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "b2a2232b2df88c57", "parentSpanId": "3a68e5f20bfe7caf", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046887683600, "endTimeUnixNano": 1748774046895187200, "attributes": [{"key": "thread.id", "value": {"intValue": 80281}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "thread.name", "value": {"stringValue": "dw-80281 - GET /fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "9855226789545e92", "parentSpanId": "6659391457d5a32f", "traceState": "", "name": "GET /fintech-winterfell/1/application/activeApplicationsResponse", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046897000000, "endTimeUnixNano": 1748774046904495900, "attributes": [{"key": "thread.id", "value": {"intValue": 88023}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-88023"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 36300}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/activeApplicationsResponse"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "77268ae11b7426c4", "parentSpanId": "47bf220fb86c1b3f", "traceState": "", "name": "POST /fintech-winterfell/1/application/create", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047086000000, "endTimeUnixNano": 1748774047185388500, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 36300}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/create"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/create"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "588c6fce02f8cf26", "parentSpanId": "79eee0a2443db130", "traceState": "", "name": "GET /fintech-winterfell/1/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047186000000, "endTimeUnixNano": 1748774047193779500, "attributes": [{"key": "thread.id", "value": {"intValue": 87922}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-87922"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 36300}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "58eb892bc7147470", "parentSpanId": "9855226789545e92", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046897262300, "endTimeUnixNano": 1748774046904158000, "attributes": [{"key": "thread.id", "value": {"intValue": 88023}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}, {"key": "thread.name", "value": {"stringValue": "dw-88023 - GET /fintech-winterfell/1/application/activeApplicationsResponse?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "240f32b4c8178510", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "POST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047086433800, "endTimeUnixNano": 1748774047100784600, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/create"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "09f5f72e13d0c4be", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "PUT", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047104024800, "endTimeUnixNano": 1748774047116284200, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f9109952c8a27a3f", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "PUT", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047116596200, "endTimeUnixNano": 1748774047129027800, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "5d3a9ceab01aaa8a", "parentSpanId": "588c6fce02f8cf26", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047186235000, "endTimeUnixNano": 1748774047191434000, "attributes": [{"key": "thread.id", "value": {"intValue": 87922}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}, {"key": "thread.name", "value": {"stringValue": "dw-87922 - GET /fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "9d3e821b1b35fb50", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047101528300, "endTimeUnixNano": 1748774047102418700, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select distinct RES.* , P.KEY_ as ProcessDefini<PERSON><PERSON><PERSON>, P.ID_ as ProcessDefinitionId, P.NAME_ as ProcessDefinitionName, P.VERSION_ as ProcessDefinitionVersion, P.DEPLOYMENT_ID_ as DeploymentId from ACT_RU_EXECUTION RES inner join ACT_RE_PROCDEF P on RES.PROC_DEF_ID_ = P.ID_ WHERE RES.PARENT_ID_ is null and RES.BUSINESS_KEY_ = ? order by RES.ID_ asc"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "a955733025cc35e9", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "SELECT sm_winterfell_prod.ACT_RE_PROCDEF", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047103238700, "endTimeUnixNano": 1748774047103725800, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RE_PROCDEF"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from ACT_RE_PROCDEF where ID_ = ?"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "c272c9813650574b", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "SELECT sm_winterfell_prod.ACT_RU_ENTITYLINK", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047129334000, "endTimeUnixNano": 1748774047130141400, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_ENTITYLINK"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from ACT_RU_ENTITYLINK where REF_SCOPE_ID_ = ? and REF_SCOPE_TYPE_ = ? and LINK_TYPE_ = ?"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "a9e830dfda26f46a", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_EXECUTION", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047130634000, "endTimeUnixNano": 1748774047136921300, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_EXECUTION"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_EXECUTION (ID_, REV_, PROC_INST_ID_, BUSINESS_KEY_, PROC_DEF_ID_, ACT_ID_, IS_ACTIVE_, IS_CONCURRENT_, IS_SCOPE_,IS_EVENT_SCOPE_, IS_MI_ROOT_, PARENT_ID_, SUPER_EXEC_, ROOT_PROC_INST_ID_, SUSPENSION_STATE_, TENANT_ID_, NAME_, START_ACT_ID_, START_TIME_, START_USER_ID_, IS_COUNT_ENABLED_, EVT_SUBSCR_COUNT_, TASK_COUNT_, JOB_COUNT_, TIMER_JOB_COUNT_, SUSP_JOB_COUNT_, DEADLETTER_JOB_COUNT_, VAR_COUNT_, ID_LINK_COUNT_, CALLBACK_ID_, CALLBACK_TYPE_, REFERENCE_ID_, REFERENCE_TYPE_, PROPAGATED_STAGE_INST_ID_) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "8f3e966af6dcfe8b", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_ACTINST", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047137205200, "endTimeUnixNano": 1748774047142927600, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_ACTINST"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_ACTINST ( ID_, REV_, PROC_DEF_ID_, PROC_INST_ID_, EXECUTION_ID_, ACT_ID_, TASK_ID_, CALL_PROC_INST_ID_, ACT_NAME_, ACT_TYPE_, ASSIGNEE_, START_TIME_, END_TIME_, DURATION_, DELETE_REASON_, TENANT_ID_ ) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) , (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) , (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "1b15d304cea330a3", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_TASK", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047143049700, "endTimeUnixNano": 1748774047144675800, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_TASK"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_TASK (ID_, REV_, NAME_, PARENT_TASK_ID_, DESCRIPTION_, PRIORITY_, CREATE_TIME_, OWNER_, ASSIGNEE_, DELEGATION_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, TASK_DEF_ID_, SCOPE_ID_, SUB_SCOPE_ID_, SCOPE_TYPE_, SCOPE_DEFINITION_ID_, PROPAGATED_STAGE_INST_ID_, TASK_DEF_KEY_, DUE_DATE_, CATEGORY_, SUSPENSION_STATE_, TENANT_ID_, FORM_KEY_, CLAIM_TIME_, IS_COUNT_ENABLED_, VAR_COUNT_, ID_LINK_COUNT_, SUB_TASK_COUNT_) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "2c83cb3d1cdce1cf", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_ENTITYLINK", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047144743000, "endTimeUnixNano": 1748774047145430300, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_ENTITYLINK"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "insert into ACT_RU_ENTITYLINK (ID_, REV_, CREATE_TIME_, LINK_TYPE_, SCOPE_ID_, SCOPE_TYPE_, SCOPE_DEFINITION_ID_, REF_SCOPE_ID_, REF_SCOPE_TYPE_, REF_SCOPE_DEFINITION_ID_, HIERARCHY_TYPE_) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "8c70fb3a622c25fe", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "INSERT sm_winterfell_prod.ACT_RU_VARIABLE", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047146434800, "endTimeUnixNano": 1748774047171120000, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.sql.table", "value": {"stringValue": "ACT_RU_VARIABLE"}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "INSERT INTO ACT_RU_VARIABLE (ID_, REV_, TYPE_, NAME_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, SCOPE_ID_, SUB_SCOPE_ID_, SCOPE_TYPE_, BYTEARRAY_ID_, DOUBLE_, LONG_ , TEXT_, TEXT2_) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) , ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "de0e8fe105e112e9", "parentSpanId": "77268ae11b7426c4", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047181980200, "endTimeUnixNano": 1748774047184583700, "attributes": [{"key": "thread.id", "value": {"intValue": 87759}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87759 - POST /fintech-winterfell/1/application/create"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "eb68b0604564139d", "parentSpanId": "588c6fce02f8cf26", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047192319700, "endTimeUnixNano": 1748774047193087700, "attributes": [{"key": "thread.id", "value": {"intValue": 87922}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87922 - GET /fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "929c045fa41de2c5", "parentSpanId": "f64e14a51023b204", "traceState": "", "name": "GET /fintech-winterfell/1/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047398000000, "endTimeUnixNano": 1748774047404651800, "attributes": [{"key": "thread.id", "value": {"intValue": 87862}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-87862"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.30.1 (Apache HttpClient 4.5.8)"}}, {"key": "network.peer.port", "value": {"intValue": 36300}}, {"key": "http.route", "value": {"stringValue": "/fintech-winterfell/1/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "url.path", "value": {"stringValue": "/fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "winterfell-service.sm-winterfell-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "4eed09c4395307c4", "parentSpanId": "929c045fa41de2c5", "traceState": "", "name": "GET", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047398268400, "endTimeUnixNano": 1748774047402287400, "attributes": [{"key": "thread.id", "value": {"intValue": 87862}}, {"key": "url.full", "value": {"stringValue": "http://citadel-service.sm-citadel-prod.fkcloud.in/fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}, {"key": "thread.name", "value": {"stringValue": "dw-87862 - GET /fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "82fa1631ccd04017", "parentSpanId": "929c045fa41de2c5", "traceState": "", "name": "SELECT sm_winterfell_prod", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047403036200, "endTimeUnixNano": 1748774047403937000, "attributes": [{"key": "thread.id", "value": {"intValue": 87862}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-87862 - GET /fintech-winterfell/1/application/APP2506011604072724117958958792624919457"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select * from `ACT_RU_TASK` where ID_ in (select REF_SCOPE_ID_ from ACT_RU_ENTITYLINK where LINK_TYPE_ = ? and SCOPE_ID_ = ? AND SCOPE_TYPE_ = ? and REF_SCOPE_TYPE_ = ?)"}}, {"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.address", "value": {"stringValue": "************"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}, {"resource": {"attributes": [{"key": "container.id", "value": {"stringValue": "aaa696e8185c5cb37d40dd8f50f479e31b3b6e506f524c0df3d9ffd57c0d0d74"}}, {"key": "env", "value": {"stringValue": "prod"}}, {"key": "host.arch", "value": {"stringValue": "amd64"}}, {"key": "host.name", "value": {"stringValue": "sm-citadel-prod-659cc7b68c-tvpwh"}}, {"key": "os.description", "value": {"stringValue": "Linux 6.6.72+"}}, {"key": "os.type", "value": {"stringValue": "linux"}}, {"key": "process.command_line", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java -Xms3338m -Xmx5341m -XX:+UseG1GC -verbose:gc -Xloggc:/var/log/sumo/sm-citadel-prod/gc.log -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE -Duser.timezone=Asia/Kolkata -Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/flipkart/java-heapdump.hprof -javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml -javaagent:/var/lib/opentelemetry/opentelemetry-javaagent.jar -Dotel.javaagent.configuration-file=/etc/sumo-observability/otel-agent.properties -Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G com.flipkart.fintech.citadel.service.application.CitadelApplication server /etc/config/config.yml"}}, {"key": "process.executable.path", "value": {"stringValue": "/opt/java/jdk8u322-b06/jre/bin/java"}}, {"key": "process.pid", "value": {"intValue": 14}}, {"key": "process.runtime.description", "value": {"stringValue": "Temurin OpenJDK 64-Bit Server VM 25.322-b06"}}, {"key": "process.runtime.name", "value": {"stringValue": "OpenJDK Runtime Environment"}}, {"key": "process.runtime.version", "value": {"stringValue": "1.8.0_322-b06"}}, {"key": "service.instance.id", "value": {"stringValue": "9ad1531c-3a3d-4628-98a7-93f1120c91d1"}}, {"key": "telemetry.distro.name", "value": {"stringValue": "opentelemetry-java-instrumentation"}}, {"key": "telemetry.distro.version", "value": {"stringValue": "2.13.3"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "java"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.47.0"}}, {"key": "service.name", "value": {"stringValue": "sm-citadel"}}], "droppedAttributesCount": 0}, "instrumentationLibrarySpans": [{"spans": [{"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "378997048427a3a4", "parentSpanId": "b2a2232b2df88c57", "traceState": "", "name": "GET /fintech-citadel/2/application/getActiveApplications", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046890000000, "endTimeUnixNano": 1748774046895025400, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}, {"key": "network.peer.address", "value": {"stringValue": "************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 54995}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 43254}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-54995"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f53a744481d19fdc", "parentSpanId": "5d3a9ceab01aaa8a", "traceState": "", "name": "GET /fintech-citadel/2/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047187000000, "endTimeUnixNano": 1748774047190806300, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 54995}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 53194}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-54995"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "3a23c31c3f1009c2", "parentSpanId": "378997048427a3a4", "traceState": "", "name": "SELECT Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046891163600, "endTimeUnixNano": 1748774046893419300, "attributes": [{"key": "thread.id", "value": {"intValue": 54995}}, {"key": "thread.name", "value": {"stringValue": "dw-54995 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "fd7813ff28b67344", "parentSpanId": "378997048427a3a4", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046893481500, "endTimeUnixNano": 1748774046894780000, "attributes": [{"key": "thread.id", "value": {"intValue": 54995}}, {"key": "thread.name", "value": {"stringValue": "dw-54995 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "426e9dcdccf50e98", "parentSpanId": "f53a744481d19fdc", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047188105200, "endTimeUnixNano": 1748774047189044200, "attributes": [{"key": "thread.id", "value": {"intValue": 54995}}, {"key": "thread.name", "value": {"stringValue": "dw-54995 - GET /fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "ad1a51fad4ab129f", "parentSpanId": "f53a744481d19fdc", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047189205000, "endTimeUnixNano": 1748774047190484500, "attributes": [{"key": "thread.id", "value": {"intValue": 54995}}, {"key": "thread.name", "value": {"stringValue": "dw-54995 - GET /fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "0c58baf55a651fe0", "parentSpanId": "3a23c31c3f1009c2", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046891199000, "endTimeUnixNano": 1748774046893399000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 54995}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Application as generatedAlias0 where generatedAlias0.userId=:param0 */ select applicatio0_.id as id1_0_, applicatio0_.created_at as created_2_0_, applicatio0_.data as data3_0_, applicatio0_.financial_provider as financia4_0_, applicatio0_.lead_id as lead_id5_0_, applicatio0_.lender_state as lender_s6_0_, applicatio0_.lender_sub_state as lender_s7_0_, applicatio0_.merchant as merchant8_0_, applicatio0_.product_type as product_9_0_, applicatio0_.state as state10_0_, applicatio0_.updated_at as updated11_0_, applicatio0_.user_id as user_id12_0_, applicatio0_.workflow_id as workflo13_0_ from application applicatio0_ where applicatio0_.user_id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-54995 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "0380d8e67b84956d", "parentSpanId": "426e9dcdccf50e98", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047188138000, "endTimeUnixNano": 1748774047188998700, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 54995}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-54995 - GET /fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "59b7bdeeacc699ec", "parentSpanId": "58eb892bc7147470", "traceState": "", "name": "GET /fintech-citadel/2/application/getActiveApplications", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774046899000000, "endTimeUnixNano": 1748774046903828500, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "url.query", "value": {"stringValue": "sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 31800}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/getActiveApplications"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 34224}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-31800"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "c6d02ca7a3f822b9", "parentSpanId": "59b7bdeeacc699ec", "traceState": "", "name": "SELECT Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046900564200, "endTimeUnixNano": 1748774046901827300, "attributes": [{"key": "thread.id", "value": {"intValue": 31800}}, {"key": "thread.name", "value": {"stringValue": "dw-31800 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "43525ee67347c534", "parentSpanId": "59b7bdeeacc699ec", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774046901930500, "endTimeUnixNano": 1748774046903475700, "attributes": [{"key": "thread.id", "value": {"intValue": 31800}}, {"key": "thread.name", "value": {"stringValue": "dw-31800 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "06436f638cb4061c", "parentSpanId": "c6d02ca7a3f822b9", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774046900631800, "endTimeUnixNano": 1748774046901809000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 31800}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* select generatedAlias0 from Application as generatedAlias0 where ( generatedAlias0.userId=:param0 ) and ( generatedAlias0.productType=:param1 ) */ select applicatio0_.id as id1_0_, applicatio0_.created_at as created_2_0_, applicatio0_.data as data3_0_, applicatio0_.financial_provider as financia4_0_, applicatio0_.lead_id as lead_id5_0_, applicatio0_.lender_state as lender_s6_0_, applicatio0_.lender_sub_state as lender_s7_0_, applicatio0_.merchant as merchant8_0_, applicatio0_.product_type as product_9_0_, applicatio0_.state as state10_0_, applicatio0_.updated_at as updated11_0_, applicatio0_.user_id as user_id12_0_, applicatio0_.workflow_id as workflo13_0_ from application applicatio0_ where applicatio0_.user_id=? and applicatio0_.product_type=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-31800 - GET /fintech-citadel/2/application/getActiveApplications?sm_user_id=SMA98A5554C8D1A456C905B6222875AE5EF&product_type=LEAD"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "faa80c97e9ef5a3f", "parentSpanId": "240f32b4c8178510", "traceState": "", "name": "POST /fintech-citadel/2/application/create", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047087000000, "endTimeUnixNano": 1748774047099866600, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/create"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 22263}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/create"}}, {"key": "http.request.method", "value": {"stringValue": "POST"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 60588}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-22263"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "3c45b1d13e5d6660", "parentSpanId": "09f5f72e13d0c4be", "traceState": "", "name": "PUT /fintech-citadel/2/application/{applicationId}/update-process-id", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047105000000, "endTimeUnixNano": 1748774047116259300, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 22245}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}/update-process-id"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 60588}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-22245"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "e9d58794be75485b", "parentSpanId": "f9109952c8a27a3f", "traceState": "", "name": "PUT /fintech-citadel/2/application/{applicationId}/state-change", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047117000000, "endTimeUnixNano": 1748774047128608300, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 22263}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}/state-change"}}, {"key": "http.request.method", "value": {"stringValue": "PUT"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 60588}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-22263"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "07697f041413a50f", "parentSpanId": "4eed09c4395307c4", "traceState": "", "name": "GET /fintech-citadel/2/application/{applicationId}", "kind": "SPAN_KIND_SERVER", "startTimeUnixNano": 1748774047399000000, "endTimeUnixNano": 1748774047402185700, "attributes": [{"key": "url.path", "value": {"stringValue": "/fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}, {"key": "network.peer.address", "value": {"stringValue": "*************"}}, {"key": "server.address", "value": {"stringValue": "citadel-service.sm-citadel-prod.fkcloud.in"}}, {"key": "client.address", "value": {"stringValue": "*************"}}, {"key": "thread.id", "value": {"intValue": 22245}}, {"key": "http.route", "value": {"stringValue": "/fintech-citadel/2/application/{applicationId}"}}, {"key": "http.request.method", "value": {"stringValue": "GET"}}, {"key": "http.response.status_code", "value": {"intValue": 200}}, {"key": "network.peer.port", "value": {"intValue": 45748}}, {"key": "user_agent.original", "value": {"stringValue": "Jersey/2.25.1 (Apache HttpClient 4.5.11)"}}, {"key": "network.protocol.version", "value": {"stringValue": "1.1"}}, {"key": "url.scheme", "value": {"stringValue": "http"}}, {"key": "thread.name", "value": {"stringValue": "dw-22245"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "37c8d9e3ab53c2d7", "parentSpanId": "faa80c97e9ef5a3f", "traceState": "", "name": "Session.saveOrUpdate com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047088316200, "endTimeUnixNano": 1748774047089161500, "attributes": [{"key": "thread.id", "value": {"intValue": 22263}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f565b86f5bdbbfce", "parentSpanId": "faa80c97e9ef5a3f", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047089300000, "endTimeUnixNano": 1748774047099546000, "attributes": [{"key": "thread.id", "value": {"intValue": 22263}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "ad2df71eb7e5f22b", "parentSpanId": "3c45b1d13e5d6660", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047105944300, "endTimeUnixNano": 1748774047106797800, "attributes": [{"key": "thread.id", "value": {"intValue": 22245}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "4d660009429fc055", "parentSpanId": "3c45b1d13e5d6660", "traceState": "", "name": "Session.saveOrUpdate com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047106803700, "endTimeUnixNano": 1748774047106805800, "attributes": [{"key": "thread.id", "value": {"intValue": 22245}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "77fe66337342a35c", "parentSpanId": "3c45b1d13e5d6660", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047106923500, "endTimeUnixNano": 1748774047115965700, "attributes": [{"key": "thread.id", "value": {"intValue": 22245}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "fd5483d6e2350af2", "parentSpanId": "e9d58794be75485b", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047117823500, "endTimeUnixNano": 1748774047118616000, "attributes": [{"key": "thread.id", "value": {"intValue": 22263}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "1f0bad7514a89b79", "parentSpanId": "e9d58794be75485b", "traceState": "", "name": "Session.saveOrUpdate com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047118618400, "endTimeUnixNano": 1748774047118620400, "attributes": [{"key": "thread.id", "value": {"intValue": 22263}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "dcdc60e737b7bfc0", "parentSpanId": "e9d58794be75485b", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047118741000, "endTimeUnixNano": 1748774047128332800, "attributes": [{"key": "thread.id", "value": {"intValue": 22263}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "0df79e7b591489aa", "parentSpanId": "07697f041413a50f", "traceState": "", "name": "Session.get com.flipkart.fintech.citadel.service.models.db.Application", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047399929600, "endTimeUnixNano": 1748774047401002200, "attributes": [{"key": "thread.id", "value": {"intValue": 22245}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - GET /fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "91b41dca9c89613d", "parentSpanId": "07697f041413a50f", "traceState": "", "name": "Transaction.commit", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": 1748774047401168100, "endTimeUnixNano": 1748774047401806600, "attributes": [{"key": "thread.id", "value": {"intValue": 22245}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - GET /fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "827d5ba79038d954", "parentSpanId": "37c8d9e3ab53c2d7", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047088367400, "endTimeUnixNano": 1748774047089139200, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22263}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* get current state com.flipkart.fintech.citadel.service.models.db.Application */ select applicatio_.id, applicatio_.created_at as created_2_0_, applicatio_.data as data3_0_, applicatio_.financial_provider as financia4_0_, applicatio_.lead_id as lead_id5_0_, applicatio_.lender_state as lender_s6_0_, applicatio_.lender_sub_state as lender_s7_0_, applicatio_.merchant as merchant8_0_, applicatio_.product_type as product_9_0_, applicatio_.state as state10_0_, applicatio_.updated_at as updated11_0_, applicatio_.user_id as user_id12_0_, applicatio_.workflow_id as workflo13_0_ from application applicatio_ where applicatio_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "7ec91691665a7ea9", "parentSpanId": "f565b86f5bdbbfce", "traceState": "", "name": "INSERT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047089439500, "endTimeUnixNano": 1748774047090296000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22263}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* insert com.flipkart.fintech.citadel.service.models.db.Application */ insert into application (created_at, data, financial_provider, lead_id, lender_state, lender_sub_state, merchant, product_type, state, updated_at, user_id, workflow_id, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"}}, {"key": "db.operation", "value": {"stringValue": "INSERT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - POST /fintech-citadel/2/application/create"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "f85165a5c4fa95cc", "parentSpanId": "ad2df71eb7e5f22b", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047105979100, "endTimeUnixNano": 1748774047106736600, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22245}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "7b1a62822f3c728a", "parentSpanId": "77fe66337342a35c", "traceState": "", "name": "UPDATE sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047107012000, "endTimeUnixNano": 1748774047107820800, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22245}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* update com.flipkart.fintech.citadel.service.models.db.Application */ update application set created_at=?, data=?, financial_provider=?, lead_id=?, lender_state=?, lender_sub_state=?, merchant=?, product_type=?, state=?, updated_at=?, user_id=?, workflow_id=? where id=?"}}, {"key": "db.operation", "value": {"stringValue": "UPDATE"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/update-process-id"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "d273686dd9796eed", "parentSpanId": "fd5483d6e2350af2", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047117853000, "endTimeUnixNano": 1748774047118572800, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22263}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "43b19bf8bb79f12e", "parentSpanId": "dcdc60e737b7bfc0", "traceState": "", "name": "UPDATE sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047118826200, "endTimeUnixNano": 1748774047119664400, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22263}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "/* update com.flipkart.fintech.citadel.service.models.db.Application */ update application set created_at=?, data=?, financial_provider=?, lead_id=?, lender_state=?, lender_sub_state=?, merchant=?, product_type=?, state=?, updated_at=?, user_id=?, workflow_id=? where id=?"}}, {"key": "db.operation", "value": {"stringValue": "UPDATE"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22263 - PUT /fintech-citadel/2/application/APP2506011604072724117958958792624919457/state-change"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}, {"traceId": "8071eb8c7006f9686be727a6a33fc1f2", "spanId": "294a421815c3dec6", "parentSpanId": "0df79e7b591489aa", "traceState": "", "name": "SELECT sm_winterfell_prod.application", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": 1748774047399968500, "endTimeUnixNano": 1748774047400953000, "attributes": [{"key": "db.user", "value": {"stringValue": "sm_admin"}}, {"key": "db.connection_string", "value": {"stringValue": "mysql://************:3306"}}, {"key": "server.port", "value": {"intValue": 3306}}, {"key": "server.address", "value": {"stringValue": "************"}}, {"key": "thread.id", "value": {"intValue": 22245}}, {"key": "db.system", "value": {"stringValue": "mysql"}}, {"key": "db.statement", "value": {"stringValue": "select applicatio0_.id as id1_0_0_, applicatio0_.created_at as created_2_0_0_, applicatio0_.data as data3_0_0_, applicatio0_.financial_provider as financia4_0_0_, applicatio0_.lead_id as lead_id5_0_0_, applicatio0_.lender_state as lender_s6_0_0_, applicatio0_.lender_sub_state as lender_s7_0_0_, applicatio0_.merchant as merchant8_0_0_, applicatio0_.product_type as product_9_0_0_, applicatio0_.state as state10_0_0_, applicatio0_.updated_at as updated11_0_0_, applicatio0_.user_id as user_id12_0_0_, applicatio0_.workflow_id as workflo13_0_0_ from application applicatio0_ where applicatio0_.id=?"}}, {"key": "db.operation", "value": {"stringValue": "SELECT"}}, {"key": "db.sql.table", "value": {"stringValue": "application"}}, {"key": "db.name", "value": {"stringValue": "sm_winterfell_prod"}}, {"key": "thread.name", "value": {"stringValue": "dw-22245 - GET /fintech-citadel/2/application/APP2506011604072724117958958792624919457"}}], "droppedAttributesCount": 0, "droppedEventsCount": 0, "droppedLinksCount": 0, "status": {"code": 0, "message": ""}}], "instrumentationLibrary": {"name": "io.opentelemetry.jetty-8.0", "version": "2.13.3-alpha"}}]}]}